"""
测试增强模块加载修复
验证配置传递问题是否已解决
"""

import torch
import sys
import os
import yaml

# 添加路径以便导入模块
sys.path.append('pulsar_trainer/models')
sys.path.append('pulsar_trainer/utils')

from coatnet import CoAtNet, create_coatnet_from_config
from config import load_config_with_classes


class MockModelConfig:
    """模拟ModelConfig对象"""
    def __init__(self):
        self.image_size = [64, 64]
        self.in_channels = 1
        self.num_blocks = [2, 2, 3, 5, 2]
        self.channels = [64, 96, 192, 384, 768]
        self.num_classes = 2
        self.block_types = ['C', 'C', 'T', 'T']
    
    def validate(self):
        """配置验证"""
        pass


def test_enhancement_config_loading():
    """测试增强配置加载"""
    print("="*60)
    print("Testing Enhancement Module Configuration Loading")
    print("="*60)
    
    # 测试1: 从YAML配置文件加载
    print("\n1. Testing YAML config loading...")
    try:
        with open('pulsar_trainer/config/coatnet_config.yaml', 'r', encoding='utf-8') as f:
            config_dict = yaml.safe_load(f)
        
        enhancement_config = config_dict.get('enhancement_modules', None)
        
        if enhancement_config:
            print("✓ Enhancement modules found in YAML config")
            print(f"  - FPP enabled: {enhancement_config.get('fpp', {}).get('enabled', False)}")
            print(f"  - TPP enabled: {enhancement_config.get('tpp', {}).get('enabled', False)}")
        else:
            print("✗ Enhancement modules not found in YAML config")
            return False
            
    except Exception as e:
        print(f"✗ YAML config loading failed: {e}")
        return False
    
    # 测试2: 使用新的函数签名创建模型
    print("\n2. Testing model creation with new signature...")
    try:
        model_config = MockModelConfig()
        
        # 不带增强模块
        model_base = create_coatnet_from_config(model_config, None)
        if model_base.enhancement_manager is None:
            print("✓ Base model created without enhancement manager")
        else:
            print("✗ Base model should not have enhancement manager")
            return False
        
        # 带增强模块
        model_enhanced = create_coatnet_from_config(model_config, enhancement_config)
        if model_enhanced.enhancement_manager is not None:
            print("✓ Enhanced model created with enhancement manager")
            
            # 获取增强模块信息
            stats = model_enhanced.get_enhancement_stats()
            if stats:
                print(f"  - Enhancement manager stats: {stats['manager']}")
            else:
                print("  - Enhancement manager created but no stats available")
        else:
            print("✗ Enhanced model should have enhancement manager")
            return False
            
    except Exception as e:
        print(f"✗ Model creation failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    # 测试3: 验证前向传播
    print("\n3. Testing forward pass...")
    try:
        x = torch.randn(2, 1, 64, 64)
        
        with torch.no_grad():
            output_base = model_base(x)
            output_enhanced = model_enhanced(x)
        
        print(f"✓ Base model output shape: {output_base.shape}")
        print(f"✓ Enhanced model output shape: {output_enhanced.shape}")
        
        if output_base.shape == output_enhanced.shape:
            print("✓ Output shapes match")
        else:
            print("✗ Output shapes don't match")
            return False
            
    except Exception as e:
        print(f"✗ Forward pass failed: {e}")
        return False
    
    # 测试4: 验证增强损失计算
    print("\n4. Testing enhanced loss computation...")
    try:
        predictions = torch.randn(2, 2)
        targets = torch.randint(0, 2, (2,))
        
        # 基础模型应该返回None
        base_loss = model_base.compute_enhanced_loss(predictions, targets)
        if base_loss is None:
            print("✓ Base model returns None for enhanced loss")
        else:
            print("✗ Base model should return None for enhanced loss")
            return False
        
        # 增强模型应该返回损失值
        enhanced_loss = model_enhanced.compute_enhanced_loss(predictions, targets)
        if enhanced_loss is not None:
            print(f"✓ Enhanced model returns loss: {enhanced_loss.item():.4f}")
            
            # 获取损失组件
            loss_components = model_enhanced.get_loss_components(predictions, targets)
            if loss_components:
                print(f"  - Loss components: {list(loss_components.keys())}")
        else:
            print("⚠️ Enhanced model returns None for enhanced loss (may be expected)")
            
    except Exception as e:
        print(f"✗ Enhanced loss computation failed: {e}")
        return False
    
    # 测试5: 验证模态切换
    print("\n5. Testing modality switching...")
    try:
        # 测试不同模态
        modalities = ['FPP', 'TPP', 'AUTO']
        
        for modality in modalities:
            model_enhanced.set_enhancement_modality(modality)
            
            with torch.no_grad():
                output = model_enhanced(x)
            
            print(f"✓ {modality} modality works, output shape: {output.shape}")
            
    except Exception as e:
        print(f"✗ Modality switching failed: {e}")
        return False
    
    print("\n" + "="*60)
    print("✅ All Enhancement Loading Tests Passed!")
    print("Enhancement modules are now correctly loaded and functional!")
    print("="*60)
    
    return True


def test_parameter_comparison():
    """测试参数数量对比"""
    print("\nTesting parameter count comparison...")
    
    model_config = MockModelConfig()
    
    # 创建基础模型和增强模型
    model_base = create_coatnet_from_config(model_config, None)
    
    enhancement_config = {
        'fpp': {'enabled': True},
        'tpp': {'enabled': True}
    }
    model_enhanced = create_coatnet_from_config(model_config, enhancement_config)
    
    # 计算参数数量
    base_params = sum(p.numel() for p in model_base.parameters() if p.requires_grad)
    enhanced_params = sum(p.numel() for p in model_enhanced.parameters() if p.requires_grad)
    
    overhead = enhanced_params - base_params
    relative_overhead = overhead / base_params * 100
    
    print(f"Base model parameters: {base_params:,}")
    print(f"Enhanced model parameters: {enhanced_params:,}")
    print(f"Enhancement overhead: {overhead:,} parameters ({relative_overhead:.2f}%)")
    
    return True


if __name__ == '__main__':
    print("Enhancement Module Loading Test")
    print("Testing the fix for configuration passing issue")
    
    success = test_enhancement_config_loading()
    
    if success:
        test_parameter_comparison()
        print("\n🎉 Enhancement module loading fix successful!")
        print("Ready to proceed with training!")
    else:
        print("\n❌ Enhancement module loading fix failed!")
        print("Please check the implementation.")
