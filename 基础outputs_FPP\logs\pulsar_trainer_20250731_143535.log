2025-07-31 14:35:35 - pulsar_trainer - INFO - <PERSON><PERSON> initialized. Log file: D:\pulsarSuanfa\outputs_FPP\logs\pulsar_trainer_20250731_143535.log
2025-07-31 14:35:35 - pulsar_trainer - INFO - 随机种子设置为: 42
2025-07-31 14:35:36 - pulsar_trainer - INFO - 使用配置文件参数创建模型
2025-07-31 14:35:36 - pulsar_trainer - INFO - 模型配置 - num_blocks: [2, 2, 6, 8, 2]
2025-07-31 14:35:36 - pulsar_trainer - INFO - 模型配置 - channels: [96, 128, 256, 512, 1024]
2025-07-31 14:35:36 - pulsar_trainer - INFO - 模型配置 - in_channels: 1
2025-07-31 14:35:36 - pulsar_trainer - INFO - 模型架构详情:
2025-07-31 14:35:36 - pulsar_trainer - INFO -   总参数数量: 38,726,128
2025-07-31 14:35:36 - pulsar_trainer - INFO -   可训练参数: 38,726,128
2025-07-31 14:35:36 - pulsar_trainer - INFO -   模型大小: 147.73 MB
2025-07-31 14:35:36 - pulsar_trainer - INFO -   块类型配置: ['C', 'C', 'T', 'T']
2025-07-31 14:35:36 - pulsar_trainer - INFO -   各阶段块数: [2, 2, 6, 8, 2]
2025-07-31 14:35:36 - pulsar_trainer - INFO -   各阶段通道数: [96, 128, 256, 512, 1024]
2025-07-31 14:35:36 - pulsar_trainer - INFO - Using standard cross entropy loss
2025-07-31 14:35:36 - pulsar_trainer - INFO - 🖥️ 使用GPU: NVIDIA GeForce RTX 4050 Laptop GPU
2025-07-31 14:35:36 - pulsar_trainer - INFO - 💾 GPU内存: 6.4GB
2025-07-31 14:35:36 - pulsar_trainer - INFO - 📋 模型参数总数: 38,726,128
2025-07-31 14:35:36 - pulsar_trainer - INFO - 🏗️ 模型架构: CoAtNet
2025-07-31 14:35:36 - pulsar_trainer - INFO - 📚 数据集大小:
2025-07-31 14:35:36 - pulsar_trainer - INFO -   - 训练集: 1674
2025-07-31 14:35:36 - pulsar_trainer - INFO -   - 验证集: 360
2025-07-31 14:35:36 - pulsar_trainer - INFO -   - 测试集: 358
2025-07-31 14:35:36 - pulsar_trainer - INFO -   - 总计: 2392
2025-07-31 14:35:36 - pulsar_trainer - INFO - ============================================================
2025-07-31 14:35:36 - pulsar_trainer - INFO - 🚀 开始脉冲星分类训练
2025-07-31 14:35:36 - pulsar_trainer - INFO - 📊 模态: FPP
2025-07-31 14:35:36 - pulsar_trainer - INFO - 🧠 模型: coatnet
2025-07-31 14:35:36 - pulsar_trainer - INFO - 📦 批次大小: 32
2025-07-31 14:35:36 - pulsar_trainer - INFO - 🎯 学习率: 0.001
2025-07-31 14:35:36 - pulsar_trainer - INFO - 🔄 训练轮数: 100
2025-07-31 14:35:36 - pulsar_trainer - INFO - ============================================================
2025-07-31 14:36:23 - pulsar_trainer - INFO - Epoch   1: Train Loss=3.2650, Val Loss=2.2897, Train Acc=80.59%, Val Acc=86.11%
2025-07-31 14:36:23 - pulsar_trainer - INFO - Validation F1: 86.0763, Precision: 86.4759, Recall: 86.1111
2025-07-31 14:36:23 - pulsar_trainer - INFO - 模型已保存: D:\pulsarSuanfa\outputs_FPP\models\best_model.pth
2025-07-31 14:36:28 - pulsar_trainer - INFO - Epoch   2: Train Loss=0.6208, Val Loss=0.3115, Train Acc=89.25%, Val Acc=86.39%
2025-07-31 14:36:28 - pulsar_trainer - INFO - Validation F1: 86.2872, Precision: 87.5012, Recall: 86.3889
2025-07-31 14:36:28 - pulsar_trainer - INFO - 模型已保存: D:\pulsarSuanfa\outputs_FPP\models\best_model.pth
2025-07-31 14:36:32 - pulsar_trainer - INFO - Epoch   3: Train Loss=0.2360, Val Loss=0.2097, Train Acc=91.64%, Val Acc=90.28%
2025-07-31 14:36:32 - pulsar_trainer - INFO - Validation F1: 90.2143, Precision: 91.3511, Recall: 90.2778
2025-07-31 14:36:33 - pulsar_trainer - INFO - 模型已保存: D:\pulsarSuanfa\outputs_FPP\models\best_model.pth
2025-07-31 14:36:37 - pulsar_trainer - INFO - Epoch   4: Train Loss=0.1518, Val Loss=0.3325, Train Acc=94.03%, Val Acc=85.28%
2025-07-31 14:36:37 - pulsar_trainer - INFO - Validation F1: 84.9999, Precision: 88.1013, Recall: 85.2778
2025-07-31 14:36:41 - pulsar_trainer - INFO - Epoch   5: Train Loss=0.0855, Val Loss=0.1598, Train Acc=96.89%, Val Acc=95.28%
2025-07-31 14:36:41 - pulsar_trainer - INFO - Validation F1: 95.2769, Precision: 95.3127, Recall: 95.2778
2025-07-31 14:36:41 - pulsar_trainer - INFO - 模型已保存: D:\pulsarSuanfa\outputs_FPP\models\best_model.pth
2025-07-31 14:36:46 - pulsar_trainer - INFO - Epoch   6: Train Loss=0.0882, Val Loss=0.2698, Train Acc=96.36%, Val Acc=87.22%
2025-07-31 14:36:46 - pulsar_trainer - INFO - Validation F1: 87.0284, Precision: 89.5877, Recall: 87.2222
2025-07-31 14:36:50 - pulsar_trainer - INFO - Epoch   7: Train Loss=0.1365, Val Loss=0.1682, Train Acc=96.24%, Val Acc=97.22%
2025-07-31 14:36:50 - pulsar_trainer - INFO - Validation F1: 97.2221, Precision: 97.2281, Recall: 97.2222
2025-07-31 14:36:50 - pulsar_trainer - INFO - 模型已保存: D:\pulsarSuanfa\outputs_FPP\models\best_model.pth
2025-07-31 14:36:55 - pulsar_trainer - INFO - Epoch   8: Train Loss=0.1008, Val Loss=2.3251, Train Acc=96.59%, Val Acc=52.50%
2025-07-31 14:36:55 - pulsar_trainer - INFO - Validation F1: 38.6602, Precision: 75.6410, Recall: 52.5000
2025-07-31 14:36:59 - pulsar_trainer - INFO - Epoch   9: Train Loss=0.0672, Val Loss=0.1086, Train Acc=97.91%, Val Acc=96.67%
2025-07-31 14:36:59 - pulsar_trainer - INFO - Validation F1: 96.6657, Precision: 96.7186, Recall: 96.6667
2025-07-31 14:37:03 - pulsar_trainer - INFO - Epoch  10: Train Loss=0.0171, Val Loss=0.1617, Train Acc=99.52%, Val Acc=96.94%
2025-07-31 14:37:03 - pulsar_trainer - INFO - Validation F1: 96.9442, Precision: 96.9575, Recall: 96.9444
2025-07-31 14:37:07 - pulsar_trainer - INFO - Epoch  11: Train Loss=0.0195, Val Loss=0.1702, Train Acc=99.22%, Val Acc=96.94%
2025-07-31 14:37:07 - pulsar_trainer - INFO - Validation F1: 96.9439, Precision: 96.9807, Recall: 96.9444
2025-07-31 14:37:12 - pulsar_trainer - INFO - Epoch  12: Train Loss=0.0502, Val Loss=0.1076, Train Acc=99.04%, Val Acc=97.22%
2025-07-31 14:37:12 - pulsar_trainer - INFO - Validation F1: 97.2221, Precision: 97.2281, Recall: 97.2222
2025-07-31 14:37:16 - pulsar_trainer - INFO - Epoch  13: Train Loss=0.0133, Val Loss=0.1553, Train Acc=99.46%, Val Acc=97.22%
2025-07-31 14:37:16 - pulsar_trainer - INFO - Validation F1: 97.2221, Precision: 97.2281, Recall: 97.2222
2025-07-31 14:37:20 - pulsar_trainer - INFO - Epoch  14: Train Loss=0.0137, Val Loss=0.1320, Train Acc=99.46%, Val Acc=96.67%
2025-07-31 14:37:20 - pulsar_trainer - INFO - Validation F1: 96.6667, Precision: 96.6667, Recall: 96.6667
2025-07-31 14:37:24 - pulsar_trainer - INFO - Epoch  15: Train Loss=0.0140, Val Loss=0.1670, Train Acc=99.46%, Val Acc=96.11%
2025-07-31 14:37:24 - pulsar_trainer - INFO - Validation F1: 96.1110, Precision: 96.1168, Recall: 96.1111
2025-07-31 14:37:29 - pulsar_trainer - INFO - Epoch  16: Train Loss=0.0293, Val Loss=0.1716, Train Acc=99.16%, Val Acc=96.39%
2025-07-31 14:37:29 - pulsar_trainer - INFO - Validation F1: 96.3886, Precision: 96.4018, Recall: 96.3889
2025-07-31 14:37:33 - pulsar_trainer - INFO - Epoch  17: Train Loss=0.0417, Val Loss=0.1693, Train Acc=98.45%, Val Acc=95.83%
2025-07-31 14:37:33 - pulsar_trainer - INFO - Validation F1: 95.8294, Precision: 96.0051, Recall: 95.8333
2025-07-31 14:37:37 - pulsar_trainer - INFO - Epoch  18: Train Loss=0.0106, Val Loss=0.0897, Train Acc=99.52%, Val Acc=97.78%
2025-07-31 14:37:37 - pulsar_trainer - INFO - Validation F1: 97.7772, Precision: 97.8309, Recall: 97.7778
2025-07-31 14:37:38 - pulsar_trainer - INFO - 模型已保存: D:\pulsarSuanfa\outputs_FPP\models\best_model.pth
2025-07-31 14:37:42 - pulsar_trainer - INFO - Epoch  19: Train Loss=0.0035, Val Loss=0.1051, Train Acc=99.82%, Val Acc=97.50%
2025-07-31 14:37:42 - pulsar_trainer - INFO - Validation F1: 97.4995, Precision: 97.5367, Recall: 97.5000
2025-07-31 14:37:46 - pulsar_trainer - INFO - Epoch  20: Train Loss=0.0094, Val Loss=0.1012, Train Acc=99.76%, Val Acc=96.39%
2025-07-31 14:37:46 - pulsar_trainer - INFO - Validation F1: 96.3882, Precision: 96.4247, Recall: 96.3889
2025-07-31 14:37:50 - pulsar_trainer - INFO - Epoch  21: Train Loss=0.0047, Val Loss=0.0802, Train Acc=99.82%, Val Acc=97.22%
2025-07-31 14:37:50 - pulsar_trainer - INFO - Validation F1: 97.2221, Precision: 97.2281, Recall: 97.2222
2025-07-31 14:37:55 - pulsar_trainer - INFO - Epoch  22: Train Loss=0.0059, Val Loss=0.1726, Train Acc=99.70%, Val Acc=97.78%
2025-07-31 14:37:55 - pulsar_trainer - INFO - Validation F1: 97.7777, Precision: 97.7837, Recall: 97.7778
2025-07-31 14:37:59 - pulsar_trainer - INFO - Epoch  23: Train Loss=0.0213, Val Loss=0.1037, Train Acc=99.46%, Val Acc=96.39%
2025-07-31 14:37:59 - pulsar_trainer - INFO - Validation F1: 96.3875, Precision: 96.4592, Recall: 96.3889
2025-07-31 14:38:03 - pulsar_trainer - INFO - Epoch  24: Train Loss=0.0092, Val Loss=0.1232, Train Acc=99.76%, Val Acc=97.22%
2025-07-31 14:38:03 - pulsar_trainer - INFO - Validation F1: 97.2219, Precision: 97.2456, Recall: 97.2222
2025-07-31 14:38:07 - pulsar_trainer - INFO - Epoch  25: Train Loss=0.0127, Val Loss=0.1320, Train Acc=99.52%, Val Acc=96.94%
2025-07-31 14:38:07 - pulsar_trainer - INFO - Validation F1: 96.9442, Precision: 96.9575, Recall: 96.9444
2025-07-31 14:38:12 - pulsar_trainer - INFO - Epoch  26: Train Loss=0.0029, Val Loss=0.1565, Train Acc=99.94%, Val Acc=96.94%
2025-07-31 14:38:12 - pulsar_trainer - INFO - Validation F1: 96.9442, Precision: 96.9575, Recall: 96.9444
2025-07-31 14:38:16 - pulsar_trainer - INFO - Epoch  27: Train Loss=0.0019, Val Loss=0.1760, Train Acc=99.94%, Val Acc=97.22%
2025-07-31 14:38:16 - pulsar_trainer - INFO - Validation F1: 97.2219, Precision: 97.2456, Recall: 97.2222
2025-07-31 14:38:20 - pulsar_trainer - INFO - Epoch  28: Train Loss=0.0208, Val Loss=0.1045, Train Acc=99.28%, Val Acc=95.83%
2025-07-31 14:38:20 - pulsar_trainer - INFO - Validation F1: 95.8325, Precision: 95.8687, Recall: 95.8333
2025-07-31 14:38:25 - pulsar_trainer - INFO - Epoch  29: Train Loss=0.0119, Val Loss=0.1208, Train Acc=99.52%, Val Acc=96.39%
2025-07-31 14:38:25 - pulsar_trainer - INFO - Validation F1: 96.3882, Precision: 96.4247, Recall: 96.3889
2025-07-31 14:38:29 - pulsar_trainer - INFO - Epoch  30: Train Loss=0.0013, Val Loss=0.1389, Train Acc=100.00%, Val Acc=96.94%
2025-07-31 14:38:29 - pulsar_trainer - INFO - Validation F1: 96.9444, Precision: 96.9459, Recall: 96.9444
2025-07-31 14:38:33 - pulsar_trainer - INFO - Epoch  31: Train Loss=0.0004, Val Loss=0.1568, Train Acc=100.00%, Val Acc=96.39%
2025-07-31 14:38:33 - pulsar_trainer - INFO - Validation F1: 96.3886, Precision: 96.4018, Recall: 96.3889
2025-07-31 14:38:38 - pulsar_trainer - INFO - Epoch  32: Train Loss=0.0004, Val Loss=0.1595, Train Acc=100.00%, Val Acc=96.94%
2025-07-31 14:38:38 - pulsar_trainer - INFO - Validation F1: 96.9444, Precision: 96.9459, Recall: 96.9444
2025-07-31 14:38:42 - pulsar_trainer - INFO - Epoch  33: Train Loss=0.0000, Val Loss=0.1609, Train Acc=100.00%, Val Acc=96.94%
2025-07-31 14:38:42 - pulsar_trainer - INFO - Validation F1: 96.9444, Precision: 96.9459, Recall: 96.9444
2025-07-31 14:38:42 - pulsar_trainer - INFO - 早停触发，在第 33 轮停止训练
2025-07-31 14:38:42 - pulsar_trainer - INFO - ============================================================
2025-07-31 14:38:42 - pulsar_trainer - INFO - ✅ 训练完成
2025-07-31 14:38:42 - pulsar_trainer - INFO - 🏆 最佳验证准确率: 97.7778
2025-07-31 14:38:42 - pulsar_trainer - INFO - ⏱️ 总训练时间: 186.04秒
2025-07-31 14:38:42 - pulsar_trainer - INFO - ============================================================
2025-07-31 14:38:42 - pulsar_trainer - INFO - 🔍 开始最终评估...
2025-07-31 14:38:42 - pulsar_trainer - INFO - 模型已加载: D:\pulsarSuanfa\outputs_FPP\models\best_model.pth
2025-07-31 14:39:05 - pulsar_trainer.utils.comprehensive_misclassification_analysis - INFO - 开始全面误分类分析...
2025-07-31 14:39:05 - pulsar_trainer.utils.comprehensive_misclassification_analysis - INFO - 综合误分类分析报告已生成: D:\pulsarSuanfa\outputs_FPP\results\comprehensive_misclassification_analysis\comprehensive_misclassification_report.txt
2025-07-31 14:39:05 - pulsar_trainer.utils.comprehensive_misclassification_analysis - INFO - 全面误分类分析完成。报告保存至: D:\pulsarSuanfa\outputs_FPP\results\comprehensive_misclassification_analysis
2025-07-31 14:39:05 - pulsar_trainer - INFO - 📊 评估结果:
2025-07-31 14:39:05 - pulsar_trainer - INFO -   - accuracy: 0.9804
2025-07-31 14:39:05 - pulsar_trainer - INFO -   - precision: 0.9886
2025-07-31 14:39:05 - pulsar_trainer - INFO -   - recall: 0.9721
2025-07-31 14:39:05 - pulsar_trainer - INFO -   - specificity: 0.9888
2025-07-31 14:39:05 - pulsar_trainer - INFO -   - f1_score: 0.9803
2025-07-31 14:39:05 - pulsar_trainer - INFO -   - false_positive_rate: 0.0112
2025-07-31 14:39:08 - pulsar_trainer - INFO - 所有结果已保存到: D:\pulsarSuanfa\outputs_FPP\results
2025-07-31 14:39:08 - pulsar_trainer - INFO - 可视化图表已保存到: D:\pulsarSuanfa\outputs_FPP\plots
