"""
FPP Enhancement Module Unit Tests
测试FFTNetBlock2D和SimplifiedFreqFusion的功能
"""

import torch
import torch.nn as nn
import unittest
import sys
import os

# 添加路径以便导入模块
sys.path.append('pulsar_trainer/models')

from fpp_enhancement_module import (
    FFTNetBlock2D, 
    SimplifiedFreqFusion, 
    FPPEnhancementModule,
    create_fpp_enhancement,
    validate_fpp_config
)


class TestFFTNetBlock2D(unittest.TestCase):
    """FFTNetBlock2D模块测试"""
    
    def setUp(self):
        """测试初始化"""
        self.batch_size = 2
        self.channels = 96
        self.height = 16
        self.width = 16
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    def test_input_output_shape(self):
        """测试输入输出形状"""
        model = FFTNetBlock2D(dim=self.channels)
        x = torch.randn(self.batch_size, self.channels, self.height, self.width)
        
        with torch.no_grad():
            output = model(x)
        
        self.assertEqual(output.shape, x.shape, "FFT processing should preserve tensor shape")
        print(f"✓ FFTNetBlock2D shape test passed: {x.shape} -> {output.shape}")
    
    def test_frequency_processing(self):
        """测试频域处理功能"""
        model = FFTNetBlock2D(dim=self.channels)
        
        # 创建具有已知频域特征的测试信号
        x = torch.randn(self.batch_size, self.channels, self.height, self.width)
        
        with torch.no_grad():
            output = model(x)
        
        # 验证输出不是简单的恒等映射
        self.assertFalse(torch.allclose(x, output, atol=1e-6), "Output should be different from input")
        print("✓ FFTNetBlock2D frequency processing test passed")
    
    def test_numerical_stability(self):
        """测试数值稳定性"""
        model = FFTNetBlock2D(dim=self.channels)
        
        # 测试极值输入
        test_inputs = [
            torch.zeros(self.batch_size, self.channels, self.height, self.width),
            torch.ones(self.batch_size, self.channels, self.height, self.width) * 100,
            torch.ones(self.batch_size, self.channels, self.height, self.width) * 1e-6
        ]
        
        for i, x_test in enumerate(test_inputs):
            with torch.no_grad():
                output = model(x_test)
            
            self.assertFalse(torch.isnan(output).any(), f"Output contains NaN for test case {i}")
            self.assertFalse(torch.isinf(output).any(), f"Output contains Inf for test case {i}")
        
        print("✓ FFTNetBlock2D numerical stability test passed")
    
    def test_channel_mismatch_error(self):
        """测试通道数不匹配的错误处理"""
        model = FFTNetBlock2D(dim=self.channels)
        wrong_channels = self.channels + 32
        x = torch.randn(self.batch_size, wrong_channels, self.height, self.width)
        
        with self.assertRaises(AssertionError):
            model(x)
        
        print("✓ FFTNetBlock2D channel mismatch error handling test passed")


class TestSimplifiedFreqFusion(unittest.TestCase):
    """SimplifiedFreqFusion模块测试"""
    
    def setUp(self):
        """测试初始化"""
        self.batch_size = 2
        self.channels = 96
        self.height = 16
        self.width = 16
        self.compressed_channels = 24
    
    def test_feature_fusion(self):
        """测试特征融合功能"""
        model = SimplifiedFreqFusion(
            hr_channels=self.channels,
            lr_channels=self.channels,
            compressed_channels=self.compressed_channels,
            use_high_pass=True,
            use_low_pass=True
        )
        
        hr_feat = torch.randn(self.batch_size, self.channels, self.height, self.width)
        lr_feat = torch.randn(self.batch_size, self.channels, self.height, self.width)
        
        with torch.no_grad():
            output = model(hr_feat, lr_feat)
        
        self.assertEqual(output.shape, hr_feat.shape, "Feature fusion should preserve input shape")
        print(f"✓ SimplifiedFreqFusion feature fusion test passed: {hr_feat.shape} -> {output.shape}")
    
    def test_single_input_fusion(self):
        """测试单输入特征融合"""
        model = SimplifiedFreqFusion(
            hr_channels=self.channels,
            lr_channels=self.channels,
            compressed_channels=self.compressed_channels
        )
        
        x = torch.randn(self.batch_size, self.channels, self.height, self.width)
        
        with torch.no_grad():
            output = model(x)  # 只提供一个输入
        
        self.assertEqual(output.shape, x.shape, "Single input fusion should preserve shape")
        print("✓ SimplifiedFreqFusion single input test passed")
    
    def test_frequency_filtering_modes(self):
        """测试不同频域滤波模式"""
        configs = [
            {'use_high_pass': True, 'use_low_pass': True},
            {'use_high_pass': True, 'use_low_pass': False},
            {'use_high_pass': False, 'use_low_pass': True},
            {'use_high_pass': False, 'use_low_pass': False}
        ]
        
        x = torch.randn(self.batch_size, self.channels, self.height, self.width)
        
        for i, config in enumerate(configs):
            model = SimplifiedFreqFusion(
                hr_channels=self.channels,
                lr_channels=self.channels,
                compressed_channels=self.compressed_channels,
                **config
            )
            
            with torch.no_grad():
                output = model(x)
            
            self.assertEqual(output.shape, x.shape, f"Config {i} should preserve shape")
        
        print("✓ SimplifiedFreqFusion frequency filtering modes test passed")
    
    def test_residual_connection(self):
        """测试残差连接"""
        model = SimplifiedFreqFusion(
            hr_channels=self.channels,
            lr_channels=self.channels,
            compressed_channels=self.compressed_channels
        )
        
        x = torch.randn(self.batch_size, self.channels, self.height, self.width)
        
        with torch.no_grad():
            output = model(x)
        
        # 输出应该包含原始输入的信息（由于残差连接）
        # 但不应该完全相同（由于特征处理）
        self.assertFalse(torch.allclose(x, output, atol=1e-6), "Output should be enhanced, not identical")
        print("✓ SimplifiedFreqFusion residual connection test passed")


class TestFPPEnhancementModule(unittest.TestCase):
    """FPPEnhancementModule集成测试"""
    
    def setUp(self):
        """测试初始化"""
        self.batch_size = 2
        self.channels = 96
        self.height = 16
        self.width = 16
    
    def test_module_creation(self):
        """测试模块创建"""
        config = {
            'channels': self.channels,
            'fft_dim': self.channels,
            'freq_fusion_config': {
                'compressed_channels': 24,
                'use_high_pass': True,
                'use_low_pass': True
            },
            'enabled': True
        }
        
        module = create_fpp_enhancement(config)
        self.assertIsInstance(module, FPPEnhancementModule)
        print("✓ FPPEnhancementModule creation test passed")
    
    def test_modality_aware_activation(self):
        """测试模态感知激活"""
        config = {
            'channels': self.channels,
            'enabled': True
        }
        
        module = create_fpp_enhancement(config)
        x = torch.randn(self.batch_size, self.channels, self.height, self.width)
        
        # FPP模态应该激活
        with torch.no_grad():
            output_fpp = module(x, modality='FPP')
        self.assertFalse(torch.equal(x, output_fpp), "FPP modality should process input")
        
        # TPP模态应该直接返回
        with torch.no_grad():
            output_tpp = module(x, modality='TPP')
        self.assertTrue(torch.equal(x, output_tpp), "TPP modality should return input unchanged")
        
        print("✓ FPPEnhancementModule modality awareness test passed")
    
    def test_enable_disable_functionality(self):
        """测试启用/禁用功能"""
        config = {
            'channels': self.channels,
            'enabled': True
        }
        
        module = create_fpp_enhancement(config)
        x = torch.randn(self.batch_size, self.channels, self.height, self.width)
        
        # 启用状态
        with torch.no_grad():
            output_enabled = module(x, modality='FPP')
        
        # 禁用状态
        module.disable()
        with torch.no_grad():
            output_disabled = module(x, modality='FPP')
        
        self.assertTrue(torch.equal(x, output_disabled), "Disabled module should return input unchanged")
        self.assertFalse(torch.equal(output_enabled, output_disabled), "Enabled and disabled outputs should differ")
        
        print("✓ FPPEnhancementModule enable/disable test passed")
    
    def test_statistics_tracking(self):
        """测试统计信息跟踪"""
        config = {
            'channels': self.channels,
            'enabled': True
        }
        
        module = create_fpp_enhancement(config)
        x = torch.randn(self.batch_size, self.channels, self.height, self.width)
        
        # 初始统计
        initial_stats = module.get_stats()
        self.assertEqual(initial_stats['total_forwards'], 0)
        self.assertEqual(initial_stats['active_forwards'], 0)
        
        # 执行前向传播
        with torch.no_grad():
            module(x, modality='FPP')  # 应该激活
            module(x, modality='TPP')  # 不应该激活
        
        # 检查统计
        final_stats = module.get_stats()
        self.assertEqual(final_stats['total_forwards'], 2)
        self.assertEqual(final_stats['active_forwards'], 1)
        self.assertEqual(final_stats['activation_rate'], 0.5)
        
        print("✓ FPPEnhancementModule statistics tracking test passed")


class TestConfigValidation(unittest.TestCase):
    """配置验证测试"""
    
    def test_valid_config(self):
        """测试有效配置"""
        valid_config = {
            'channels': 96,
            'fft_dim': 96,
            'enabled': True
        }
        
        self.assertTrue(validate_fpp_config(valid_config))
        print("✓ Valid FPP config validation test passed")
    
    def test_invalid_config(self):
        """测试无效配置"""
        invalid_configs = [
            {},  # 缺少必需字段
            {'channels': 0},  # 无效通道数
            {'channels': -1},  # 负通道数
        ]
        
        for config in invalid_configs:
            self.assertFalse(validate_fpp_config(config))
        
        print("✓ Invalid FPP config validation test passed")


def run_fpp_tests():
    """运行所有FPP增强模块测试"""
    print("="*60)
    print("FPP Enhancement Module Unit Tests")
    print("="*60)
    
    # 创建测试套件
    test_classes = [
        TestFFTNetBlock2D,
        TestSimplifiedFreqFusion,
        TestFPPEnhancementModule,
        TestConfigValidation
    ]
    
    total_tests = 0
    passed_tests = 0
    
    for test_class in test_classes:
        print(f"\n--- Testing {test_class.__name__} ---")
        
        # 获取测试方法
        test_methods = [method for method in dir(test_class) if method.startswith('test_')]
        
        for method_name in test_methods:
            total_tests += 1
            try:
                # 创建测试实例并运行测试
                test_instance = test_class()
                test_instance.setUp()
                test_method = getattr(test_instance, method_name)
                test_method()
                passed_tests += 1
            except Exception as e:
                print(f"✗ {method_name} failed: {e}")
    
    print("\n" + "="*60)
    print(f"FPP Tests Results: {passed_tests}/{total_tests} tests passed")
    print("="*60)
    
    return passed_tests == total_tests


if __name__ == '__main__':
    success = run_fpp_tests()
    
    if success:
        print("\n🎉 All FPP Enhancement Module tests passed!")
    else:
        print("\n❌ Some FPP Enhancement Module tests failed!")
        exit(1)
