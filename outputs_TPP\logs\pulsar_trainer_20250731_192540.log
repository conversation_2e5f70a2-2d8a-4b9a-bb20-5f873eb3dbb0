2025-07-31 19:25:40 - pulsar_trainer - INFO - <PERSON><PERSON> initialized. Log file: D:\pulsarSuanfa\outputs_TPP\logs\pulsar_trainer_20250731_192540.log
2025-07-31 19:25:40 - pulsar_trainer - INFO - 随机种子设置为: 42
2025-07-31 19:25:40 - pulsar_trainer - INFO - 使用配置文件参数创建模型
2025-07-31 19:25:40 - pulsar_trainer - INFO - 增强模块配置已加载: FPP=True, TPP=True
2025-07-31 19:25:40 - pulsar_trainer - INFO - 模型配置 - num_blocks: [2, 2, 6, 8, 2]
2025-07-31 19:25:40 - pulsar_trainer - INFO - 模型配置 - channels: [96, 128, 256, 512, 1024]
2025-07-31 19:25:40 - pulsar_trainer - INFO - 模型配置 - in_channels: 1
2025-07-31 19:25:41 - pulsar_trainer - INFO - ✓ 增强模块已启用
2025-07-31 19:25:41 - pulsar_trainer - INFO -   - FPP模块可用: True
2025-07-31 19:25:41 - pulsar_trainer - INFO -   - TPP模块可用: True
2025-07-31 19:25:41 - pulsar_trainer - INFO -   - 当前模态: AUTO
2025-07-31 19:25:41 - pulsar_trainer - INFO - 模型架构详情:
2025-07-31 19:25:41 - pulsar_trainer - INFO -   总参数数量: 39,096,359
2025-07-31 19:25:41 - pulsar_trainer - INFO -   可训练参数: 39,096,359
2025-07-31 19:25:41 - pulsar_trainer - INFO -   模型大小: 149.14 MB
2025-07-31 19:25:41 - pulsar_trainer - INFO -   块类型配置: ['C', 'C', 'T', 'T']
2025-07-31 19:25:41 - pulsar_trainer - INFO -   各阶段块数: [2, 2, 6, 8, 2]
2025-07-31 19:25:41 - pulsar_trainer - INFO -   各阶段通道数: [96, 128, 256, 512, 1024]
2025-07-31 19:25:41 - pulsar_trainer - INFO - 🔍 从数据配置对象检测到模态: TPP
2025-07-31 19:25:41 - pulsar_trainer - INFO - 🎯 模态感知损失函数选择: TPP → cross_entropy
2025-07-31 19:25:41 - pulsar_trainer - INFO - 📋 交叉熵损失函数 (标签平滑: 0.1)
2025-07-31 19:25:41 - pulsar_trainer - INFO - 🖥️ 使用GPU: NVIDIA GeForce RTX 4050 Laptop GPU
2025-07-31 19:25:41 - pulsar_trainer - INFO - 💾 GPU内存: 6.4GB
2025-07-31 19:25:41 - pulsar_trainer - INFO - 📋 模型参数总数: 39,096,359
2025-07-31 19:25:41 - pulsar_trainer - INFO - 🏗️ 模型架构: CoAtNet
2025-07-31 19:25:41 - pulsar_trainer - INFO - 📚 数据集大小:
2025-07-31 19:25:41 - pulsar_trainer - INFO -   - 训练集: 1674
2025-07-31 19:25:41 - pulsar_trainer - INFO -   - 验证集: 360
2025-07-31 19:25:41 - pulsar_trainer - INFO -   - 测试集: 358
2025-07-31 19:25:41 - pulsar_trainer - INFO -   - 总计: 2392
2025-07-31 19:25:41 - pulsar_trainer - INFO - ============================================================
2025-07-31 19:25:41 - pulsar_trainer - INFO - 🚀 开始脉冲星分类训练
2025-07-31 19:25:41 - pulsar_trainer - INFO - 📊 模态: TPP
2025-07-31 19:25:41 - pulsar_trainer - INFO - 🧠 模型: coatnet
2025-07-31 19:25:41 - pulsar_trainer - INFO - 📦 批次大小: 32
2025-07-31 19:25:41 - pulsar_trainer - INFO - 🎯 学习率: 0.001
2025-07-31 19:25:41 - pulsar_trainer - INFO - 🔄 训练轮数: 100
2025-07-31 19:25:41 - pulsar_trainer - INFO - ============================================================
2025-07-31 19:26:03 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.8943088054656982, 'fd_loss': 0.5428918600082397, 'vd_loss': 0.6205710768699646, 'confidence_penalty': 0.0, 'total_loss': 1.3519260883331299}
2025-07-31 19:26:31 - pulsar_trainer - INFO - Epoch   1: Train Loss=3.5883, Val Loss=0.4308, Train Acc=57.83%, Val Acc=86.11%
2025-07-31 19:26:31 - pulsar_trainer - INFO - Validation F1: 85.8379, Precision: 89.1304, Recall: 86.1111
2025-07-31 19:26:32 - pulsar_trainer - INFO - 模型已保存: D:\pulsarSuanfa\outputs_TPP\models\best_model.pth
2025-07-31 19:26:32 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.598435640335083, 'fd_loss': 0.35408496856689453, 'vd_loss': 0.6383814811706543, 'confidence_penalty': 0.0007557660574093461, 'total_loss': 0.9677483439445496}
2025-07-31 19:26:37 - pulsar_trainer - INFO - Epoch   2: Train Loss=0.4459, Val Loss=0.2772, Train Acc=89.73%, Val Acc=97.78%
2025-07-31 19:26:37 - pulsar_trainer - INFO - Validation F1: 97.7777, Precision: 97.7837, Recall: 97.7778
2025-07-31 19:26:37 - pulsar_trainer - INFO - 模型已保存: D:\pulsarSuanfa\outputs_TPP\models\best_model.pth
2025-07-31 19:26:37 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.1389928013086319, 'fd_loss': 0.07284428179264069, 'vd_loss': 0.12435412406921387, 'confidence_penalty': 0.003932950086891651, 'total_loss': 0.21665413677692413}
2025-07-31 19:26:43 - pulsar_trainer - INFO - Epoch   3: Train Loss=0.4961, Val Loss=2.3537, Train Acc=91.76%, Val Acc=50.28%
2025-07-31 19:26:43 - pulsar_trainer - INFO - Validation F1: 33.9477, Precision: 75.0696, Recall: 50.2778
2025-07-31 19:26:43 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 2.3789145946502686, 'fd_loss': 0.49017953872680664, 'vd_loss': 0.8796473741531372, 'confidence_penalty': 0.0045178900472819805, 'total_loss': 2.892416477203369}
2025-07-31 19:26:48 - pulsar_trainer - INFO - Epoch   4: Train Loss=0.4794, Val Loss=0.2604, Train Acc=89.90%, Val Acc=97.78%
2025-07-31 19:26:48 - pulsar_trainer - INFO - Validation F1: 97.7767, Precision: 97.8723, Recall: 97.7778
2025-07-31 19:26:48 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.20353026688098907, 'fd_loss': 0.13142246007919312, 'vd_loss': 0.11332449316978455, 'confidence_penalty': 0.002793235471472144, 'total_loss': 0.30603209137916565}
2025-07-31 19:26:53 - pulsar_trainer - INFO - Epoch   5: Train Loss=0.3157, Val Loss=0.2306, Train Acc=95.22%, Val Acc=99.44%
2025-07-31 19:26:53 - pulsar_trainer - INFO - Validation F1: 99.4444, Precision: 99.4505, Recall: 99.4444
2025-07-31 19:26:53 - pulsar_trainer - INFO - 模型已保存: D:\pulsarSuanfa\outputs_TPP\models\best_model.pth
2025-07-31 19:26:53 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.17686215043067932, 'fd_loss': 0.09431789815425873, 'vd_loss': 0.07124185562133789, 'confidence_penalty': 0.003933980595320463, 'total_loss': 0.2493276447057724}
2025-07-31 19:26:58 - pulsar_trainer - INFO - Epoch   6: Train Loss=0.2362, Val Loss=0.2387, Train Acc=97.67%, Val Acc=98.61%
2025-07-31 19:26:58 - pulsar_trainer - INFO - Validation F1: 98.6108, Precision: 98.6486, Recall: 98.6111
2025-07-31 19:26:58 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.01690657250583172, 'fd_loss': 0.016753628849983215, 'vd_loss': 7.605552673339844e-05, 'confidence_penalty': 0.0033246383536607027, 'total_loss': 0.02863084338605404}
2025-07-31 19:27:03 - pulsar_trainer - INFO - Epoch   7: Train Loss=0.2121, Val Loss=0.2186, Train Acc=98.09%, Val Acc=99.44%
2025-07-31 19:27:03 - pulsar_trainer - INFO - Validation F1: 99.4444, Precision: 99.4505, Recall: 99.4444
2025-07-31 19:27:03 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.09006041288375854, 'fd_loss': 0.07762112468481064, 'vd_loss': 0.10103893280029297, 'confidence_penalty': 0.002495840657502413, 'total_loss': 0.1616785079240799}
2025-07-31 19:27:08 - pulsar_trainer - INFO - Epoch   8: Train Loss=0.2177, Val Loss=0.2138, Train Acc=97.85%, Val Acc=100.00%
2025-07-31 19:27:08 - pulsar_trainer - INFO - Validation F1: 100.0000, Precision: 100.0000, Recall: 100.0000
2025-07-31 19:27:08 - pulsar_trainer - INFO - 模型已保存: D:\pulsarSuanfa\outputs_TPP\models\best_model.pth
2025-07-31 19:27:08 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.05563510209321976, 'fd_loss': 0.05173928290605545, 'vd_loss': 0.025635451078414917, 'confidence_penalty': 0.001814796938560903, 'total_loss': 0.091010183095932}
2025-07-31 19:27:13 - pulsar_trainer - INFO - Epoch   9: Train Loss=0.2027, Val Loss=0.2095, Train Acc=98.27%, Val Acc=100.00%
2025-07-31 19:27:13 - pulsar_trainer - INFO - Validation F1: 100.0000, Precision: 100.0000, Recall: 100.0000
2025-07-31 19:27:13 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.0528404675424099, 'fd_loss': 0.04970622807741165, 'vd_loss': 0.053349047899246216, 'confidence_penalty': 0.0013014725409448147, 'total_loss': 0.0949997678399086}
2025-07-31 19:27:18 - pulsar_trainer - INFO - Epoch  10: Train Loss=0.2255, Val Loss=0.2312, Train Acc=97.79%, Val Acc=99.17%
2025-07-31 19:27:18 - pulsar_trainer - INFO - Validation F1: 99.1666, Precision: 99.1803, Recall: 99.1667
2025-07-31 19:27:18 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.02932223491370678, 'fd_loss': 0.028751587495207787, 'vd_loss': 0.0120927095413208, 'confidence_penalty': 0.0023917830549180508, 'total_loss': 0.04971762374043465}
2025-07-31 19:27:23 - pulsar_trainer - INFO - Epoch  11: Train Loss=0.2160, Val Loss=0.2376, Train Acc=98.03%, Val Acc=99.44%
2025-07-31 19:27:23 - pulsar_trainer - INFO - Validation F1: 99.4444, Precision: 99.4505, Recall: 99.4444
2025-07-31 19:27:23 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.04534466937184334, 'fd_loss': 0.037644535303115845, 'vd_loss': 0.06158483028411865, 'confidence_penalty': 0.002967018401250243, 'total_loss': 0.08560939878225327}
2025-07-31 19:27:28 - pulsar_trainer - INFO - Epoch  12: Train Loss=0.2049, Val Loss=0.2202, Train Acc=98.27%, Val Acc=99.44%
2025-07-31 19:27:28 - pulsar_trainer - INFO - Validation F1: 99.4444, Precision: 99.4505, Recall: 99.4444
2025-07-31 19:27:28 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.10909976065158844, 'fd_loss': 0.05936809629201889, 'vd_loss': 0.02333831787109375, 'confidence_penalty': 0.0025613592006266117, 'total_loss': 0.1483466625213623}
2025-07-31 19:27:33 - pulsar_trainer - INFO - Epoch  13: Train Loss=0.2123, Val Loss=0.2203, Train Acc=98.09%, Val Acc=99.17%
2025-07-31 19:27:33 - pulsar_trainer - INFO - Validation F1: 99.1667, Precision: 99.1682, Recall: 99.1667
2025-07-31 19:27:33 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.04759335517883301, 'fd_loss': 0.04370328038930893, 'vd_loss': 0.021245718002319336, 'confidence_penalty': 0.0025159677024930716, 'total_loss': 0.07833468168973923}
2025-07-31 19:27:38 - pulsar_trainer - INFO - Epoch  14: Train Loss=0.2240, Val Loss=0.2336, Train Acc=97.79%, Val Acc=98.89%
2025-07-31 19:27:38 - pulsar_trainer - INFO - Validation F1: 98.8889, Precision: 98.8949, Recall: 98.8889
2025-07-31 19:27:38 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.028744524344801903, 'fd_loss': 0.028037987649440765, 'vd_loss': 0.025643229484558105, 'confidence_penalty': 0.003067896468564868, 'total_loss': 0.053524382412433624}
2025-07-31 19:27:43 - pulsar_trainer - INFO - Epoch  15: Train Loss=0.2050, Val Loss=0.2202, Train Acc=98.51%, Val Acc=99.17%
2025-07-31 19:27:43 - pulsar_trainer - INFO - Validation F1: 99.1666, Precision: 99.1803, Recall: 99.1667
2025-07-31 19:27:43 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.03355884552001953, 'fd_loss': 0.032922569662332535, 'vd_loss': 0.026529788970947266, 'confidence_penalty': 0.0018844126025214791, 'total_loss': 0.05986347794532776}
2025-07-31 19:27:48 - pulsar_trainer - INFO - Epoch  16: Train Loss=0.1936, Val Loss=0.2097, Train Acc=98.81%, Val Acc=99.72%
2025-07-31 19:27:48 - pulsar_trainer - INFO - Validation F1: 99.7222, Precision: 99.7238, Recall: 99.7222
2025-07-31 19:27:48 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.05317793786525726, 'fd_loss': 0.05152689665555954, 'vd_loss': 0.028257280588150024, 'confidence_penalty': 0.0021169837564229965, 'total_loss': 0.08953555673360825}
2025-07-31 19:27:53 - pulsar_trainer - INFO - Epoch  17: Train Loss=0.1905, Val Loss=0.2131, Train Acc=98.81%, Val Acc=99.44%
2025-07-31 19:27:53 - pulsar_trainer - INFO - Validation F1: 99.4444, Precision: 99.4505, Recall: 99.4444
2025-07-31 19:27:53 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.04546260088682175, 'fd_loss': 0.04418342560529709, 'vd_loss': 0.008891761302947998, 'confidence_penalty': 0.0013236913364380598, 'total_loss': 0.07154553383588791}
2025-07-31 19:27:58 - pulsar_trainer - INFO - Epoch  18: Train Loss=0.2104, Val Loss=0.2329, Train Acc=97.97%, Val Acc=99.17%
2025-07-31 19:27:58 - pulsar_trainer - INFO - Validation F1: 99.1666, Precision: 99.1803, Recall: 99.1667
2025-07-31 19:27:58 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.04521469399333, 'fd_loss': 0.043517112731933594, 'vd_loss': 0.013503313064575195, 'confidence_penalty': 0.0016839354066178203, 'total_loss': 0.07270818203687668}
2025-07-31 19:28:03 - pulsar_trainer - INFO - Epoch  19: Train Loss=0.1998, Val Loss=0.2188, Train Acc=98.45%, Val Acc=100.00%
2025-07-31 19:28:03 - pulsar_trainer - INFO - Validation F1: 100.0000, Precision: 100.0000, Recall: 100.0000
2025-07-31 19:28:03 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.03314298018813133, 'fd_loss': 0.03234725818037987, 'vd_loss': 0.038814544677734375, 'confidence_penalty': 0.0026098662056028843, 'total_loss': 0.06357083469629288}
2025-07-31 19:28:08 - pulsar_trainer - INFO - Epoch  20: Train Loss=0.2234, Val Loss=0.2212, Train Acc=97.49%, Val Acc=99.44%
2025-07-31 19:28:08 - pulsar_trainer - INFO - Validation F1: 99.4444, Precision: 99.4505, Recall: 99.4444
2025-07-31 19:28:09 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.06244102492928505, 'fd_loss': 0.05943102762103081, 'vd_loss': 0.0359654426574707, 'confidence_penalty': 0.0031910778488963842, 'total_loss': 0.1061372458934784}
2025-07-31 19:28:13 - pulsar_trainer - INFO - Epoch  21: Train Loss=0.2072, Val Loss=0.2142, Train Acc=98.21%, Val Acc=98.89%
2025-07-31 19:28:13 - pulsar_trainer - INFO - Validation F1: 98.8889, Precision: 98.8949, Recall: 98.8889
2025-07-31 19:28:13 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.04341236129403114, 'fd_loss': 0.041365914046764374, 'vd_loss': 0.03702956438064575, 'confidence_penalty': 0.0020377859473228455, 'total_loss': 0.07724197208881378}
2025-07-31 19:28:18 - pulsar_trainer - INFO - Epoch  22: Train Loss=0.2009, Val Loss=0.2151, Train Acc=98.75%, Val Acc=99.72%
2025-07-31 19:28:18 - pulsar_trainer - INFO - Validation F1: 99.7222, Precision: 99.7238, Recall: 99.7222
2025-07-31 19:28:18 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.04558279365301132, 'fd_loss': 0.042392682284116745, 'vd_loss': 0.023227840662002563, 'confidence_penalty': 0.002028628019616008, 'total_loss': 0.0757761150598526}
2025-07-31 19:28:23 - pulsar_trainer - INFO - Epoch  23: Train Loss=0.1978, Val Loss=0.2017, Train Acc=98.81%, Val Acc=100.00%
2025-07-31 19:28:23 - pulsar_trainer - INFO - Validation F1: 100.0000, Precision: 100.0000, Recall: 100.0000
2025-07-31 19:28:23 - pulsar_trainer - INFO - 早停触发，在第 23 轮停止训练
2025-07-31 19:28:23 - pulsar_trainer - INFO - ============================================================
2025-07-31 19:28:23 - pulsar_trainer - INFO - ✅ 训练完成
2025-07-31 19:28:23 - pulsar_trainer - INFO - 🏆 最佳验证准确率: 100.0000
2025-07-31 19:28:23 - pulsar_trainer - INFO - ⏱️ 总训练时间: 162.88秒
2025-07-31 19:28:23 - pulsar_trainer - INFO - ============================================================
2025-07-31 19:28:23 - pulsar_trainer - INFO - 🔍 开始最终评估...
2025-07-31 19:28:24 - pulsar_trainer - INFO - 模型已加载: D:\pulsarSuanfa\outputs_TPP\models\best_model.pth
2025-07-31 19:28:47 - pulsar_trainer.utils.comprehensive_misclassification_analysis - INFO - 开始全面误分类分析...
2025-07-31 19:28:47 - pulsar_trainer.utils.comprehensive_misclassification_analysis - INFO - 综合误分类分析报告已生成: D:\pulsarSuanfa\outputs_TPP\results\comprehensive_misclassification_analysis\comprehensive_misclassification_report.txt
2025-07-31 19:28:47 - pulsar_trainer.utils.comprehensive_misclassification_analysis - INFO - 全面误分类分析完成。报告保存至: D:\pulsarSuanfa\outputs_TPP\results\comprehensive_misclassification_analysis
2025-07-31 19:28:47 - pulsar_trainer - INFO - 📊 评估结果:
2025-07-31 19:28:47 - pulsar_trainer - INFO -   - accuracy: 0.9860
2025-07-31 19:28:47 - pulsar_trainer - INFO -   - precision: 0.9888
2025-07-31 19:28:47 - pulsar_trainer - INFO -   - recall: 0.9832
2025-07-31 19:28:47 - pulsar_trainer - INFO -   - specificity: 0.9888
2025-07-31 19:28:47 - pulsar_trainer - INFO -   - f1_score: 0.9860
2025-07-31 19:28:47 - pulsar_trainer - INFO -   - false_positive_rate: 0.0112
2025-07-31 19:28:47 - pulsar_trainer - INFO - 📊 增强模块统计信息:
2025-07-31 19:28:47 - pulsar_trainer - INFO -   - 总前向传播次数: 1596
2025-07-31 19:28:47 - pulsar_trainer - INFO -   - FPP激活次数: 532
2025-07-31 19:28:47 - pulsar_trainer - INFO -   - TPP激活次数: 532
2025-07-31 19:28:47 - pulsar_trainer - INFO -   - 损失计算次数: 424
2025-07-31 19:28:47 - pulsar_trainer - INFO -   - FPP激活率: 33.33%
2025-07-31 19:28:47 - pulsar_trainer - INFO -   - TPP激活率: 33.33%
2025-07-31 19:28:50 - pulsar_trainer - INFO - 所有结果已保存到: D:\pulsarSuanfa\outputs_TPP\results
2025-07-31 19:28:50 - pulsar_trainer - INFO - 可视化图表已保存到: D:\pulsarSuanfa\outputs_TPP\plots
