"""
FPP Enhancement Module - FPP误分类解决方案
集成FFTNetBlock2D和SimplifiedFreqFusion，专门解决脉冲星FPP数据的假阳性问题

模块组成:
- FFTNetBlock2D: 频域特征处理，提取脉冲星周期性特征
- SimplifiedFreqFusion: 频域感知特征融合，增强特征表示能力

目标: 在保持100%召回率基础上，进一步提高精确率，减少假阳性误分类
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import warnings


class ModReLU(nn.Module):
    """
    复数域的修正线性单元激活函数
    专门用于处理FFT变换后的复数特征
    """
    def __init__(self, features):
        super().__init__()
        self.b = nn.Parameter(torch.Tensor(features))
        self.b.data.uniform_(-0.1, 0.1)

    def forward(self, x):
        """
        Args:
            x: 复数张量
        Returns:
            激活后的复数张量
        """
        return torch.abs(x) * F.relu(torch.cos(torch.angle(x) + self.b))


class FFTNetBlock2D(nn.Module):
    """
    2D图像频域特征处理模块

    专门适配脉冲星FPP数据的频域特征增强：
    - 使用2D FFT处理图像的频域信息
    - 通过ModReLU激活函数处理复数域特征
    - 提取脉冲星信号的周期性特征

    Args:
        dim: 特征维度，对应输入图像的通道数

    Input:
        x: [batch_size, channels, height, width] - 4D图像张量

    Output:
        x: [batch_size, channels, height, width] - 增强后的图像张量
    """

    def __init__(self, dim):
        super().__init__()
        self.dim = dim
        self.filter = nn.Linear(dim, dim)
        self.modrelu = ModReLU(dim)

    def forward(self, x):
        """
        2D图像的频域特征处理

        Args:
            x: [batch_size, channels, height, width] - 输入图像张量

        Returns:
            x_out: [batch_size, channels, height, width] - 频域增强后的图像张量
        """
        # 输入形状验证
        assert len(x.shape) == 4, f"Input must be 4D tensor [B, C, H, W], got shape {x.shape}"

        batch_size, channels, height, width = x.shape
        assert channels == self.dim, f"Input channels {channels} must match dim {self.dim}"

        # 将图像重塑为 [batch_size, height*width, channels] 以便进行线性变换
        x_reshaped = x.permute(0, 2, 3, 1).reshape(batch_size, height * width, channels)

        # 2D FFT处理 - 在空间维度上进行FFT
        x_spatial = x_reshaped.reshape(batch_size, height, width, channels).permute(0, 3, 1, 2)
        x_fft = torch.fft.fft2(x_spatial)  # 2D FFT处理

        # 频域滤波 - 分别处理实部和虚部
        x_fft_reshaped = x_fft.permute(0, 2, 3, 1).reshape(batch_size, height * width, channels)
        x_filtered = self.filter(x_fft_reshaped.real) + 1j * self.filter(x_fft_reshaped.imag)

        # 复数域激活
        x_filtered = self.modrelu(x_filtered)

        # 逆FFT变换回空间域
        x_filtered_spatial = x_filtered.reshape(batch_size, height, width, channels).permute(0, 3, 1, 2)
        x_out = torch.fft.ifft2(x_filtered_spatial).real

        return x_out


# 测试代码
if __name__ == '__main__':
    # 测试2D图像处理
    batch_size = 2
    channels = 256  # 对应CoAtNet s2阶段的通道数
    height, width = 16, 16  # 对应64x64输入经过s0,s1后的尺寸

    # 创建测试输入
    x = torch.randn(batch_size, channels, height, width)
    print(f"Input shape: {x.shape}")

    # 初始化模块
    model = FFTNetBlock2D(dim=channels)
    print(f"Model parameters: {sum(p.numel() for p in model.parameters())}")

    # 前向传播测试
    with torch.no_grad():
        output = model(x)
        print(f"Output shape: {output.shape}")
        print(f"Shape preserved: {x.shape == output.shape}")
        print("FFTNetBlock2D adaptation completed successfully!")
