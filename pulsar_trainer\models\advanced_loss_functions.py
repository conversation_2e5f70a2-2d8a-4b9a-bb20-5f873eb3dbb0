"""
高级损失函数模块 - 阶段3：损失函数优化
专门针对FPP问题的召回率保护和精确率提升

包含以下损失函数：
1. RecallProtectionLoss - 召回率保护损失函数
2. PrecisionEnhancementLoss - 精确率提升损失函数  
3. FocalLossVariant - Focal Loss变体
4. ContrastiveDiscriminationLoss - 对比判别损失
5. AdaptiveWeightedLoss - 自适应加权损失组合器

版本: 3.0.0 (阶段3：损失函数优化)
作者: Pulsar Classification Enhancement Team
日期: 2025-01-31
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import Optional, Dict, Any, Tuple


class RecallProtectionLoss(nn.Module):
    """
    召回率保护损失函数
    通过对假阴性给予更高惩罚来保护召回率
    """
    def __init__(self, false_negative_weight: float = 10.0, temperature: float = 1.0):
        super().__init__()
        self.false_negative_weight = false_negative_weight
        self.temperature = temperature
        
    def forward(self, logits: torch.Tensor, targets: torch.Tensor) -> torch.Tensor:
        """
        Args:
            logits: 模型输出 [B, num_classes]
            targets: 真实标签 [B]
        """
        # 计算标准交叉熵
        ce_loss = F.cross_entropy(logits / self.temperature, targets, reduction='none')
        
        # 计算预测概率
        probs = F.softmax(logits / self.temperature, dim=1)
        pred_classes = torch.argmax(probs, dim=1)
        
        # 识别假阴性样本（真实为正类但预测为负类）
        false_negatives = (targets == 1) & (pred_classes == 0)
        
        # 对假阴性样本应用更高权重
        weights = torch.ones_like(ce_loss)
        weights[false_negatives] = self.false_negative_weight
        
        # 加权损失
        weighted_loss = ce_loss * weights
        
        return weighted_loss.mean()


class PrecisionEnhancementLoss(nn.Module):
    """
    精确率提升损失函数
    通过对假阳性给予额外惩罚来提升精确率
    """
    def __init__(self, false_positive_weight: float = 5.0, margin: float = 0.1):
        super().__init__()
        self.false_positive_weight = false_positive_weight
        self.margin = margin
        
    def forward(self, logits: torch.Tensor, targets: torch.Tensor) -> torch.Tensor:
        """
        Args:
            logits: 模型输出 [B, num_classes]
            targets: 真实标签 [B]
        """
        # 计算预测概率
        probs = F.softmax(logits, dim=1)
        pred_classes = torch.argmax(probs, dim=1)
        
        # 识别假阳性样本（真实为负类但预测为正类）
        false_positives = (targets == 0) & (pred_classes == 1)
        
        # 基础交叉熵损失
        base_loss = F.cross_entropy(logits, targets, reduction='none')
        
        # 对假阳性样本的额外惩罚
        fp_penalty = torch.zeros_like(base_loss)
        if false_positives.any():
            # 使用margin-based penalty
            positive_probs = probs[false_positives, 1]  # 假阳性样本的正类概率
            fp_penalty[false_positives] = F.relu(positive_probs - self.margin) * self.false_positive_weight
        
        total_loss = base_loss + fp_penalty
        return total_loss.mean()


class FocalLossVariant(nn.Module):
    """
    Focal Loss变体
    专门针对FPP问题的类别不平衡处理
    """
    def __init__(self, alpha: float = 0.75, gamma: float = 2.0, recall_focus: bool = True):
        super().__init__()
        self.alpha = alpha
        self.gamma = gamma
        self.recall_focus = recall_focus
        
    def forward(self, logits: torch.Tensor, targets: torch.Tensor) -> torch.Tensor:
        """
        Args:
            logits: 模型输出 [B, num_classes]
            targets: 真实标签 [B]
        """
        # 计算交叉熵
        ce_loss = F.cross_entropy(logits, targets, reduction='none')
        
        # 计算预测概率
        probs = F.softmax(logits, dim=1)
        
        # 获取正确类别的概率
        pt = probs.gather(1, targets.unsqueeze(1)).squeeze(1)
        
        # 计算alpha权重
        if self.recall_focus:
            # 对正类（脉冲星）给予更高权重
            alpha_t = torch.where(targets == 1, self.alpha, 1 - self.alpha)
        else:
            alpha_t = torch.ones_like(targets, dtype=torch.float)
        
        # 计算focal权重
        focal_weight = alpha_t * (1 - pt) ** self.gamma
        
        # 最终损失
        focal_loss = focal_weight * ce_loss
        
        return focal_loss.mean()


class ContrastiveDiscriminationLoss(nn.Module):
    """
    对比判别损失函数
    利用FDE模块的对比特征增强判别能力
    """
    def __init__(self, temperature: float = 0.1, margin: float = 0.5):
        super().__init__()
        self.temperature = temperature
        self.margin = margin
        
    def forward(self, contrast_features: torch.Tensor, targets: torch.Tensor) -> torch.Tensor:
        """
        Args:
            contrast_features: 对比特征 [B, feature_dim]
            targets: 真实标签 [B]
        """
        batch_size = contrast_features.size(0)
        
        # L2归一化
        contrast_features = F.normalize(contrast_features, p=2, dim=1)
        
        # 计算相似度矩阵
        similarity_matrix = torch.matmul(contrast_features, contrast_features.T) / self.temperature
        
        # 创建标签掩码
        labels = targets.view(-1, 1)
        mask = torch.eq(labels, labels.T).float()
        
        # 正样本掩码（排除自身）
        positive_mask = mask * (1 - torch.eye(batch_size, device=contrast_features.device))
        
        # 负样本掩码
        negative_mask = 1 - mask
        
        # 计算对比损失
        exp_sim = torch.exp(similarity_matrix)
        
        # 正样本相似度
        positive_sum = (exp_sim * positive_mask).sum(dim=1)
        
        # 所有样本相似度（排除自身）
        total_sum = exp_sim.sum(dim=1) - torch.diag(exp_sim)
        
        # 对比损失
        contrastive_loss = -torch.log(positive_sum / (total_sum + 1e-8))
        
        # 只对有正样本对的样本计算损失
        valid_samples = positive_sum > 0
        if valid_samples.any():
            return contrastive_loss[valid_samples].mean()
        else:
            return torch.tensor(0.0, device=contrast_features.device, requires_grad=True)


class AdaptiveWeightedLoss(nn.Module):
    """
    自适应加权损失组合器
    动态调整不同损失函数的权重
    """
    def __init__(self, 
                 recall_weight: float = 3.0,
                 precision_weight: float = 2.0,
                 focal_weight: float = 1.0,
                 contrastive_weight: float = 0.5,
                 adaptation_rate: float = 0.01):
        super().__init__()
        
        # 初始权重
        self.register_parameter('recall_weight', nn.Parameter(torch.tensor(recall_weight)))
        self.register_parameter('precision_weight', nn.Parameter(torch.tensor(precision_weight)))
        self.register_parameter('focal_weight', nn.Parameter(torch.tensor(focal_weight)))
        self.register_parameter('contrastive_weight', nn.Parameter(torch.tensor(contrastive_weight)))
        
        self.adaptation_rate = adaptation_rate
        
        # 损失函数组件
        self.recall_loss = RecallProtectionLoss()
        self.precision_loss = PrecisionEnhancementLoss()
        self.focal_loss = FocalLossVariant()
        self.contrastive_loss = ContrastiveDiscriminationLoss()
        
        # 性能监控
        self.register_buffer('recall_history', torch.zeros(100))
        self.register_buffer('precision_history', torch.zeros(100))
        self.register_buffer('step_count', torch.tensor(0))
        
    def forward(self, 
                logits: torch.Tensor, 
                targets: torch.Tensor,
                contrast_features: Optional[torch.Tensor] = None) -> Dict[str, torch.Tensor]:
        """
        Args:
            logits: 模型输出 [B, num_classes]
            targets: 真实标签 [B]
            contrast_features: 对比特征 [B, feature_dim]
        """
        # 计算各个损失组件
        recall_loss = self.recall_loss(logits, targets)
        precision_loss = self.precision_loss(logits, targets)
        focal_loss = self.focal_loss(logits, targets)
        
        # 对比损失（如果提供对比特征）
        if contrast_features is not None:
            contrastive_loss = self.contrastive_loss(contrast_features, targets)
        else:
            contrastive_loss = torch.tensor(0.0, device=logits.device)
        
        # 应用权重
        weighted_recall = torch.abs(self.recall_weight) * recall_loss
        weighted_precision = torch.abs(self.precision_weight) * precision_loss
        weighted_focal = torch.abs(self.focal_weight) * focal_loss
        weighted_contrastive = torch.abs(self.contrastive_weight) * contrastive_loss
        
        # 总损失
        total_loss = weighted_recall + weighted_precision + weighted_focal + weighted_contrastive
        
        return {
            'total_loss': total_loss,
            'recall_loss': recall_loss,
            'precision_loss': precision_loss,
            'focal_loss': focal_loss,
            'contrastive_loss': contrastive_loss,
            'weighted_recall': weighted_recall,
            'weighted_precision': weighted_precision,
            'weighted_focal': weighted_focal,
            'weighted_contrastive': weighted_contrastive
        }
    
    def update_weights(self, current_recall: float, current_precision: float):
        """
        根据当前性能动态调整权重
        """
        step = self.step_count.item() % 100
        self.recall_history[step] = current_recall
        self.precision_history[step] = current_precision
        self.step_count += 1
        
        if self.step_count > 10:  # 有足够历史数据后开始调整
            # 计算性能趋势
            recent_recall = self.recall_history[max(0, step-10):step].mean()
            recent_precision = self.precision_history[max(0, step-10):step].mean()
            
            # 如果召回率下降，增加召回率损失权重
            if current_recall < recent_recall:
                self.recall_weight.data += self.adaptation_rate
            
            # 如果精确率下降，增加精确率损失权重
            if current_precision < recent_precision:
                self.precision_weight.data += self.adaptation_rate
            
            # 权重衰减防止过度增长
            self.recall_weight.data *= 0.999
            self.precision_weight.data *= 0.999
            self.focal_weight.data *= 0.999
            self.contrastive_weight.data *= 0.999


def test_advanced_loss_functions():
    """测试高级损失函数"""
    print("测试高级损失函数模块...")
    
    # 创建测试数据
    batch_size = 8
    num_classes = 2
    feature_dim = 64
    
    logits = torch.randn(batch_size, num_classes)
    targets = torch.randint(0, num_classes, (batch_size,))
    contrast_features = torch.randn(batch_size, feature_dim)
    
    # 测试自适应加权损失
    adaptive_loss = AdaptiveWeightedLoss()
    
    loss_dict = adaptive_loss(logits, targets, contrast_features)
    
    print(f"✅ 高级损失函数测试通过")
    print(f"   总损失: {loss_dict['total_loss'].item():.4f}")
    print(f"   召回率损失: {loss_dict['recall_loss'].item():.4f}")
    print(f"   精确率损失: {loss_dict['precision_loss'].item():.4f}")
    print(f"   Focal损失: {loss_dict['focal_loss'].item():.4f}")
    print(f"   对比损失: {loss_dict['contrastive_loss'].item():.4f}")
    
    # 测试权重更新
    adaptive_loss.update_weights(0.99, 0.97)
    print(f"   权重更新完成")
    
    return True


if __name__ == '__main__':
    test_advanced_loss_functions()
