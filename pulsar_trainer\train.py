#!/usr/bin/env python3
"""
Main training script for CoAtNet pulsar classification.

This script provides a command-line interface for training and evaluating
CoAtNet models on pulsar classification tasks.

Usage:
    python train.py --config config/coatnet_config.yaml
    python train.py --config config/coatnet_config.yaml --modality TPP
    python train.py --config config/coatnet_config.yaml --output-dir custom_output
"""

import argparse
import sys
from pathlib import Path
import traceback

# Add the current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

try:
    from utils.config import load_config_with_classes, validate_config_with_classes
    from utils.logging import PulsarLogger
    from utils.path_manager import PathManager
    from training.trainer import PulsarTrainer
except ImportError:
    # If running from within the package directory
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.abspath(__file__)))
    from utils.config import load_config_with_classes, validate_config_with_classes
    from utils.logging import PulsarLogger
    from utils.path_manager import PathManager
    from training.trainer import PulsarTrainer


def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description="CoAtNet脉冲星分类训练系统",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python train.py --config config/coatnet_config.yaml
  python train.py --config config/coatnet_config.yaml --modality TPP
  python train.py --config config/coatnet_config.yaml --output-dir custom_output
  python train.py --config config/coatnet_config.yaml --epochs 100 --batch-size 32
        """
    )

    # Required arguments
    parser.add_argument(
        '--config',
        type=str,
        default='coatnet_config.yaml',
        help='配置文件路径 (默认: coatnet_config.yaml)'
    )

    # Optional overrides
    parser.add_argument(
        '--modality',
        type=str,
        choices=['FPP', 'TPP'],
        help='覆盖配置中的模态选择 (FPP 或 TPP)'
    )

    parser.add_argument(
        '--output-dir',
        type=str,
        help='输出目录路径'
    )

    parser.add_argument(
        '--epochs',
        type=int,
        help='训练轮数'
    )

    parser.add_argument(
        '--batch-size',
        type=int,
        help='批次大小'
    )

    parser.add_argument(
        '--learning-rate',
        type=float,
        help='学习率'
    )

    parser.add_argument(
        '--device',
        type=str,
        choices=['cpu', 'gpu', 'auto'],
        default='auto',
        help='计算设备选择 (默认: auto)'
    )

    parser.add_argument(
        '--seed',
        type=int,
        default=42,
        help='随机种子 (默认: 42)'
    )

    parser.add_argument(
        '--verbose',
        action='store_true',
        help='详细输出模式'
    )

    parser.add_argument(
        '--dry-run',
        action='store_true',
        help='干运行模式，只验证配置不执行训练'
    )

    return parser.parse_args()


def override_config(config, args):
    """Override configuration with command line arguments."""
    # Override modality - 兼容新旧配置系统
    if args.modality:
        if hasattr(config['data'], 'modality'):
            config['data'].modality = args.modality
        else:
            config['data']['modality'] = args.modality
        print(f"✓ 模态覆盖为: {args.modality}")

    # Override output directory
    if args.output_dir:
        config['output']['output_dir'] = args.output_dir
        print(f"✓ 输出目录覆盖为: {args.output_dir}")

    # Override training parameters - 兼容新旧配置系统
    if args.epochs:
        if hasattr(config['training'], 'epochs'):
            config['training'].epochs = args.epochs
        else:
            config['training']['epochs'] = args.epochs
        print(f"✓ 训练轮数覆盖为: {args.epochs}")

    if args.batch_size:
        if hasattr(config['training'], 'batch_size'):
            config['training'].batch_size = args.batch_size
        else:
            config['training']['batch_size'] = args.batch_size
        print(f"✓ 批次大小覆盖为: {args.batch_size}")

    if args.learning_rate:
        if hasattr(config['training'], 'learning_rate'):
            config['training'].learning_rate = args.learning_rate
        else:
            config['training']['learning_rate'] = args.learning_rate
        print(f"✓ 学习率覆盖为: {args.learning_rate}")

    # Override device preference
    if args.device != 'auto':
        config['device']['prefer_gpu'] = (args.device == 'gpu')
        print(f"✓ 设备偏好覆盖为: {args.device}")

    return config


def setup_logging(config, output_dirs, verbose=False):
    """Setup logging configuration with modality-specific directory."""
    import logging
    log_level = "DEBUG" if verbose else config.get('output', {}).get('log_level', 'INFO')

    # Use the modality-specific log directory from output_dirs
    log_dir = output_dirs['log']

    logger = PulsarLogger('pulsar_trainer', log_dir=log_dir,
                         level=getattr(logging, log_level.upper()))

    return logger


def validate_environment():
    """Validate the environment and dependencies."""
    try:
        import torch
        import numpy as np
        import yaml
        import cv2
        from scipy import ndimage
        from timm.models.layers import DropPath, to_2tuple

        print("✓ 所有依赖项检查通过")
        return True

    except ImportError as e:
        print(f"❌ 缺少依赖项: {e}")
        print("请安装所需的依赖项:")
        print("pip install torch torchvision numpy opencv-python scipy timm pyyaml")
        return False


def print_system_info():
    """Print system information."""
    import torch

    print("=" * 60)
    print("🚀 CoAtNet脉冲星分类训练系统")
    print("=" * 60)
    print(f"Python版本: {sys.version}")
    print(f"PyTorch版本: {torch.__version__}")
    print(f"CUDA可用: {torch.cuda.is_available()}")
    if torch.cuda.is_available():
        print(f"CUDA版本: {torch.version.cuda}")
        print(f"GPU设备: {torch.cuda.get_device_name(0)}")
    print("=" * 60)


def main():
    """Main function."""
    # Parse arguments
    args = parse_arguments()

    # Print system info
    print_system_info()

    # Validate environment
    if not validate_environment():
        sys.exit(1)

    try:
        # Initialize PathManager for cross-environment compatibility
        path_manager = PathManager()

        # 确保配置文件路径正确
        config_path = path_manager.get_config_path(args.config)
        print(f"📋 加载配置文件: {config_path}")
        config = load_config_with_classes(str(config_path))

        # 设置PathManager配置
        path_manager.config = config

        # 使用命令行参数覆盖配置
        config = override_config(config, args)

        # 验证配置
        print("🔍 验证配置...")
        validate_config_with_classes(config)
        print("✓ 配置验证通过")

        # Create output directories first
        from utils.config import create_output_dirs
        output_dirs = create_output_dirs(config)

        # Setup logging with correct directory
        logger = setup_logging(config, output_dirs, args.verbose)

        # Set random seed
        if args.seed:
            import torch
            import numpy as np
            torch.manual_seed(args.seed)
            np.random.seed(args.seed)
            if torch.cuda.is_available():
                torch.cuda.manual_seed_all(args.seed)
            logger.log_info(f"随机种子设置为: {args.seed}")

        # Dry run mode
        if args.dry_run:
            print("🧪 干运行模式 - 配置验证完成，退出")
            logger.log_info("干运行模式完成")
            return

        # Create trainer
        print("🏗️ 初始化训练器...")
        trainer = PulsarTrainer(config, logger)

        # Start training
        print("🚀 开始训练...")
        train_history = trainer.train()

        # Evaluate model
        print("📊 开始评估...")
        eval_results = trainer.evaluate()

        # Save results
        print("💾 保存结果...")
        trainer.save_results(train_history, eval_results)

        # Print summary
        summary = trainer.get_training_summary()
        print("\n" + "=" * 60)
        print("📋 训练总结")
        print("=" * 60)
        print(f"最佳验证准确率: {summary['best_validation_accuracy']:.4f}")
        print(f"最佳验证损失: {summary['best_validation_loss']:.4f}")
        print(f"训练轮数: {summary['total_epochs_trained']}")
        print(f"是否早停: {'是' if summary['early_stopped'] else '否'}")

        # Performance evaluation completed (targets checked internally)

        print("✅ 训练完成")

    except KeyboardInterrupt:
        print("\n⚠️ 用户中断训练")
        sys.exit(1)

    except Exception as e:
        print(f"\n❌ 训练失败: {e}")
        if args.verbose:
            traceback.print_exc()
        sys.exit(1)


if __name__ == '__main__':
    main()
