#!/usr/bin/env python3
"""
Verify that data augmentation is truly disabled in the configuration and implementation.
"""

import sys
import torch
import numpy as np
from pathlib import Path
import yaml

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from pulsar_trainer.data.transforms import ChannelTransformer
from pulsar_trainer.data.dataset import PulsarDataset

def test_channel_transformer_strategies():
    """Test different channel transformation strategies to verify augmentation behavior."""
    print("🧪 测试通道变换策略...")
    
    # Create dummy data
    dummy_data = np.random.rand(64, 64).astype(np.float32)
    
    strategies = ['replicate', 'single_channel', 'physical', 'augment']
    
    results = {}
    
    for strategy in strategies:
        print(f"\n📋 测试策略: {strategy}")
        
        try:
            transformer = ChannelTransformer(strategy=strategy)
            
            # Transform the same data multiple times to check for randomness
            outputs = []
            for i in range(5):
                output = transformer.transform(dummy_data, 'FPP')
                outputs.append(output)
            
            # Check if outputs are identical (no randomness = no augmentation)
            all_identical = True
            for i in range(1, len(outputs)):
                if not np.allclose(outputs[0], outputs[i], atol=1e-6):
                    all_identical = False
                    break
            
            result = {
                'strategy': strategy,
                'output_shape': outputs[0].shape,
                'all_identical': all_identical,
                'has_augmentation': not all_identical,
                'success': True
            }
            
            print(f"   输出形状: {outputs[0].shape}")
            print(f"   多次输出相同: {all_identical}")
            print(f"   包含增强: {not all_identical}")
            
        except Exception as e:
            result = {
                'strategy': strategy,
                'error': str(e),
                'success': False
            }
            print(f"   ❌ 错误: {e}")
        
        results[strategy] = result
    
    return results

def verify_config_file(config_path: str):
    """Verify configuration file settings."""
    print(f"\n🔍 验证配置文件: {config_path}")
    
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        verification = {
            'config_file': config_path,
            'issues': [],
            'success': True
        }
        
        # Check data configuration
        data_config = config.get('data', {})
        channel_strategy = data_config.get('channel_strategy', 'unknown')
        
        print(f"   通道策略: {channel_strategy}")
        
        if channel_strategy == 'single_channel':
            verification['issues'].append("channel_strategy is 'single_channel' which includes physical augmentation")
            verification['success'] = False
        elif channel_strategy == 'augment':
            verification['issues'].append("channel_strategy is 'augment' which includes explicit augmentation")
            verification['success'] = False
        elif channel_strategy == 'replicate':
            print("   ✅ 使用 'replicate' 策略，无增强")
        else:
            verification['issues'].append(f"Unknown channel_strategy: {channel_strategy}")
            verification['success'] = False
        
        # Check augmentation configuration
        aug_config = config.get('augmentation', {})
        aug_enabled = aug_config.get('enabled', True)
        
        print(f"   增强总开关: {aug_enabled}")
        
        if aug_enabled:
            verification['issues'].append("augmentation.enabled is True")
            verification['success'] = False
        else:
            print("   ✅ 增强总开关已禁用")
        
        # Check individual augmentation settings
        for aug_type in ['mixup', 'cutmix', 'random_erasing', 'rotation', 'noise']:
            if aug_type in aug_config:
                aug_type_enabled = aug_config[aug_type].get('enabled', False)
                print(f"   {aug_type}: {aug_type_enabled}")
                
                if aug_type_enabled:
                    verification['issues'].append(f"{aug_type} is enabled")
                    verification['success'] = False
        
        # Check model configuration
        model_config = config.get('model', {}).get('coatnet', {})
        in_channels = model_config.get('in_channels', 1)
        use_predefined = model_config.get('use_predefined', None)
        
        print(f"   输入通道数: {in_channels}")
        print(f"   使用预定义模型: {use_predefined}")
        
        if channel_strategy == 'replicate' and in_channels != 3:
            verification['issues'].append(f"replicate strategy should use 3 channels, got {in_channels}")
            verification['success'] = False
        
        return verification
        
    except Exception as e:
        return {
            'config_file': config_path,
            'error': str(e),
            'success': False
        }

def test_dataset_loading():
    """Test actual dataset loading to verify no augmentation."""
    print(f"\n🔍 测试数据集加载...")
    
    try:
        # Test with replicate strategy (should have no augmentation)
        dataset = PulsarDataset(
            modality='FPP',
            split='train',
            data_root='D:/pulsarSuanfa/datasets/HTRU',
            channel_strategy='replicate',
            normalize=True
        )
        
        if len(dataset) == 0:
            print("   ⚠️ 数据集为空，无法测试")
            return {'success': False, 'error': 'Empty dataset'}
        
        # Load the same sample multiple times
        sample_idx = 0
        samples = []
        
        for i in range(3):
            data, label = dataset[sample_idx]
            samples.append(data.numpy())
        
        # Check if samples are identical
        all_identical = True
        for i in range(1, len(samples)):
            if not np.allclose(samples[0], samples[i], atol=1e-6):
                all_identical = False
                break
        
        result = {
            'dataset_size': len(dataset),
            'sample_shape': samples[0].shape,
            'all_identical': all_identical,
            'has_augmentation': not all_identical,
            'success': True
        }
        
        print(f"   数据集大小: {len(dataset)}")
        print(f"   样本形状: {samples[0].shape}")
        print(f"   多次加载相同: {all_identical}")
        print(f"   包含增强: {not all_identical}")
        
        return result
        
    except Exception as e:
        print(f"   ❌ 错误: {e}")
        return {'success': False, 'error': str(e)}

if __name__ == "__main__":
    print("🚀 开始验证数据增强禁用状态...")
    
    # Test channel transformer strategies
    transformer_results = test_channel_transformer_strategies()
    
    # Verify configuration file
    config_path = "D:/pulsarSuanfa/pulsar_trainer/config/coatnet_config_FPP.yaml"
    config_verification = verify_config_file(config_path)
    
    # Test dataset loading
    dataset_result = test_dataset_loading()
    
    # Summary
    print(f"\n{'='*60}")
    print("📊 验证结果汇总:")
    
    print(f"\n🔧 通道变换策略测试:")
    for strategy, result in transformer_results.items():
        if result['success']:
            status = "✅ 无增强" if not result['has_augmentation'] else "⚠️ 有增强"
            print(f"  {strategy}: {status}")
        else:
            print(f"  {strategy}: ❌ 失败")
    
    print(f"\n📋 配置文件验证:")
    if config_verification['success']:
        print("  ✅ 配置正确，所有增强已禁用")
    else:
        print("  ❌ 配置存在问题:")
        for issue in config_verification['issues']:
            print(f"    - {issue}")
    
    print(f"\n📦 数据集加载测试:")
    if dataset_result['success']:
        if not dataset_result['has_augmentation']:
            print("  ✅ 数据集加载无增强")
        else:
            print("  ⚠️ 数据集加载仍有增强")
    else:
        print(f"  ❌ 测试失败: {dataset_result.get('error', 'Unknown error')}")
    
    # Overall assessment
    overall_success = (
        config_verification['success'] and
        dataset_result['success'] and
        not dataset_result.get('has_augmentation', True) and
        transformer_results.get('replicate', {}).get('success', False) and
        not transformer_results.get('replicate', {}).get('has_augmentation', True)
    )
    
    print(f"\n🎯 总体评估:")
    if overall_success:
        print("✅ 数据增强已完全禁用，可以进行基准训练")
    else:
        print("❌ 数据增强未完全禁用，需要进一步修正")
    
    sys.exit(0 if overall_success else 1)
