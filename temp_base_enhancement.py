"""
BaseEnhancementModule - 增强模块的抽象基类和通用接口
为所有增强模块提供统一的配置接口和模态感知控制

用途: 脉冲星分类系统的增强模块基础架构
"""

import torch
import torch.nn as nn
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, Union


class BaseEnhancementModule(nn.Module, ABC):
    """
    增强模块抽象基类
    
    定义所有增强模块的通用接口和行为：
    - 统一的配置参数接口
    - 模态感知的激活控制
    - 参数验证和错误处理机制
    - 性能监控接口
    
    Args:
        enabled: 是否启用模块
        modality: 支持的模态 ('FPP', 'TPP', 'BOTH')
        config: 模块特定配置
    """
    
    def __init__(self, enabled: bool = True, modality: str = 'BOTH', config: Optional[Dict[str, Any]] = None):
        super().__init__()
        self.enabled = enabled
        self.modality = modality.upper()
        self.config = config or {}
        
        # 验证模态参数
        assert self.modality in ['FPP', 'TPP', 'BOTH'], \
            f"Modality must be 'FPP', 'TPP', or 'BOTH', got {self.modality}"
        
        # 性能监控
        self.register_buffer('forward_count', torch.tensor(0, dtype=torch.long))
        self.register_buffer('active_count', torch.tensor(0, dtype=torch.long))
    
    @abstractmethod
    def _forward_impl(self, x: torch.Tensor, **kwargs) -> torch.Tensor:
        """
        模块的具体实现，由子类重写
        
        Args:
            x: 输入张量
            **kwargs: 额外参数
            
        Returns:
            enhanced_x: 增强后的张量
        """
        pass
    
    def forward(self, x: torch.Tensor, current_modality: str = 'BOTH', **kwargs) -> torch.Tensor:
        """
        统一的前向传播接口
        
        Args:
            x: 输入张量
            current_modality: 当前数据模态
            **kwargs: 额外参数
            
        Returns:
            enhanced_x: 增强后的张量（如果模块激活）或原始张量（如果模块未激活）
        """
        self.forward_count += 1
        
        # 检查模块是否应该激活
        if not self.should_activate(current_modality):
            return x
        
        self.active_count += 1
        
        # 执行具体的增强逻辑
        return self._forward_impl(x, **kwargs)
    
    def should_activate(self, current_modality: str) -> bool:
        """
        判断模块是否应该在当前模态下激活
        
        Args:
            current_modality: 当前数据模态
            
        Returns:
            bool: 是否应该激活
        """
        if not self.enabled:
            return False
        
        current_modality = current_modality.upper()
        
        if self.modality == 'BOTH':
            return True
        
        return self.modality == current_modality
    
    def enable(self):
        """启用模块"""
        self.enabled = True
    
    def disable(self):
        """禁用模块"""
        self.enabled = False
    
    def set_modality(self, modality: str):
        """设置支持的模态"""
        modality = modality.upper()
        assert modality in ['FPP', 'TPP', 'BOTH'], \
            f"Modality must be 'FPP', 'TPP', or 'BOTH', got {modality}"
        self.modality = modality
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取模块统计信息
        
        Returns:
            dict: 包含模块统计信息的字典
        """
        total_forwards = self.forward_count.item()
        active_forwards = self.active_count.item()
        
        return {
            'enabled': self.enabled,
            'modality': self.modality,
            'total_forwards': total_forwards,
            'active_forwards': active_forwards,
            'activation_rate': active_forwards / max(total_forwards, 1),
            'parameter_count': sum(p.numel() for p in self.parameters() if p.requires_grad)
        }
    
    def reset_stats(self):
        """重置统计信息"""
        self.forward_count.zero_()
        self.active_count.zero_()
    
    def validate_config(self, config: Dict[str, Any]) -> bool:
        """
        验证配置参数（由子类重写以添加特定验证）
        
        Args:
            config: 配置字典
            
        Returns:
            bool: 配置是否有效
        """
        return True
    
    def update_config(self, config: Dict[str, Any]):
        """
        更新配置参数
        
        Args:
            config: 新的配置字典
        """
        if self.validate_config(config):
            self.config.update(config)
        else:
            raise ValueError("Invalid configuration provided")


class EnhancementModuleFactory:
    """
    增强模块工厂类
    
    负责创建和管理各种增强模块实例
    """
    
    @staticmethod
    def create_module(module_type: str, config: Dict[str, Any]) -> BaseEnhancementModule:
        """
        创建指定类型的增强模块
        
        Args:
            module_type: 模块类型 ('fft', 'freq_fusion', 'cbam', 'cf_loss')
            config: 模块配置
            
        Returns:
            BaseEnhancementModule: 创建的模块实例
        """
        module_type = module_type.lower()
        
        if module_type == 'fft':
            from .fft_block_2d import FFTNetBlock2D
            return FFTNetBlock2D(**config)
        elif module_type == 'freq_fusion':
            from .freq_fusion_simplified import SimplifiedFreqFusion
            return SimplifiedFreqFusion(**config)
        elif module_type == 'cbam':
            from .cbam import CBAM
            return CBAM(**config)
        elif module_type == 'cf_loss':
            from .cf_loss_2d import CF_Loss_2D
            return CF_Loss_2D(**config)
        else:
            raise ValueError(f"Unknown module type: {module_type}")
    
    @staticmethod
    def validate_module_config(module_type: str, config: Dict[str, Any]) -> bool:
        """
        验证模块配置的有效性
        
        Args:
            module_type: 模块类型
            config: 配置字典
            
        Returns:
            bool: 配置是否有效
        """
        module_type = module_type.lower()
        
        # 基本配置验证
        required_keys = {
            'fft': ['dim'],
            'freq_fusion': ['hr_channels', 'lr_channels'],
            'cbam': ['in_planes'],
            'cf_loss': ['beta', 'alpha', 'gamma']
        }
        
        if module_type not in required_keys:
            return False
        
        # 检查必需的键
        for key in required_keys[module_type]:
            if key not in config:
                return False
        
        return True


# 工具函数
def create_enhancement_config(
    fft_config: Optional[Dict[str, Any]] = None,
    freq_fusion_config: Optional[Dict[str, Any]] = None,
    cbam_config: Optional[Dict[str, Any]] = None,
    cf_loss_config: Optional[Dict[str, Any]] = None,
    modality: str = 'BOTH'
) -> Dict[str, Any]:
    """
    创建完整的增强模块配置
    
    Args:
        fft_config: FFT模块配置
        freq_fusion_config: 频域融合模块配置
        cbam_config: CBAM模块配置
        cf_loss_config: CF损失模块配置
        modality: 数据模态
        
    Returns:
        dict: 完整的配置字典
    """
    config = {
        'modality': modality,
        'modules': {}
    }
    
    if fft_config:
        config['modules']['fft'] = fft_config
    
    if freq_fusion_config:
        config['modules']['freq_fusion'] = freq_fusion_config
    
    if cbam_config:
        config['modules']['cbam'] = cbam_config
    
    if cf_loss_config:
        config['modules']['cf_loss'] = cf_loss_config
    
    return config


# 测试代码
if __name__ == '__main__':
    # 测试基础接口
    print("Testing BaseEnhancementModule interface...")
    
    # 创建配置
    config = create_enhancement_config(
        fft_config={'dim': 256},
        cbam_config={'in_planes': 256, 'ratio': 16},
        modality='FPP'
    )
    
    print(f"Created config: {config}")
    
    # 验证配置
    for module_type, module_config in config['modules'].items():
        is_valid = EnhancementModuleFactory.validate_module_config(module_type, module_config)
        print(f"{module_type} config valid: {is_valid}")
    
    print("BaseEnhancementModule interface created successfully!")
