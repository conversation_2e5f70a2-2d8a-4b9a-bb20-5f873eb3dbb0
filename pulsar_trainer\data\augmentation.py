"""
独立的数据增强模块，完全配置驱动

This module provides configuration-driven data augmentation for pulsar classification.
All augmentation parameters are controlled through configuration files.
"""

import numpy as np
from typing import Optional, List
from scipy import ndimage
import logging


class DataAugmentation:
    """独立的数据增强类，完全配置驱动"""
    
    def __init__(self, config):
        """
        初始化数据增强器
        
        Args:
            config: AugmentationConfig实例
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        
    def apply_augmentation(self, data: np.ndarray, modality: str) -> np.ndarray:
        """应用数据增强，基于配置参数"""
        if not self.config.enabled:
            return data
            
        augmented_data = data.copy()
        
        # 相位偏移
        if (self.config.phase_shift["enabled"] and 
            np.random.random() < self.config.phase_shift["probability"]):
            augmented_data = self._phase_shift_data(
                augmented_data, 
                self.config.phase_shift["max_shift"]
            )
        
        # 模态特定增强
        if modality == "FPP" and self.config.frequency_shift["enabled"]:
            if np.random.random() < self.config.frequency_shift["probability"]:
                augmented_data = self._frequency_shift_data(
                    augmented_data,
                    self.config.frequency_shift["max_shift"]
                )
        elif modality == "TPP" and self.config.time_shift["enabled"]:
            if np.random.random() < self.config.time_shift["probability"]:
                augmented_data = self._time_shift_data(
                    augmented_data,
                    self.config.time_shift["max_shift"]
                )
        
        # 亮度缩放
        if (self.config.brightness_scaling["enabled"] and
            np.random.random() < self.config.brightness_scaling["probability"]):
            augmented_data = self._brightness_scale_data(
                augmented_data,
                self.config.brightness_scaling["scale_range"]
            )
        
        # 噪声
        if (self.config.noise["enabled"] and
            np.random.random() < self.config.noise["probability"]):
            augmented_data = self._add_noise(
                augmented_data,
                self.config.noise["noise_level"]
            )
        
        return augmented_data
    
    def _phase_shift_data(self, data: np.ndarray, max_shift: float) -> np.ndarray:
        """
        应用相位循环偏移到脉冲星数据
        
        这是脉冲星数据最重要的物理合理增强。
        相位是周期性的(0-1)，所以循环偏移保持了脉冲星信号的物理意义。
        
        Args:
            data: 输入2D数据 (64, 64)
            max_shift: 最大偏移量作为相位维度的分数
            
        Returns:
            相位偏移后的数据
        """
        # 随机选择偏移量
        shift_fraction = np.random.uniform(-max_shift, max_shift)
        
        # 假设相位维度是第二个维度（列）
        phase_dim_size = data.shape[1]
        shift_pixels = int(shift_fraction * phase_dim_size)
        
        # 应用循环偏移
        shifted_data = np.roll(data, shift_pixels, axis=1)
        
        return shifted_data
    
    def _frequency_shift_data(self, data: np.ndarray, max_shift: float) -> np.ndarray:
        """
        应用频率偏移到FPP数据
        
        Args:
            data: 输入2D数据 (64, 64)
            max_shift: 最大偏移量
            
        Returns:
            频率偏移后的数据
        """
        # 随机选择偏移量
        shift_fraction = np.random.uniform(-max_shift, max_shift)
        
        # 假设频率维度是第一个维度（行）
        freq_dim_size = data.shape[0]
        shift_pixels = int(shift_fraction * freq_dim_size)
        
        # 应用循环偏移
        shifted_data = np.roll(data, shift_pixels, axis=0)
        
        return shifted_data
    
    def _time_shift_data(self, data: np.ndarray, max_shift: float) -> np.ndarray:
        """
        应用时间偏移到TPP数据
        
        Args:
            data: 输入2D数据 (64, 64)
            max_shift: 最大偏移量
            
        Returns:
            时间偏移后的数据
        """
        # 随机选择偏移量
        shift_fraction = np.random.uniform(-max_shift, max_shift)
        
        # 假设时间维度是第一个维度（行）
        time_dim_size = data.shape[0]
        shift_pixels = int(shift_fraction * time_dim_size)
        
        # 应用循环偏移
        shifted_data = np.roll(data, shift_pixels, axis=0)
        
        return shifted_data
    
    def _brightness_scale_data(self, data: np.ndarray, scale_range: List[float]) -> np.ndarray:
        """
        应用亮度缩放
        
        Args:
            data: 输入2D数据 (64, 64)
            scale_range: 缩放范围 [min_scale, max_scale]
            
        Returns:
            亮度缩放后的数据，裁剪到[0, 1]范围
        """
        # 随机缩放因子
        scale_factor = np.random.uniform(scale_range[0], scale_range[1])
        
        # 应用缩放并裁剪到有效范围
        scaled_data = data * scale_factor
        scaled_data = np.clip(scaled_data, 0, 1)
        
        return scaled_data
    
    def _add_noise(self, data: np.ndarray, noise_level: float) -> np.ndarray:
        """
        添加高斯噪声
        
        Args:
            data: 输入2D数据 (64, 64)
            noise_level: 噪声标准差
            
        Returns:
            添加噪声后的数据
        """
        # 生成高斯噪声
        noise = np.random.normal(0, noise_level, data.shape)
        
        # 添加噪声并裁剪到有效范围
        noisy_data = data + noise
        noisy_data = np.clip(noisy_data, 0, 1)
        
        return noisy_data
    
    def get_augmentation_info(self) -> dict:
        """获取当前增强配置信息"""
        return {
            'enabled': self.config.enabled,
            'phase_shift': self.config.phase_shift,
            'frequency_shift': self.config.frequency_shift,
            'time_shift': self.config.time_shift,
            'brightness_scaling': self.config.brightness_scaling,
            'noise': self.config.noise
        }
