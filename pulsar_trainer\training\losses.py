"""
Advanced loss functions for high-precision pulsar classification.

This module implements specialized loss functions designed to achieve
99.7% accuracy in pulsar classification tasks.

Version 3.0.0: Added Stage 3 advanced loss functions for recall protection
and precision enhancement.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import Optional, Dict, Any


class LabelSmoothingCrossEntropy(nn.Module):
    """
    Label Smoothing Cross Entropy Loss.

    Implements label smoothing to improve model calibration and generalization.
    Particularly effective for achieving high precision targets (99.7%).

    Args:
        smoothing (float): Label smoothing factor (default: 0.1)
        reduction (str): Reduction method ('mean', 'sum', 'none')
    """

    def __init__(self, smoothing: float = 0.1, reduction: str = 'mean'):
        super().__init__()
        self.smoothing = smoothing
        self.reduction = reduction

        if not 0.0 <= smoothing <= 1.0:
            raise ValueError(f"Smoothing must be in [0, 1], got {smoothing}")

    def forward(self, pred: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        """
        Forward pass of label smoothing cross entropy.

        Args:
            pred: Predictions with shape (batch_size, num_classes)
            target: Ground truth labels with shape (batch_size,)

        Returns:
            Loss tensor
        """
        confidence = 1.0 - self.smoothing
        log_probs = F.log_softmax(pred, dim=-1)

        # Negative log likelihood for true class
        nll_loss = -log_probs.gather(dim=-1, index=target.unsqueeze(1))
        nll_loss = nll_loss.squeeze(1)

        # Smooth loss (uniform distribution over all classes)
        smooth_loss = -log_probs.mean(dim=-1)

        # Combine losses
        loss = confidence * nll_loss + self.smoothing * smooth_loss

        if self.reduction == 'mean':
            return loss.mean()
        elif self.reduction == 'sum':
            return loss.sum()
        else:
            return loss


class FocalLoss(nn.Module):
    """
    Focal Loss for addressing class imbalance.

    Implements the focal loss from "Focal Loss for Dense Object Detection"
    to handle class imbalance in pulsar classification.

    Args:
        alpha (float): Weighting factor for rare class (default: 1.0)
        gamma (float): Focusing parameter (default: 2.0)
        reduction (str): Reduction method ('mean', 'sum', 'none')
    """

    def __init__(self, alpha: float = 1.0, gamma: float = 2.0, reduction: str = 'mean'):
        super().__init__()
        self.alpha = alpha
        self.gamma = gamma
        self.reduction = reduction

    def forward(self, pred: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        """
        Forward pass of focal loss.

        Args:
            pred: Predictions with shape (batch_size, num_classes)
            target: Ground truth labels with shape (batch_size,)

        Returns:
            Loss tensor
        """
        # Compute cross entropy
        ce_loss = F.cross_entropy(pred, target, reduction='none')

        # Compute p_t
        pt = torch.exp(-ce_loss)

        # Compute focal loss
        focal_loss = self.alpha * (1 - pt) ** self.gamma * ce_loss

        if self.reduction == 'mean':
            return focal_loss.mean()
        elif self.reduction == 'sum':
            return focal_loss.sum()
        else:
            return focal_loss


class WeightedCrossEntropyLoss(nn.Module):
    """
    Weighted Cross Entropy Loss for class imbalance.

    Applies class weights to handle imbalanced datasets common in
    pulsar classification.

    Args:
        weights (torch.Tensor): Class weights
        reduction (str): Reduction method ('mean', 'sum', 'none')
    """

    def __init__(self, weights: torch.Tensor = None, reduction: str = 'mean'):
        super().__init__()
        self.weights = weights
        self.reduction = reduction

    def forward(self, pred: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        """
        Forward pass of weighted cross entropy.

        Args:
            pred: Predictions with shape (batch_size, num_classes)
            target: Ground truth labels with shape (batch_size,)

        Returns:
            Loss tensor
        """
        return F.cross_entropy(pred, target, weight=self.weights, reduction=self.reduction)


class CombinedLoss(nn.Module):
    """
    Combined loss function for multi-objective optimization.

    Combines multiple loss functions with configurable weights to achieve
    optimal performance across multiple metrics.

    Args:
        losses (dict): Dictionary of loss functions and their weights
    """

    def __init__(self, losses: dict):
        super().__init__()
        self.losses = nn.ModuleDict()
        self.weights = {}

        for name, (loss_fn, weight) in losses.items():
            self.losses[name] = loss_fn
            self.weights[name] = weight

    def forward(self, pred: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        """
        Forward pass of combined loss.

        Args:
            pred: Predictions with shape (batch_size, num_classes)
            target: Ground truth labels with shape (batch_size,)

        Returns:
            Combined loss tensor
        """
        total_loss = 0.0

        for name, loss_fn in self.losses.items():
            loss_value = loss_fn(pred, target)
            total_loss += self.weights[name] * loss_value

        return total_loss


def create_loss_function(loss_config: dict) -> nn.Module:
    """
    Factory function to create loss functions based on configuration.

    Args:
        loss_config: Configuration dictionary for loss function

    Returns:
        Configured loss function
    """
    loss_type = loss_config.get('type', 'cross_entropy')

    if loss_type == 'cross_entropy':
        return nn.CrossEntropyLoss()

    elif loss_type == 'label_smoothing':
        smoothing = loss_config.get('smoothing', 0.1)
        return LabelSmoothingCrossEntropy(smoothing=smoothing)

    elif loss_type == 'focal':
        alpha = loss_config.get('alpha', 1.0)
        gamma = loss_config.get('gamma', 2.0)
        return FocalLoss(alpha=alpha, gamma=gamma)

    elif loss_type == 'weighted':
        weights = loss_config.get('weights', None)
        if weights is not None:
            weights = torch.tensor(weights, dtype=torch.float32)
        return WeightedCrossEntropyLoss(weights=weights)

    elif loss_type == 'combined':
        loss_configs = loss_config.get('losses', {})
        losses = {}
        for name, config in loss_configs.items():
            loss_fn = create_loss_function(config['config'])
            weight = config.get('weight', 1.0)
            losses[name] = (loss_fn, weight)
        return CombinedLoss(losses)

    else:
        raise ValueError(f"Unknown loss type: {loss_type}")


class RecallProtectionLoss(nn.Module):
    """
    Stage 3: Recall Protection Loss Function
    Specialized loss to maintain 100% recall while improving precision
    """
    def __init__(self, false_negative_weight: float = 10.0, temperature: float = 1.0):
        super().__init__()
        self.false_negative_weight = false_negative_weight
        self.temperature = temperature

    def forward(self, logits: torch.Tensor, targets: torch.Tensor) -> torch.Tensor:
        # Calculate standard cross entropy
        ce_loss = F.cross_entropy(logits / self.temperature, targets, reduction='none')

        # Calculate prediction probabilities
        probs = F.softmax(logits / self.temperature, dim=1)
        pred_classes = torch.argmax(probs, dim=1)

        # Identify false negatives (true=1, pred=0)
        false_negatives = (targets == 1) & (pred_classes == 0)

        # Apply higher weight to false negatives
        weights = torch.ones_like(ce_loss)
        weights[false_negatives] = self.false_negative_weight

        weighted_loss = ce_loss * weights
        return weighted_loss.mean()


class PrecisionEnhancementLoss(nn.Module):
    """
    Stage 3: Precision Enhancement Loss Function
    Specialized loss to reduce false positives and improve precision
    """
    def __init__(self, false_positive_weight: float = 5.0, margin: float = 0.1):
        super().__init__()
        self.false_positive_weight = false_positive_weight
        self.margin = margin

    def forward(self, logits: torch.Tensor, targets: torch.Tensor) -> torch.Tensor:
        # Calculate prediction probabilities
        probs = F.softmax(logits, dim=1)
        pred_classes = torch.argmax(probs, dim=1)

        # Identify false positives (true=0, pred=1)
        false_positives = (targets == 0) & (pred_classes == 1)

        # Base cross entropy loss
        base_loss = F.cross_entropy(logits, targets, reduction='none')

        # Additional penalty for false positives
        fp_penalty = torch.zeros_like(base_loss)
        if false_positives.any():
            positive_probs = probs[false_positives, 1]
            fp_penalty[false_positives] = F.relu(positive_probs - self.margin) * self.false_positive_weight

        total_loss = base_loss + fp_penalty
        return total_loss.mean()


class Stage3AdaptiveLoss(nn.Module):
    """
    Stage 3: Adaptive Loss Combination
    Combines recall protection and precision enhancement losses
    """
    def __init__(self,
                 recall_weight: float = 3.0,
                 precision_weight: float = 2.0,
                 focal_weight: float = 1.0,
                 adaptation_rate: float = 0.01):
        super().__init__()

        # Loss components
        self.recall_loss = RecallProtectionLoss()
        self.precision_loss = PrecisionEnhancementLoss()
        self.focal_loss = FocalLoss(alpha=0.75, gamma=2.0)

        # Adaptive weights
        self.register_parameter('recall_weight', nn.Parameter(torch.tensor(recall_weight)))
        self.register_parameter('precision_weight', nn.Parameter(torch.tensor(precision_weight)))
        self.register_parameter('focal_weight', nn.Parameter(torch.tensor(focal_weight)))

        self.adaptation_rate = adaptation_rate

        # Performance tracking
        self.register_buffer('recall_history', torch.zeros(50))
        self.register_buffer('precision_history', torch.zeros(50))
        self.register_buffer('step_count', torch.tensor(0))

    def forward(self, logits: torch.Tensor, targets: torch.Tensor) -> Dict[str, torch.Tensor]:
        # Calculate individual loss components
        recall_loss = self.recall_loss(logits, targets)
        precision_loss = self.precision_loss(logits, targets)
        focal_loss = self.focal_loss(logits, targets)

        # Apply adaptive weights
        weighted_recall = torch.abs(self.recall_weight) * recall_loss
        weighted_precision = torch.abs(self.precision_weight) * precision_loss
        weighted_focal = torch.abs(self.focal_weight) * focal_loss

        # Total loss
        total_loss = weighted_recall + weighted_precision + weighted_focal

        return {
            'total_loss': total_loss,
            'recall_loss': recall_loss,
            'precision_loss': precision_loss,
            'focal_loss': focal_loss,
            'weighted_recall': weighted_recall,
            'weighted_precision': weighted_precision,
            'weighted_focal': weighted_focal
        }

    def update_weights(self, current_recall: float, current_precision: float):
        """Update adaptive weights based on current performance"""
        step = self.step_count.item() % 50
        self.recall_history[step] = current_recall
        self.precision_history[step] = current_precision
        self.step_count += 1

        if self.step_count > 10:
            # Calculate recent performance
            recent_recall = self.recall_history[max(0, step-5):step].mean()
            recent_precision = self.precision_history[max(0, step-5):step].mean()

            # Adjust weights based on performance trends
            if current_recall < recent_recall:
                self.recall_weight.data += self.adaptation_rate
            if current_precision < recent_precision:
                self.precision_weight.data += self.adaptation_rate

            # Prevent excessive weight growth
            self.recall_weight.data *= 0.999
            self.precision_weight.data *= 0.999
            self.focal_weight.data *= 0.999


# Loss function registry for easy access
LOSS_REGISTRY = {
    'cross_entropy': nn.CrossEntropyLoss,
    'label_smoothing': LabelSmoothingCrossEntropy,
    'focal': FocalLoss,
    'weighted': WeightedCrossEntropyLoss,
    'combined': CombinedLoss,
    'recall_protection': RecallProtectionLoss,
    'precision_enhancement': PrecisionEnhancementLoss,
    'stage3_adaptive': Stage3AdaptiveLoss
}


def get_available_losses() -> list:
    """Get list of available loss functions."""
    return list(LOSS_REGISTRY.keys())


def create_loss_function(config: Dict[str, Any]) -> nn.Module:
    """
    Create loss function from configuration.

    Args:
        config: Loss function configuration

    Returns:
        Loss function instance
    """
    loss_type = config.get('type', 'cross_entropy')
    loss_params = config.get('params', {})

    if loss_type not in LOSS_REGISTRY:
        raise ValueError(f"Unknown loss type: {loss_type}. Available: {get_available_losses()}")

    loss_class = LOSS_REGISTRY[loss_type]

    # Handle special cases for different loss functions
    if loss_type == 'stage3_adaptive':
        return loss_class(
            recall_weight=loss_params.get('recall_weight', 3.0),
            precision_weight=loss_params.get('precision_weight', 2.0),
            focal_weight=loss_params.get('focal_weight', 1.0),
            adaptation_rate=loss_params.get('adaptation_rate', 0.01)
        )
    elif loss_type == 'recall_protection':
        return loss_class(
            false_negative_weight=loss_params.get('false_negative_weight', 10.0),
            temperature=loss_params.get('temperature', 1.0)
        )
    elif loss_type == 'precision_enhancement':
        return loss_class(
            false_positive_weight=loss_params.get('false_positive_weight', 5.0),
            margin=loss_params.get('margin', 0.1)
        )
    else:
        # For standard loss functions, pass all parameters
        try:
            return loss_class(**loss_params)
        except TypeError:
            # If parameters don't match, create with defaults
            return loss_class()
