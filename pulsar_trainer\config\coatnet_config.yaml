# CoAtNet Pulsar Classification Configuration - Standardized Architecture
# Version 3.0.0 - Complete Configuration-Driven System

# 项目信息
project:
  name: "pulsar-coatnet"
  version: "3.0.0"
  description: "Standardized CoAtNet-based pulsar classification system"

# 数据配置
data:
  modality: "FPP"  # 可配置为FPP或TPP
  data_root: "D:/pulsarSuanfa/datasets/HTRU"
  normalize: true
  num_workers: 4
  pin_memory: true

# 模型配置（统一单通道架构）
model:
  name: "coatnet"
  coatnet:
    num_blocks: [2, 2, 6, 8, 2]
    channels: [96, 128, 256, 512, 1024]
    block_types: ['C', 'C', 'T', 'T']
    image_size: [64, 64]
    in_channels: 1  # 统一单通道输入
    num_classes: 2
    dropout: 0.2

# 数据增强配置（完全配置驱动）
augmentation:
  enabled: true  # 主控开关
  phase_shift:
    enabled: true
    probability: 0.7
    max_shift: 1.0
  frequency_shift:
    enabled: true
    probability: 0.3
    max_shift: 0.1
  time_shift:
    enabled: true
    probability: 0.3
    max_shift: 0.1
  brightness_scaling:
    enabled: true
    probability: 0.5
    scale_range: [0.8, 1.2]
  noise:
    enabled: true
    probability: 0.4
    noise_level: 0.01

# 训练配置
training:
  batch_size: 32
  epochs: 100
  learning_rate: 0.001
  weight_decay: 0.01
  optimizer: "adamw"
  scheduler:
    type: "cosine"
    warmup_epochs: 10
    min_lr: 0.00001
  early_stopping:
    patience: 15
    min_delta: 0.001
  loss:
    type: "cross_entropy"
    smoothing: 0.1

# 设备配置
device:
  use_cuda: true
  device_id: 0
  compile_model: false
  benchmark: true
  deterministic: false

# 输出配置
output:
  base_dir: "outputs"
  save_best_model: true
  save_last_model: true
  save_optimizer: true
  log_interval: 10
  eval_interval: 1

# 日志配置
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  save_to_file: true
  console_output: true

# 评估配置
evaluation:
  metrics:
    - "accuracy"
    - "precision"
    - "recall"
    - "f1_score"
    - "auc_roc"
    - "confusion_matrix"
  save_predictions: true
  save_probabilities: true
  generate_plots: true
  misclassification_analysis: true

# 增强模块配置 - 新增功能
enhancement_modules:
  enabled: true
  modality_aware: true

  # FPP增强模块配置
  fpp:
    enabled: true
    # 通道数会在CoAtNet中自动配置，无需手动设置
    # FFT维度会自动匹配通道数
    freq_fusion_config:
      lowpass_kernel: 5
      highpass_kernel: 3
      compressed_channels: 24  # 相对于输入通道数的压缩比例
      use_high_pass: true
      use_low_pass: true
      hamming_window: true

  # TPP增强模块配置
  tpp:
    enabled: true
    # 通道数会在CoAtNet中自动配置，无需手动设置
    cbam_config:
      ratio: 16
      kernel_size: 7
    loss_config:
      beta: 1.0    # CE损失权重
      alpha: 0.5   # 频域损失权重
      gamma: 0.3   # 体积差异损失权重

# 性能目标配置（增强后）
performance_targets:
  accuracy: 1.0      # 目标100%准确率
  precision: 1.0     # 目标100%精确率
  recall: 1.0        # 目标100%召回率
  f1_score: 1.0      # 目标100% F1分数
  fpp_specific:      # FPP数据集特定目标
    maintain_recall: 1.0    # 保持100%召回率
    improve_precision: 0.99 # 提升精确率至99%+
  tpp_specific:      # TPP数据集特定目标
    improve_all_metrics: 0.99  # 所有指标提升至99%+

# 模块控制开关
module_control:
  fpp_enhancement:
    fft_processing: true
    freq_fusion: true
    stage_s2_only: true
  tpp_enhancement:
    cbam_attention: true
    enhanced_loss: true
    stage_s3_only: true  # 只在s3阶段激活，避免s4通道数不匹配
  fallback_mode: false  # 是否启用回退模式
  performance_monitoring: true
  auto_channel_config: true  # 自动配置通道数

# 训练策略配置（增强版）
training_strategy:
  enhanced_loss_weight: 0.3  # 增强损失的权重
  standard_loss_weight: 0.7  # 标准损失的权重
  loss_combination: "weighted"  # 损失组合方式：weighted, adaptive, scheduled
  enhancement_warmup_epochs: 5  # 增强模块预热轮数

# 验证和测试配置
validation:
  enhanced_metrics: true  # 启用增强指标计算
  loss_component_tracking: true  # 跟踪损失组件
  modality_specific_eval: true  # 模态特定评估

# 调试和监控配置
debug:
  enhancement_stats: true  # 增强模块统计
  channel_mismatch_check: true  # 通道数匹配检查
  forward_pass_timing: false  # 前向传播计时
  memory_usage_tracking: false  # 内存使用跟踪


