2025-07-31 17:15:03 - pulsar_trainer - INFO - <PERSON><PERSON> initialized. Log file: D:\pulsarSuanfa\outputs_FPP\logs\pulsar_trainer_20250731_171503.log
2025-07-31 17:15:03 - pulsar_trainer - INFO - 随机种子设置为: 42
2025-07-31 17:15:03 - pulsar_trainer - INFO - 使用配置文件参数创建模型
2025-07-31 17:15:03 - pulsar_trainer - INFO - 增强模块配置已加载: FPP=True, TPP=True
2025-07-31 17:15:03 - pulsar_trainer - INFO - 模型配置 - num_blocks: [2, 2, 6, 8, 2]
2025-07-31 17:15:03 - pulsar_trainer - INFO - 模型配置 - channels: [96, 128, 256, 512, 1024]
2025-07-31 17:15:03 - pulsar_trainer - INFO - 模型配置 - in_channels: 1
2025-07-31 17:15:03 - pulsar_trainer - INFO - ✓ 增强模块已启用
2025-07-31 17:15:03 - pulsar_trainer - INFO -   - FPP模块可用: True
2025-07-31 17:15:03 - pulsar_trainer - INFO -   - TPP模块可用: True
2025-07-31 17:15:03 - pulsar_trainer - INFO -   - 当前模态: AUTO
2025-07-31 17:15:03 - pulsar_trainer - INFO - 模型架构详情:
2025-07-31 17:15:03 - pulsar_trainer - INFO -   总参数数量: 38,860,338
2025-07-31 17:15:03 - pulsar_trainer - INFO -   可训练参数: 38,860,338
2025-07-31 17:15:03 - pulsar_trainer - INFO -   模型大小: 148.24 MB
2025-07-31 17:15:03 - pulsar_trainer - INFO -   块类型配置: ['C', 'C', 'T', 'T']
2025-07-31 17:15:03 - pulsar_trainer - INFO -   各阶段块数: [2, 2, 6, 8, 2]
2025-07-31 17:15:03 - pulsar_trainer - INFO -   各阶段通道数: [96, 128, 256, 512, 1024]
2025-07-31 17:15:03 - pulsar_trainer - INFO - Using standard cross entropy loss
2025-07-31 17:15:03 - pulsar_trainer - INFO - 🖥️ 使用GPU: NVIDIA GeForce RTX 4050 Laptop GPU
2025-07-31 17:15:03 - pulsar_trainer - INFO - 💾 GPU内存: 6.4GB
2025-07-31 17:15:03 - pulsar_trainer - INFO - 📋 模型参数总数: 38,860,338
2025-07-31 17:15:03 - pulsar_trainer - INFO - 🏗️ 模型架构: CoAtNet
2025-07-31 17:15:03 - pulsar_trainer - INFO - 📚 数据集大小:
2025-07-31 17:15:03 - pulsar_trainer - INFO -   - 训练集: 1674
2025-07-31 17:15:03 - pulsar_trainer - INFO -   - 验证集: 360
2025-07-31 17:15:03 - pulsar_trainer - INFO -   - 测试集: 358
2025-07-31 17:15:03 - pulsar_trainer - INFO -   - 总计: 2392
2025-07-31 17:15:03 - pulsar_trainer - INFO - ============================================================
2025-07-31 17:15:03 - pulsar_trainer - INFO - 🚀 开始脉冲星分类训练
2025-07-31 17:15:03 - pulsar_trainer - INFO - 📊 模态: FPP
2025-07-31 17:15:03 - pulsar_trainer - INFO - 🧠 模型: coatnet
2025-07-31 17:15:03 - pulsar_trainer - INFO - 📦 批次大小: 32
2025-07-31 17:15:03 - pulsar_trainer - INFO - 🎯 学习率: 0.001
2025-07-31 17:15:03 - pulsar_trainer - INFO - 🔄 训练轮数: 100
2025-07-31 17:15:03 - pulsar_trainer - INFO - ============================================================
2025-07-31 17:15:26 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.689093291759491, 'fd_loss': 0.49637365341186523, 'vd_loss': 0.06563931703567505, 'confidence_penalty': 0.0, 'total_loss': 0.9569718837738037}
2025-07-31 17:15:54 - pulsar_trainer - INFO - Epoch   1: Train Loss=2.5288, Val Loss=0.7854, Train Acc=70.91%, Val Acc=91.11%
2025-07-31 17:15:54 - pulsar_trainer - INFO - Validation F1: 91.0836, Precision: 91.6250, Recall: 91.1111
2025-07-31 17:15:54 - pulsar_trainer - INFO - 模型已保存: D:\pulsarSuanfa\outputs_FPP\models\best_model.pth
2025-07-31 17:15:54 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 1.3063637018203735, 'fd_loss': 0.1424810290336609, 'vd_loss': 0.28442972898483276, 'confidence_penalty': 0.004976991098374128, 'total_loss': 1.4679101705551147}
2025-07-31 17:15:59 - pulsar_trainer - INFO - Epoch   2: Train Loss=0.2563, Val Loss=0.0961, Train Acc=92.83%, Val Acc=97.22%
2025-07-31 17:15:59 - pulsar_trainer - INFO - Validation F1: 97.2219, Precision: 97.2456, Recall: 97.2222
2025-07-31 17:16:00 - pulsar_trainer - INFO - 模型已保存: D:\pulsarSuanfa\outputs_FPP\models\best_model.pth
2025-07-31 17:16:00 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.03886809200048447, 'fd_loss': 0.03577982634305954, 'vd_loss': 0.03590285778045654, 'confidence_penalty': 0.003635450964793563, 'total_loss': 0.07116430997848511}
2025-07-31 17:16:05 - pulsar_trainer - INFO - Epoch   3: Train Loss=0.1720, Val Loss=0.4532, Train Acc=95.64%, Val Acc=82.78%
2025-07-31 17:16:05 - pulsar_trainer - INFO - Validation F1: 82.3188, Precision: 86.5753, Recall: 82.7778
2025-07-31 17:16:05 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.07102109491825104, 'fd_loss': 0.04607713967561722, 'vd_loss': 0.023865818977355957, 'confidence_penalty': 0.004390276037156582, 'total_loss': 0.10560968518257141}
2025-07-31 17:16:10 - pulsar_trainer - INFO - Epoch   4: Train Loss=0.1489, Val Loss=0.1018, Train Acc=95.82%, Val Acc=97.78%
2025-07-31 17:16:10 - pulsar_trainer - INFO - Validation F1: 97.7777, Precision: 97.7837, Recall: 97.7778
2025-07-31 17:16:10 - pulsar_trainer - INFO - 模型已保存: D:\pulsarSuanfa\outputs_FPP\models\best_model.pth
2025-07-31 17:16:10 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.004385703708976507, 'fd_loss': 0.0043248748406767845, 'vd_loss': 0.0060894787311553955, 'confidence_penalty': 0.004567514639347792, 'total_loss': 0.012942500412464142}
2025-07-31 17:16:15 - pulsar_trainer - INFO - Epoch   5: Train Loss=0.0992, Val Loss=0.0893, Train Acc=97.61%, Val Acc=97.78%
2025-07-31 17:16:15 - pulsar_trainer - INFO - Validation F1: 97.7777, Precision: 97.7837, Recall: 97.7778
2025-07-31 17:16:15 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.1648002415895462, 'fd_loss': 0.05689840763807297, 'vd_loss': 0.11172947287559509, 'confidence_penalty': 0.004601933527737856, 'total_loss': 0.2313702255487442}
2025-07-31 17:16:20 - pulsar_trainer - INFO - Epoch   6: Train Loss=0.1045, Val Loss=0.1272, Train Acc=97.55%, Val Acc=96.67%
2025-07-31 17:16:20 - pulsar_trainer - INFO - Validation F1: 96.6667, Precision: 96.6667, Recall: 96.6667
2025-07-31 17:16:21 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.22520795464515686, 'fd_loss': 0.057981520891189575, 'vd_loss': 0.11387321352958679, 'confidence_penalty': 0.004655248951166868, 'total_loss': 0.2930159270763397}
2025-07-31 17:16:25 - pulsar_trainer - INFO - Epoch   7: Train Loss=0.1804, Val Loss=0.1610, Train Acc=95.64%, Val Acc=96.67%
2025-07-31 17:16:25 - pulsar_trainer - INFO - Validation F1: 96.6650, Precision: 96.7590, Recall: 96.6667
2025-07-31 17:16:26 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.001897829701192677, 'fd_loss': 0.0018847121391445398, 'vd_loss': 0.0029869377613067627, 'confidence_penalty': 0.004811530001461506, 'total_loss': 0.008547796867787838}
2025-07-31 17:16:31 - pulsar_trainer - INFO - Epoch   8: Train Loss=0.1268, Val Loss=0.0626, Train Acc=96.65%, Val Acc=98.06%
2025-07-31 17:16:31 - pulsar_trainer - INFO - Validation F1: 98.0555, Precision: 98.0570, Recall: 98.0556
2025-07-31 17:16:31 - pulsar_trainer - INFO - 模型已保存: D:\pulsarSuanfa\outputs_FPP\models\best_model.pth
2025-07-31 17:16:31 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.03505361080169678, 'fd_loss': 0.031448811292648315, 'vd_loss': 0.028806477785110474, 'confidence_penalty': 0.004590663127601147, 'total_loss': 0.0640106201171875}
2025-07-31 17:16:36 - pulsar_trainer - INFO - Epoch   9: Train Loss=0.0890, Val Loss=0.0737, Train Acc=97.01%, Val Acc=96.94%
2025-07-31 17:16:36 - pulsar_trainer - INFO - Validation F1: 96.9439, Precision: 96.9807, Recall: 96.9444
2025-07-31 17:16:36 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.15509502589702606, 'fd_loss': 0.09599293768405914, 'vd_loss': 0.19122996926307678, 'confidence_penalty': 0.004417470656335354, 'total_loss': 0.26487797498703003}
2025-07-31 17:16:41 - pulsar_trainer - INFO - Epoch  10: Train Loss=0.0952, Val Loss=0.1072, Train Acc=97.25%, Val Acc=95.83%
2025-07-31 17:16:41 - pulsar_trainer - INFO - Validation F1: 95.8279, Precision: 96.0737, Recall: 95.8333
2025-07-31 17:16:41 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.02772885374724865, 'fd_loss': 0.026268891990184784, 'vd_loss': 0.04085609316825867, 'confidence_penalty': 0.003895112546160817, 'total_loss': 0.05701523646712303}
2025-07-31 17:16:46 - pulsar_trainer - INFO - Epoch  11: Train Loss=0.0799, Val Loss=0.0658, Train Acc=98.21%, Val Acc=98.33%
2025-07-31 17:16:46 - pulsar_trainer - INFO - Validation F1: 98.3331, Precision: 98.3572, Recall: 98.3333
2025-07-31 17:16:47 - pulsar_trainer - INFO - 模型已保存: D:\pulsarSuanfa\outputs_FPP\models\best_model.pth
2025-07-31 17:16:47 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.008195010013878345, 'fd_loss': 0.008057741448283195, 'vd_loss': 0.014730215072631836, 'confidence_penalty': 0.00438903970643878, 'total_loss': 0.02103198505938053}
2025-07-31 17:16:52 - pulsar_trainer - INFO - Epoch  12: Train Loss=0.0621, Val Loss=0.0570, Train Acc=98.33%, Val Acc=98.06%
2025-07-31 17:16:52 - pulsar_trainer - INFO - Validation F1: 98.0555, Precision: 98.0570, Recall: 98.0556
2025-07-31 17:16:52 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.034189194440841675, 'fd_loss': 0.026516180485486984, 'vd_loss': 0.04603549838066101, 'confidence_penalty': 0.004708900582045317, 'total_loss': 0.06596683710813522}
2025-07-31 17:16:57 - pulsar_trainer - INFO - Epoch  13: Train Loss=0.0620, Val Loss=0.0758, Train Acc=98.33%, Val Acc=98.06%
2025-07-31 17:16:57 - pulsar_trainer - INFO - Validation F1: 98.0555, Precision: 98.0570, Recall: 98.0556
2025-07-31 17:16:57 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.007895120419561863, 'fd_loss': 0.007303411141037941, 'vd_loss': 0.014048546552658081, 'confidence_penalty': 0.004827192518860102, 'total_loss': 0.020588582381606102}
2025-07-31 17:17:02 - pulsar_trainer - INFO - Epoch  14: Train Loss=0.0763, Val Loss=0.0580, Train Acc=98.03%, Val Acc=97.78%
2025-07-31 17:17:02 - pulsar_trainer - INFO - Validation F1: 97.7775, Precision: 97.8014, Recall: 97.7778
2025-07-31 17:17:02 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.010396106168627739, 'fd_loss': 0.010129714384675026, 'vd_loss': 0.009993106126785278, 'confidence_penalty': 0.004279173444956541, 'total_loss': 0.02273806929588318}
2025-07-31 17:17:07 - pulsar_trainer - INFO - Epoch  15: Train Loss=0.0627, Val Loss=0.0774, Train Acc=98.45%, Val Acc=97.78%
2025-07-31 17:17:07 - pulsar_trainer - INFO - Validation F1: 97.7772, Precision: 97.8309, Recall: 97.7778
2025-07-31 17:17:07 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.006222663447260857, 'fd_loss': 0.0061156535521149635, 'vd_loss': 0.0034473538398742676, 'confidence_penalty': 0.004388436209410429, 'total_loss': 0.014703132212162018}
2025-07-31 17:17:12 - pulsar_trainer - INFO - Epoch  16: Train Loss=0.0531, Val Loss=0.0545, Train Acc=98.51%, Val Acc=98.33%
2025-07-31 17:17:12 - pulsar_trainer - INFO - Validation F1: 98.3331, Precision: 98.3572, Recall: 98.3333
2025-07-31 17:17:12 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.184132382273674, 'fd_loss': 0.056750137358903885, 'vd_loss': 0.11179018020629883, 'confidence_penalty': 0.004816044121980667, 'total_loss': 0.2508605718612671}
2025-07-31 17:17:17 - pulsar_trainer - INFO - Epoch  17: Train Loss=0.0458, Val Loss=0.0999, Train Acc=98.63%, Val Acc=97.50%
2025-07-31 17:17:17 - pulsar_trainer - INFO - Validation F1: 97.4998, Precision: 97.5132, Recall: 97.5000
2025-07-31 17:17:17 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.06447124481201172, 'fd_loss': 0.028040196746587753, 'vd_loss': 0.05522891879081726, 'confidence_penalty': 0.004909820389002562, 'total_loss': 0.09996984153985977}
2025-07-31 17:17:22 - pulsar_trainer - INFO - Epoch  18: Train Loss=0.0472, Val Loss=0.0777, Train Acc=98.86%, Val Acc=97.22%
2025-07-31 17:17:22 - pulsar_trainer - INFO - Validation F1: 97.2221, Precision: 97.2281, Recall: 97.2222
2025-07-31 17:17:22 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.0033643259666860104, 'fd_loss': 0.003314357716590166, 'vd_loss': 0.003354310989379883, 'confidence_penalty': 0.004821772687137127, 'total_loss': 0.010849570855498314}
2025-07-31 17:17:27 - pulsar_trainer - INFO - Epoch  19: Train Loss=0.0581, Val Loss=0.1402, Train Acc=98.63%, Val Acc=95.56%
2025-07-31 17:17:27 - pulsar_trainer - INFO - Validation F1: 95.5506, Precision: 95.7589, Recall: 95.5556
2025-07-31 17:17:27 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.008001740090548992, 'fd_loss': 0.007625503931194544, 'vd_loss': 0.01144707202911377, 'confidence_penalty': 0.0048546637408435345, 'total_loss': 0.020103277638554573}
2025-07-31 17:17:32 - pulsar_trainer - INFO - Epoch  20: Train Loss=0.0651, Val Loss=0.1967, Train Acc=98.27%, Val Acc=97.22%
2025-07-31 17:17:32 - pulsar_trainer - INFO - Validation F1: 97.2215, Precision: 97.2747, Recall: 97.2222
2025-07-31 17:17:33 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 8.828812860883772e-05, 'fd_loss': 8.827356214169413e-05, 'vd_loss': 0.000172346830368042, 'confidence_penalty': 0.00499117374420166, 'total_loss': 0.005175302736461163}
2025-07-31 17:17:38 - pulsar_trainer - INFO - Epoch  21: Train Loss=0.0772, Val Loss=0.1561, Train Acc=97.97%, Val Acc=94.44%
2025-07-31 17:17:38 - pulsar_trainer - INFO - Validation F1: 94.4272, Precision: 95.0000, Recall: 94.4444
2025-07-31 17:17:38 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.2336750030517578, 'fd_loss': 0.08667056262493134, 'vd_loss': 0.16957765817642212, 'confidence_penalty': 0.004761575721204281, 'total_loss': 0.3326451778411865}
2025-07-31 17:17:43 - pulsar_trainer - INFO - Epoch  22: Train Loss=0.0872, Val Loss=0.0920, Train Acc=97.97%, Val Acc=97.50%
2025-07-31 17:17:43 - pulsar_trainer - INFO - Validation F1: 97.5000, Precision: 97.5015, Recall: 97.5000
2025-07-31 17:17:43 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.0478137731552124, 'fd_loss': 0.03304325416684151, 'vd_loss': 0.06591373682022095, 'confidence_penalty': 0.00481761060655117, 'total_loss': 0.08892713487148285}
2025-07-31 17:17:48 - pulsar_trainer - INFO - Epoch  23: Train Loss=0.0813, Val Loss=0.0695, Train Acc=97.79%, Val Acc=98.33%
2025-07-31 17:17:48 - pulsar_trainer - INFO - Validation F1: 98.3331, Precision: 98.3572, Recall: 98.3333
2025-07-31 17:17:48 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.006229493301361799, 'fd_loss': 0.005900997668504715, 'vd_loss': 0.010125607252120972, 'confidence_penalty': 0.0048299082554876804, 'total_loss': 0.0170475821942091}
2025-07-31 17:17:53 - pulsar_trainer - INFO - Epoch  24: Train Loss=0.0395, Val Loss=0.0393, Train Acc=99.04%, Val Acc=98.33%
2025-07-31 17:17:53 - pulsar_trainer - INFO - Validation F1: 98.3333, Precision: 98.3393, Recall: 98.3333
2025-07-31 17:17:53 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.0558101087808609, 'fd_loss': 0.035210154950618744, 'vd_loss': 0.06184530258178711, 'confidence_penalty': 0.004383055027574301, 'total_loss': 0.09635183960199356}
2025-07-31 17:17:58 - pulsar_trainer - INFO - Epoch  25: Train Loss=0.0543, Val Loss=0.0768, Train Acc=98.45%, Val Acc=97.78%
2025-07-31 17:17:58 - pulsar_trainer - INFO - Validation F1: 97.7772, Precision: 97.8309, Recall: 97.7778
2025-07-31 17:17:58 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.08297233283519745, 'fd_loss': 0.030457213521003723, 'vd_loss': 0.05624553561210632, 'confidence_penalty': 0.004843785893172026, 'total_loss': 0.11991839110851288}
2025-07-31 17:18:03 - pulsar_trainer - INFO - Epoch  26: Train Loss=0.0374, Val Loss=0.0529, Train Acc=98.81%, Val Acc=98.89%
2025-07-31 17:18:03 - pulsar_trainer - INFO - Validation F1: 98.8889, Precision: 98.8949, Recall: 98.8889
2025-07-31 17:18:03 - pulsar_trainer - INFO - 模型已保存: D:\pulsarSuanfa\outputs_FPP\models\best_model.pth
2025-07-31 17:18:03 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.016275916248559952, 'fd_loss': 0.013301846571266651, 'vd_loss': 0.025817453861236572, 'confidence_penalty': 0.004836536943912506, 'total_loss': 0.03550861030817032}
2025-07-31 17:18:08 - pulsar_trainer - INFO - Epoch  27: Train Loss=0.0578, Val Loss=0.0709, Train Acc=98.15%, Val Acc=98.33%
2025-07-31 17:18:08 - pulsar_trainer - INFO - Validation F1: 98.3333, Precision: 98.3393, Recall: 98.3333
2025-07-31 17:18:09 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.018996434286236763, 'fd_loss': 0.014579921960830688, 'vd_loss': 0.026293396949768066, 'confidence_penalty': 0.0049196574836969376, 'total_loss': 0.039094068109989166}
2025-07-31 17:18:14 - pulsar_trainer - INFO - Epoch  28: Train Loss=0.1329, Val Loss=0.1095, Train Acc=98.39%, Val Acc=98.06%
2025-07-31 17:18:14 - pulsar_trainer - INFO - Validation F1: 98.0552, Precision: 98.0927, Recall: 98.0556
2025-07-31 17:18:14 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.01825649105012417, 'fd_loss': 0.01791766658425331, 'vd_loss': 0.020937681198120117, 'confidence_penalty': 0.003530336543917656, 'total_loss': 0.037026964128017426}
2025-07-31 17:18:19 - pulsar_trainer - INFO - Epoch  29: Train Loss=0.1383, Val Loss=0.1265, Train Acc=96.24%, Val Acc=96.39%
2025-07-31 17:18:19 - pulsar_trainer - INFO - Validation F1: 96.3855, Precision: 96.5628, Recall: 96.3889
2025-07-31 17:18:19 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.11343403905630112, 'fd_loss': 0.09949542582035065, 'vd_loss': 0.1985885202884674, 'confidence_penalty': 0.004977012053132057, 'total_loss': 0.22773532569408417}
2025-07-31 17:18:24 - pulsar_trainer - INFO - Epoch  30: Train Loss=0.1316, Val Loss=0.1071, Train Acc=97.25%, Val Acc=97.50%
2025-07-31 17:18:24 - pulsar_trainer - INFO - Validation F1: 97.4998, Precision: 97.5132, Recall: 97.5000
2025-07-31 17:18:24 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.17243297398090363, 'fd_loss': 0.05149956792593002, 'vd_loss': 0.10081052780151367, 'confidence_penalty': 0.004606475587934256, 'total_loss': 0.23303239047527313}
2025-07-31 17:18:29 - pulsar_trainer - INFO - Epoch  31: Train Loss=0.0689, Val Loss=0.0528, Train Acc=98.86%, Val Acc=98.33%
2025-07-31 17:18:29 - pulsar_trainer - INFO - Validation F1: 98.3331, Precision: 98.3572, Recall: 98.3333
2025-07-31 17:18:29 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.02276361733675003, 'fd_loss': 0.020906493067741394, 'vd_loss': 0.03941342234611511, 'confidence_penalty': 0.004581300541758537, 'total_loss': 0.04962219297885895}
2025-07-31 17:18:34 - pulsar_trainer - INFO - Epoch  32: Train Loss=0.0407, Val Loss=0.0415, Train Acc=98.92%, Val Acc=98.89%
2025-07-31 17:18:34 - pulsar_trainer - INFO - Validation F1: 98.8889, Precision: 98.8889, Recall: 98.8889
2025-07-31 17:18:34 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.003589011961594224, 'fd_loss': 0.0034600761719048023, 'vd_loss': 0.006292223930358887, 'confidence_penalty': 0.004923900123685598, 'total_loss': 0.01213061809539795}
2025-07-31 17:18:39 - pulsar_trainer - INFO - Epoch  33: Train Loss=0.0340, Val Loss=0.0443, Train Acc=99.16%, Val Acc=98.33%
2025-07-31 17:18:39 - pulsar_trainer - INFO - Validation F1: 98.3331, Precision: 98.3572, Recall: 98.3333
2025-07-31 17:18:39 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.00011637721763690934, 'fd_loss': 0.00011633653775788844, 'vd_loss': 7.915496826171875e-05, 'confidence_penalty': 0.004988368134945631, 'total_loss': 0.005186660215258598}
2025-07-31 17:18:44 - pulsar_trainer - INFO - Epoch  34: Train Loss=0.0603, Val Loss=0.0347, Train Acc=98.45%, Val Acc=98.61%
2025-07-31 17:18:44 - pulsar_trainer - INFO - Validation F1: 98.6111, Precision: 98.6126, Recall: 98.6111
2025-07-31 17:18:44 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.010568661615252495, 'fd_loss': 0.009681591764092445, 'vd_loss': 0.011919975280761719, 'confidence_penalty': 0.004705531056970358, 'total_loss': 0.023690981790423393}
2025-07-31 17:18:49 - pulsar_trainer - INFO - Epoch  35: Train Loss=0.0344, Val Loss=0.0678, Train Acc=99.10%, Val Acc=98.61%
2025-07-31 17:18:49 - pulsar_trainer - INFO - Validation F1: 98.6110, Precision: 98.6246, Recall: 98.6111
2025-07-31 17:18:49 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.08797230571508408, 'fd_loss': 0.029842782765626907, 'vd_loss': 0.0587286651134491, 'confidence_penalty': 0.004948957357555628, 'total_loss': 0.12546125054359436}
2025-07-31 17:18:54 - pulsar_trainer - INFO - Epoch  36: Train Loss=0.0544, Val Loss=0.0490, Train Acc=98.51%, Val Acc=98.33%
2025-07-31 17:18:54 - pulsar_trainer - INFO - Validation F1: 98.3333, Precision: 98.3393, Recall: 98.3333
2025-07-31 17:18:54 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.0004893938312307, 'fd_loss': 0.0004889309057034552, 'vd_loss': 0.000873863697052002, 'confidence_penalty': 0.004951108247041702, 'total_loss': 0.005947126541286707}
2025-07-31 17:18:59 - pulsar_trainer - INFO - Epoch  37: Train Loss=0.0393, Val Loss=0.0580, Train Acc=98.86%, Val Acc=98.33%
2025-07-31 17:18:59 - pulsar_trainer - INFO - Validation F1: 98.3331, Precision: 98.3572, Recall: 98.3333
2025-07-31 17:18:59 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.012270769104361534, 'fd_loss': 0.010204672813415527, 'vd_loss': 0.020326614379882812, 'confidence_penalty': 0.00498195318505168, 'total_loss': 0.028453044593334198}
2025-07-31 17:19:04 - pulsar_trainer - INFO - Epoch  38: Train Loss=0.0294, Val Loss=0.0426, Train Acc=99.10%, Val Acc=98.61%
2025-07-31 17:19:04 - pulsar_trainer - INFO - Validation F1: 98.6110, Precision: 98.6246, Recall: 98.6111
2025-07-31 17:19:05 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.0036109050270169973, 'fd_loss': 0.003589773550629616, 'vd_loss': 0.005068182945251465, 'confidence_penalty': 0.004641023930162191, 'total_loss': 0.011567270383238792}
2025-07-31 17:19:10 - pulsar_trainer - INFO - Epoch  39: Train Loss=0.0294, Val Loss=0.0772, Train Acc=99.40%, Val Acc=98.06%
2025-07-31 17:19:10 - pulsar_trainer - INFO - Validation F1: 98.0552, Precision: 98.0927, Recall: 98.0556
2025-07-31 17:19:10 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.0181459691375494, 'fd_loss': 0.01656629517674446, 'vd_loss': 0.033042311668395996, 'confidence_penalty': 0.0044679599814116955, 'total_loss': 0.04080976918339729}
2025-07-31 17:19:15 - pulsar_trainer - INFO - Epoch  40: Train Loss=0.0410, Val Loss=0.0981, Train Acc=98.92%, Val Acc=97.78%
2025-07-31 17:19:15 - pulsar_trainer - INFO - Validation F1: 97.7772, Precision: 97.8309, Recall: 97.7778
2025-07-31 17:19:15 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.00026711798273026943, 'fd_loss': 0.00026692100800573826, 'vd_loss': 0.00035902857780456543, 'confidence_penalty': 0.004973308648914099, 'total_loss': 0.005481595639139414}
2025-07-31 17:19:20 - pulsar_trainer - INFO - Epoch  41: Train Loss=0.0320, Val Loss=0.0601, Train Acc=99.40%, Val Acc=98.06%
2025-07-31 17:19:20 - pulsar_trainer - INFO - Validation F1: 98.0552, Precision: 98.0927, Recall: 98.0556
2025-07-31 17:19:20 - pulsar_trainer - INFO - 早停触发，在第 41 轮停止训练
2025-07-31 17:19:20 - pulsar_trainer - INFO - ============================================================
2025-07-31 17:19:20 - pulsar_trainer - INFO - ✅ 训练完成
2025-07-31 17:19:20 - pulsar_trainer - INFO - 🏆 最佳验证准确率: 98.8889
2025-07-31 17:19:20 - pulsar_trainer - INFO - ⏱️ 总训练时间: 256.39秒
2025-07-31 17:19:20 - pulsar_trainer - INFO - ============================================================
2025-07-31 17:19:20 - pulsar_trainer - INFO - 🔍 开始最终评估...
2025-07-31 17:19:20 - pulsar_trainer - INFO - 模型已加载: D:\pulsarSuanfa\outputs_FPP\models\best_model.pth
2025-07-31 17:19:43 - pulsar_trainer.utils.comprehensive_misclassification_analysis - INFO - 开始全面误分类分析...
2025-07-31 17:19:43 - pulsar_trainer.utils.comprehensive_misclassification_analysis - INFO - 综合误分类分析报告已生成: D:\pulsarSuanfa\outputs_FPP\results\comprehensive_misclassification_analysis\comprehensive_misclassification_report.txt
2025-07-31 17:19:43 - pulsar_trainer.utils.comprehensive_misclassification_analysis - INFO - 全面误分类分析完成。报告保存至: D:\pulsarSuanfa\outputs_FPP\results\comprehensive_misclassification_analysis
2025-07-31 17:19:43 - pulsar_trainer - INFO - 📊 评估结果:
2025-07-31 17:19:43 - pulsar_trainer - INFO -   - accuracy: 0.9860
2025-07-31 17:19:43 - pulsar_trainer - INFO -   - precision: 0.9728
2025-07-31 17:19:43 - pulsar_trainer - INFO -   - recall: 1.0000
2025-07-31 17:19:43 - pulsar_trainer - INFO -   - specificity: 0.9721
2025-07-31 17:19:43 - pulsar_trainer - INFO -   - f1_score: 0.9862
2025-07-31 17:19:43 - pulsar_trainer - INFO -   - false_positive_rate: 0.0279
2025-07-31 17:19:43 - pulsar_trainer - INFO - 📊 增强模块统计信息:
2025-07-31 17:19:43 - pulsar_trainer - INFO -   - 总前向传播次数: 5106
2025-07-31 17:19:43 - pulsar_trainer - INFO -   - FPP激活次数: 1702
2025-07-31 17:19:43 - pulsar_trainer - INFO -   - TPP激活次数: 1702
2025-07-31 17:19:43 - pulsar_trainer - INFO -   - 损失计算次数: 1378
2025-07-31 17:19:43 - pulsar_trainer - INFO -   - FPP激活率: 33.33%
2025-07-31 17:19:43 - pulsar_trainer - INFO -   - TPP激活率: 33.33%
2025-07-31 17:19:45 - pulsar_trainer - WARNING - ⚠️ Misclassification analysis failed: 'low_pulsar_prob_count'
2025-07-31 17:19:45 - pulsar_trainer - INFO - 所有结果已保存到: D:\pulsarSuanfa\outputs_FPP\results
2025-07-31 17:19:45 - pulsar_trainer - INFO - 可视化图表已保存到: D:\pulsarSuanfa\outputs_FPP\plots
