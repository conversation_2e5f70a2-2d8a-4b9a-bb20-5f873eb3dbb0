"""
CBAM - 卷积块注意力模块
直接提取自原始CBAM实现，用于脉冲星分类中的TPP数据处理

原始论文: CBAM: Convolutional Block Attention Module (ECCV 2018)
应用用途: 脉冲星TPP数据的通道和空间注意力增强
"""

import torch
import torch.nn as nn


class ChannelAttention(nn.Module):
    """
    通道注意力模块
    
    通过全局平均池化和最大池化提取通道间的重要性权重，
    专门用于增强脉冲星信号在不同通道维度上的判别能力。
    
    Args:
        in_planes: 输入通道数
        ratio: 通道降维比例，用于减少参数量
        
    Input:
        x: [B, C, H, W] - 输入特征图
        
    Output:
        attention_weights: [B, C, 1, 1] - 通道注意力权重
    """
    
    def __init__(self, in_planes, ratio=16):
        super(ChannelAttention, self).__init__()
        self.avg_pool = nn.AdaptiveAvgPool2d(1)  # 全局平均池化
        self.max_pool = nn.AdaptiveMaxPool2d(1)  # 全局最大池化

        # 使用1x1卷积代替全连接层，减少参数量，ratio用于降维
        self.fc = nn.Sequential(
            nn.Conv2d(in_planes, in_planes // ratio, 1, bias=False),
            nn.ReLU(),
            nn.Conv2d(in_planes // ratio, in_planes, 1, bias=False)
        )
        self.sigmoid = nn.Sigmoid()  # 激活函数

    def forward(self, x):
        """
        通道注意力前向传播
        
        Args:
            x: [B, C, H, W] - 输入特征图
            
        Returns:
            attention_weights: [B, C, 1, 1] - 通道注意力权重
        """
        avg_out = self.fc(self.avg_pool(x))  # 全局平均池化分支
        max_out = self.fc(self.max_pool(x))  # 全局最大池化分支
        out = avg_out + max_out  # 融合两个池化分支
        return self.sigmoid(out)  # 返回通道权重


class SpatialAttention(nn.Module):
    """
    空间注意力模块
    
    通过通道维度的平均和最大操作提取空间位置的重要性权重，
    专门用于增强脉冲星信号在空间维度上的定位能力。
    
    Args:
        kernel_size: 卷积核大小，用于空间特征提取
        
    Input:
        x: [B, C, H, W] - 输入特征图
        
    Output:
        attention_weights: [B, 1, H, W] - 空间注意力权重
    """
    
    def __init__(self, kernel_size=7):
        super(SpatialAttention, self).__init__()
        self.conv1 = nn.Conv2d(2, 1, kernel_size, padding=kernel_size // 2, bias=False)
        self.sigmoid = nn.Sigmoid()

    def forward(self, x):
        """
        空间注意力前向传播
        
        Args:
            x: [B, C, H, W] - 输入特征图
            
        Returns:
            attention_weights: [B, 1, H, W] - 空间注意力权重
        """
        avg_out = torch.mean(x, dim=1, keepdim=True)  # 计算特征图的平均值
        max_out, _ = torch.max(x, dim=1, keepdim=True)  # 计算特征图的最大值
        x = torch.cat([avg_out, max_out], dim=1)  # 沿通道维度拼接
        x = self.conv1(x)  # 通过卷积层
        return self.sigmoid(x)  # 返回空间权重


class CBAM(nn.Module):
    """
    卷积块注意力模块 (Convolutional Block Attention Module)
    
    结合通道注意力和空间注意力的双重注意力机制，
    专门用于脉冲星TPP数据的特征增强，提高模型对脉冲星信号的判别能力。
    
    处理流程：
    1. 首先应用通道注意力，增强重要通道的特征
    2. 然后应用空间注意力，突出重要空间位置的特征
    3. 两个注意力模块串行工作，逐步细化特征表示
    
    Args:
        in_planes: 输入通道数
        ratio: 通道注意力的降维比例
        kernel_size: 空间注意力的卷积核大小
        
    Input:
        x: [B, C, H, W] - 输入特征图
        
    Output:
        enhanced_x: [B, C, H, W] - 注意力增强后的特征图
    """
    
    def __init__(self, in_planes, ratio=16, kernel_size=7):
        super(CBAM, self).__init__()
        self.channel_attention = ChannelAttention(in_planes, ratio)  # 通道注意力模块
        self.spatial_attention = SpatialAttention(kernel_size)  # 空间注意力模块

    def forward(self, x):
        """
        CBAM前向传播
        
        Args:
            x: [B, C, H, W] - 输入特征图
            
        Returns:
            enhanced_x: [B, C, H, W] - 双重注意力增强后的特征图
        """
        # 输入验证
        assert len(x.shape) == 4, f"Input must be 4D tensor [B, C, H, W], got shape {x.shape}"
        
        # 先应用通道注意力
        out = self.channel_attention(x) * x
        
        # 再应用空间注意力
        out = self.spatial_attention(out) * out
        
        return out  # 返回增强后的特征


# 测试代码
if __name__ == '__main__':
    # 测试CBAM模块
    batch_size = 2
    channels = 512  # 对应CoAtNet s3/s4阶段的通道数
    height, width = 8, 8  # 对应后期阶段的特征图尺寸
    
    # 创建测试输入
    x = torch.randn(batch_size, channels, height, width)
    print(f"Input shape: {x.shape}")
    
    # 初始化CBAM模块
    cbam = CBAM(in_planes=channels, ratio=16, kernel_size=7)
    print(f"CBAM parameters: {sum(p.numel() for p in cbam.parameters())}")
    
    # 前向传播测试
    with torch.no_grad():
        output = cbam(x)
        print(f"Output shape: {output.shape}")
        print(f"Shape preserved: {x.shape == output.shape}")
        
        # 测试注意力权重
        channel_weights = cbam.channel_attention(x)
        spatial_weights = cbam.spatial_attention(x)
        print(f"Channel attention weights shape: {channel_weights.shape}")
        print(f"Spatial attention weights shape: {spatial_weights.shape}")
        print("CBAM extraction completed successfully!")
