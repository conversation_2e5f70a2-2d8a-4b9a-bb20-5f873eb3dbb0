"""
Configuration management for CoAtNet pulsar classification.

This module provides functions to load, validate, and manage configuration files.
Includes configuration classes following PyTorch AO best practices.
"""

import yaml
from pathlib import Path
from typing import Dict, Any, List, Union, Optional
import os
import copy
from dataclasses import dataclass
from abc import ABC, abstractmethod


class PulsarTrainingError(Exception):
    """Base exception for pulsar training related errors."""
    pass


class ConfigError(PulsarTrainingError):
    """Configuration related errors."""
    pass


# Configuration Classes (following PyTorch AO best practices)

@dataclass
class BaseConfig(ABC):
    """基础配置类，参考PyTorch AO的AOBaseConfig"""

    def validate(self) -> None:
        """验证配置参数的有效性"""
        pass


@dataclass
class AugmentationConfig(BaseConfig):
    """数据增强配置类"""
    enabled: bool = True
    phase_shift: Dict[str, Union[bool, float]] = None
    frequency_shift: Dict[str, Union[bool, float]] = None
    time_shift: Dict[str, Union[bool, float]] = None
    brightness_scaling: Dict[str, Union[bool, float, List[float]]] = None
    noise: Dict[str, Union[bool, float]] = None

    def __post_init__(self):
        if self.phase_shift is None:
            self.phase_shift = {"enabled": True, "probability": 0.7, "max_shift": 1.0}
        if self.frequency_shift is None:
            self.frequency_shift = {"enabled": True, "probability": 0.3, "max_shift": 0.1}
        if self.time_shift is None:
            self.time_shift = {"enabled": True, "probability": 0.3, "max_shift": 0.1}
        if self.brightness_scaling is None:
            self.brightness_scaling = {"enabled": True, "probability": 0.5, "scale_range": [0.8, 1.2]}
        if self.noise is None:
            self.noise = {"enabled": True, "probability": 0.4, "noise_level": 0.01}


@dataclass
class ModelConfig(BaseConfig):
    """模型配置类"""
    name: str = "coatnet"
    num_blocks: List[int] = None
    channels: List[int] = None
    block_types: List[str] = None
    image_size: List[int] = None
    in_channels: int = 1
    num_classes: int = 2
    dropout: float = 0.2

    def __post_init__(self):
        if self.num_blocks is None:
            self.num_blocks = [2, 2, 6, 8, 2]
        if self.channels is None:
            self.channels = [96, 128, 256, 512, 1024]
        if self.block_types is None:
            self.block_types = ['C', 'C', 'T', 'T']
        if self.image_size is None:
            self.image_size = [64, 64]


@dataclass
class DataConfig(BaseConfig):
    """数据配置类"""
    modality: str = "FPP"
    data_root: str = ""
    normalize: bool = True
    num_workers: int = 4
    pin_memory: bool = True

    def validate(self) -> None:
        if self.modality not in ["FPP", "TPP"]:
            raise ValueError(f"Invalid modality: {self.modality}")


@dataclass
class TrainingConfig(BaseConfig):
    """训练配置类"""
    batch_size: int = 32
    epochs: int = 100
    learning_rate: float = 0.001
    weight_decay: float = 0.01
    optimizer: str = "adamw"


def _ensure_type(config: Dict[str, Any], param_path: tuple, target_type: type) -> None:
    """
    确保指定路径的参数是目标类型。

    Args:
        config: 配置字典
        param_path: 参数路径元组，如 ('training', 'batch_size')
        target_type: 目标类型 (int 或 float)
    """
    try:
        current = config
        for key in param_path[:-1]:
            if key not in current:
                return  # 参数不存在，跳过
            current = current[key]

        final_key = param_path[-1]
        if final_key in current:
            current[final_key] = target_type(current[final_key])

    except (ValueError, TypeError, KeyError):
        # 类型转换失败，保持原值（让后续验证处理）
        pass


def normalize_config_types(config: Dict[str, Any]) -> Dict[str, Any]:
    """
    规范化配置中的数据类型，确保数值参数是正确的类型。

    这是一个最小化的类型安全方案，只处理已知会导致类型错误的参数。
    保持YAML配置文件为唯一权威源，代码只进行必要的类型转换。

    Args:
        config: 原始配置字典

    Returns:
        类型规范化后的配置字典
    """
    # 定义需要确保为整数的参数路径
    int_params = [
        ('training', 'batch_size'),
        ('training', 'epochs'),
        ('training', 'scheduler', 'warmup_epochs'),
        ('training', 'early_stopping', 'patience'),
        ('data', 'num_workers'),
        ('model', 'coatnet', 'in_channels'),
        ('model', 'coatnet', 'num_classes'),
    ]

    # 定义需要确保为浮点数的参数路径
    float_params = [
        ('training', 'learning_rate'),
        ('training', 'weight_decay'),
        ('training', 'scheduler', 'min_lr'),
        ('training', 'early_stopping', 'min_delta'),
        ('training', 'loss', 'smoothing'),
        ('model', 'coatnet', 'dropout'),
        ('performance_targets', 'accuracy'),
        ('performance_targets', 'precision'),
        ('performance_targets', 'recall'),
        ('performance_targets', 'f1_score'),
        ('performance_targets', 'false_positive_rate'),
    ]

    # 创建配置副本以避免修改原始配置
    config_copy = copy.deepcopy(config)

    # 执行类型转换
    for param_path in int_params:
        _ensure_type(config_copy, param_path, int)

    for param_path in float_params:
        _ensure_type(config_copy, param_path, float)

    # 处理performance_targets的单位转换问题
    # 配置文件中是小数形式（0.997），需要转换为百分比形式（99.7）以匹配计算出的指标
    if 'performance_targets' in config_copy:
        targets = config_copy['performance_targets']
        for metric, value in targets.items():
            try:
                numeric_value = float(value)
                # 如果是小数形式（< 1.0），转换为百分比形式
                if numeric_value < 1.0:
                    targets[metric] = numeric_value * 100.0
                else:
                    targets[metric] = numeric_value
            except (ValueError, TypeError):
                # 保持原值，让后续验证处理
                pass

    return config_copy


def load_config_with_classes(config_path: str) -> Dict[str, Any]:
    """加载配置并创建配置类实例"""
    config = load_config(config_path)

    # 创建配置类实例
    config['augmentation'] = AugmentationConfig(**config.get('augmentation', {}))
    config['model']['coatnet'] = ModelConfig(**config['model'].get('coatnet', {}))
    config['data'] = DataConfig(**config.get('data', {}))

    return config


def load_config(config_path: str) -> Dict[str, Any]:
    """
    Load configuration from YAML file.

    Args:
        config_path: Path to the configuration file

    Returns:
        Configuration dictionary

    Raises:
        ConfigError: If configuration file cannot be loaded
    """
    config_path = Path(config_path)

    if not config_path.exists():
        raise ConfigError(f"Configuration file not found: {config_path}")

    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)

        # Expand environment variables and relative paths
        config = _expand_paths(config)

        # 添加类型规范化（新增的关键步骤）
        config = normalize_config_types(config)

        return config

    except yaml.YAMLError as e:
        raise ConfigError(f"Failed to parse YAML configuration: {e}")
    except Exception as e:
        raise ConfigError(f"Failed to load configuration: {e}")


def validate_config(config: Dict[str, Any]) -> None:
    """
    Validate configuration dictionary.

    Args:
        config: Configuration dictionary to validate

    Raises:
        ConfigError: If configuration is invalid
    """
    required_sections = ['data', 'model', 'training', 'performance_targets']

    for section in required_sections:
        if section not in config:
            raise ConfigError(f"Missing required configuration section: {section}")

    # Validate data section
    data_config = config['data']
    required_data_keys = ['modality', 'channel_strategy', 'data_root']
    for key in required_data_keys:
        if key not in data_config:
            raise ConfigError(f"Missing required data configuration: {key}")

    # Validate modality
    valid_modalities = ['FPP', 'TPP']
    if data_config['modality'] not in valid_modalities:
        raise ConfigError(f"Invalid modality: {data_config['modality']}. Must be one of {valid_modalities}")

    # Validate channel strategy
    valid_strategies = ['physical', 'replicate', 'augment', 'single_channel']
    if data_config['channel_strategy'] not in valid_strategies:
        raise ConfigError(f"Invalid channel strategy: {data_config['channel_strategy']}. Must be one of {valid_strategies}")

    # Validate data root exists
    data_root = Path(data_config['data_root'])
    if not data_root.exists():
        raise ConfigError(f"Data root directory does not exist: {data_root}")

    # Validate model section
    model_config = config['model']
    if 'name' not in model_config:
        raise ConfigError("Missing model name in configuration")

    if model_config['name'] not in model_config:
        raise ConfigError(f"Missing configuration for model: {model_config['name']}")

    # Validate training section
    training_config = config['training']
    required_training_keys = ['batch_size', 'learning_rate', 'epochs']
    for key in required_training_keys:
        if key not in training_config:
            raise ConfigError(f"Missing required training configuration: {key}")

    # Validate performance targets
    targets = config['performance_targets']
    required_targets = ['accuracy', 'recall', 'false_positive_rate']
    for target in required_targets:
        if target not in targets:
            raise ConfigError(f"Missing performance target: {target}")

    # Validate performance target types and values
    for metric, value in targets.items():
        try:
            numeric_value = float(value)
            if numeric_value < 0:
                raise ConfigError(f"Performance target for {metric} must be non-negative: {value}")
            if metric != 'false_positive_rate' and numeric_value > 100:
                raise ConfigError(f"Performance target for {metric} seems too high (>100): {value}")
        except (ValueError, TypeError):
            raise ConfigError(f"Performance target for {metric} must be numeric: {value}")


def validate_config_with_classes(config: Dict[str, Any]) -> None:
    """
    验证使用配置类的配置

    Args:
        config: 配置字典

    Raises:
        ConfigError: 配置验证失败
    """
    try:
        # 验证各个配置类
        if 'augmentation' in config:
            config['augmentation'].validate()

        if 'model' in config and 'coatnet' in config['model']:
            config['model']['coatnet'].validate()

        if 'data' in config:
            config['data'].validate()

        # 验证配置一致性
        model_config = config['model']['coatnet']
        if model_config.in_channels != 1:
            raise ConfigError(f"Model in_channels must be 1, got {model_config.in_channels}")

        # 验证数据根目录存在
        data_root = Path(config['data'].data_root)
        if not data_root.exists():
            raise ConfigError(f"Data root directory does not exist: {data_root}")

        print("✓ 所有配置验证通过")

    except Exception as e:
        raise ConfigError(f"Configuration validation failed: {e}")


def _expand_paths(config: Dict[str, Any]) -> Dict[str, Any]:
    """
    Expand environment variables and relative paths in configuration.

    Args:
        config: Configuration dictionary

    Returns:
        Configuration with expanded paths
    """
    def expand_value(value):
        if isinstance(value, str):
            # Expand environment variables
            value = os.path.expandvars(value)
            # Expand user home directory
            value = os.path.expanduser(value)
            return value
        elif isinstance(value, dict):
            return {k: expand_value(v) for k, v in value.items()}
        elif isinstance(value, list):
            return [expand_value(item) for item in value]
        else:
            return value

    return expand_value(config)


def get_data_path(config: Dict[str, Any], split: str) -> Path:
    """
    Get data path for a specific split and modality.

    Args:
        config: Configuration dictionary
        split: Data split (train, validation, test)

    Returns:
        Path to the data directory
    """
    data_config = config['data']

    # 兼容新旧配置系统
    if hasattr(data_config, 'data_root'):
        data_root = Path(data_config.data_root)
        modality = data_config.modality
    else:
        data_root = Path(data_config['data_root'])
        modality = data_config['modality']

    return data_root / modality / split


def get_project_root() -> Path:
    """
    获取项目根目录。

    Returns:
        项目根目录的Path对象
    """
    current_file = Path(__file__).resolve()

    # 向上查找包含pulsar_trainer目录的父目录
    for parent in current_file.parents:
        if (parent / "pulsar_trainer").exists():
            return parent

    # 如果找不到，返回当前工作目录
    return Path.cwd()


def create_output_dirs(config: Dict[str, Any]) -> Dict[str, Path]:
    """
    Create modality-specific output directories based on configuration.

    Args:
        config: Configuration dictionary

    Returns:
        Dictionary of created directory paths
    """
    output_config = config.get('output', {})
    data_config = config.get('data', {})

    # Get modality for directory naming
    modality = data_config.modality

    # Create modality-specific directories
    # Support both 'base_dir' (from config file) and 'output_dir' (from command line override)
    base_output_dir = output_config.get('base_dir', output_config.get('output_dir', 'outputs'))
    output_dir = Path(f"{base_output_dir}_{modality}")

    # Ensure absolute path relative to project root if path is relative
    if not output_dir.is_absolute():
        project_root = get_project_root()
        output_dir = project_root / output_dir

    # Place logs inside the modality-specific output directory
    log_dir = output_dir / 'logs'

    # Create directories
    output_dir.mkdir(parents=True, exist_ok=True)
    log_dir.mkdir(parents=True, exist_ok=True)

    # Create subdirectories
    model_dir = output_dir / 'models'
    plot_dir = output_dir / 'plots'
    result_dir = output_dir / 'results'

    model_dir.mkdir(exist_ok=True)
    plot_dir.mkdir(exist_ok=True)
    result_dir.mkdir(exist_ok=True)

    return {
        'output': output_dir,
        'log': log_dir,
        'model': model_dir,
        'plot': plot_dir,
        'result': result_dir,
        'modality': modality
    }
