"""
特征判别增强器 (Feature Discriminator Enhancer, FDE)
基于对比学习思想，专门针对FPP问题的细粒度特征判别
增强真实脉冲星信号与噪声信号的特征区分度
"""

import torch
import torch.nn as nn
import torch.nn.functional as F


class ContrastiveFeatureExtractor(nn.Module):
    """
    对比特征提取器
    提取用于对比学习的判别性特征
    """
    def __init__(self, channels, feature_dim=128):
        super().__init__()
        
        # 特征提取网络
        self.feature_extractor = nn.Sequential(
            # 第一层：降维和特征提取
            nn.Conv2d(channels, feature_dim, 3, padding=1, bias=False),
            nn.BatchNorm2d(feature_dim),
            nn.ReLU(inplace=True),
            
            # 第二层：进一步特征抽象
            nn.Conv2d(feature_dim, feature_dim, 3, padding=1, bias=False),
            nn.BatchNorm2d(feature_dim),
            nn.ReLU(inplace=True),
            
            # 第三层：最终特征映射
            nn.Conv2d(feature_dim, feature_dim, 1, bias=False),
            nn.BatchNorm2d(feature_dim),
            nn.ReLU(inplace=True)
        )
        
        # 全局特征聚合
        self.global_pool = nn.AdaptiveAvgPool2d(1)
        
        # 特征归一化
        self.feature_norm = nn.LayerNorm(feature_dim)
        
    def forward(self, x):
        # 提取局部特征
        local_features = self.feature_extractor(x)
        
        # 全局特征聚合
        global_features = self.global_pool(local_features)
        global_features = global_features.view(global_features.size(0), -1)
        
        # 特征归一化
        global_features = self.feature_norm(global_features)
        
        return local_features, global_features


class DiscriminativeFeatureEnhancer(nn.Module):
    """
    判别性特征增强器
    基于对比特征增强原始特征的判别能力
    """
    def __init__(self, channels, feature_dim=128):
        super().__init__()
        
        # 判别性权重生成
        self.discriminative_weights = nn.Sequential(
            nn.Conv2d(feature_dim, channels // 4, 1, bias=False),
            nn.BatchNorm2d(channels // 4),
            nn.ReLU(inplace=True),
            nn.Conv2d(channels // 4, channels, 1, bias=False),
            nn.Sigmoid()
        )
        
        # 特征重构网络
        self.feature_reconstructor = nn.Sequential(
            nn.Conv2d(feature_dim, channels // 2, 1, bias=False),
            nn.BatchNorm2d(channels // 2),
            nn.ReLU(inplace=True),
            nn.Conv2d(channels // 2, channels, 1, bias=False),
            nn.BatchNorm2d(channels),
            nn.ReLU(inplace=True)
        )
        
        # 自适应融合权重
        self.fusion_weight = nn.Parameter(torch.tensor(0.5))
        
    def forward(self, x, contrast_features):
        # 生成判别性权重
        discriminative_weights = self.discriminative_weights(contrast_features)
        
        # 特征重构
        reconstructed_features = self.feature_reconstructor(contrast_features)
        
        # 判别性增强
        enhanced_original = x * discriminative_weights
        
        # 自适应特征融合
        output = (1 - self.fusion_weight) * enhanced_original + self.fusion_weight * reconstructed_features
        
        return output


class FeatureDiscriminatorEnhancer(nn.Module):
    """
    特征判别增强器 (FDE)
    结合对比特征提取和判别性特征增强
    专门针对FPP问题的假阳性抑制
    """
    def __init__(self, channels, feature_dim=128, contrast_temperature=0.1):
        super().__init__()
        
        # 对比特征提取器
        self.contrast_extractor = ContrastiveFeatureExtractor(channels, feature_dim)
        
        # 判别性特征增强器
        self.discriminative_enhancer = DiscriminativeFeatureEnhancer(channels, feature_dim)
        
        # 对比学习温度参数
        self.temperature = contrast_temperature
        
        # 特征一致性约束
        self.consistency_loss_weight = nn.Parameter(torch.tensor(0.1))
        
    def forward(self, x):
        # 对比特征提取
        local_contrast_features, global_contrast_features = self.contrast_extractor(x)
        
        # 判别性特征增强
        enhanced_features = self.discriminative_enhancer(x, local_contrast_features)
        
        # 返回增强特征和对比特征（用于损失计算）
        return enhanced_features, {
            'local_contrast': local_contrast_features,
            'global_contrast': global_contrast_features
        }
    
    def compute_contrastive_loss(self, global_features, labels=None):
        """
        计算对比学习损失
        增强真实脉冲星和噪声信号的特征区分度
        """
        if labels is None:
            # 无监督对比学习：增强批内样本的多样性
            batch_size = global_features.size(0)
            
            # 计算特征相似度矩阵
            similarity_matrix = torch.matmul(global_features, global_features.T) / self.temperature
            
            # 创建对角掩码（排除自身）
            mask = torch.eye(batch_size, device=global_features.device).bool()
            similarity_matrix = similarity_matrix.masked_fill(mask, -float('inf'))
            
            # 计算对比损失
            contrastive_loss = -torch.log_softmax(similarity_matrix, dim=1).diag().mean()
            
        else:
            # 有监督对比学习：增强同类样本相似性，异类样本差异性
            batch_size = global_features.size(0)
            
            # 计算特征相似度
            similarity_matrix = torch.matmul(global_features, global_features.T) / self.temperature
            
            # 创建标签掩码
            labels = labels.view(-1, 1)
            mask = torch.eq(labels, labels.T).float()
            
            # 正样本掩码（排除自身）
            positive_mask = mask * (1 - torch.eye(batch_size, device=global_features.device))
            
            # 计算有监督对比损失
            exp_sim = torch.exp(similarity_matrix)
            positive_sum = (exp_sim * positive_mask).sum(dim=1)
            total_sum = exp_sim.sum(dim=1) - torch.diag(exp_sim)
            
            contrastive_loss = -torch.log(positive_sum / (total_sum + 1e-8)).mean()
        
        return contrastive_loss


def test_fde():
    """测试FDE模块"""
    print("测试特征判别增强器 (FDE)...")
    
    # 创建测试输入 (s3阶段：256通道)
    batch_size, channels, height, width = 2, 256, 8, 8
    x = torch.randn(batch_size, channels, height, width)
    
    # 创建FDE模块
    fde = FeatureDiscriminatorEnhancer(channels)
    
    # 前向传播
    with torch.no_grad():
        enhanced_features, contrast_info = fde(x)
    
    # 验证输出形状
    assert enhanced_features.shape == x.shape, f"输出形状不匹配: {enhanced_features.shape} vs {x.shape}"
    
    # 验证对比特征
    local_contrast = contrast_info['local_contrast']
    global_contrast = contrast_info['global_contrast']
    
    assert local_contrast.shape[0] == batch_size, "局部对比特征批次维度不匹配"
    assert global_contrast.shape == (batch_size, 128), f"全局对比特征形状不匹配: {global_contrast.shape}"
    
    # 计算参数数量
    total_params = sum(p.numel() for p in fde.parameters())
    trainable_params = sum(p.numel() for p in fde.parameters() if p.requires_grad)
    
    print(f"✅ FDE模块测试通过")
    print(f"   输入形状: {x.shape}")
    print(f"   输出形状: {enhanced_features.shape}")
    print(f"   局部对比特征: {local_contrast.shape}")
    print(f"   全局对比特征: {global_contrast.shape}")
    print(f"   总参数数: {total_params:,}")
    print(f"   可训练参数: {trainable_params:,}")
    
    # 测试对比损失计算
    contrastive_loss = fde.compute_contrastive_loss(global_contrast)
    print(f"   对比损失: {contrastive_loss.item():.4f}")
    
    return True


if __name__ == '__main__':
    test_fde()
