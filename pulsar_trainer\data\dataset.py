"""
Dataset classes for CoAtNet pulsar classification.

This module provides dataset classes for loading and preprocessing
pulsar data from FPP and TPP modalities with configuration-driven augmentation.

Version 3.0.0 - Standardized single-channel architecture
"""

import numpy as np
import torch
from torch.utils.data import Dataset, DataLoader
from pathlib import Path
from typing import Tuple, List, Optional, Dict, Any
import logging

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from data.transforms import ChannelTransformer
from data.augmentation import DataAugmentation


class DataLoadError(Exception):
    """Data loading related errors."""
    pass


class PulsarDataset(Dataset):
    """重构的脉冲星数据集类"""

    def __init__(
        self,
        modality: str,
        split: str,
        data_root: str,
        augmentation_config: Optional = None,
        normalize: bool = True,
        transform: Optional[callable] = None
    ):
        """
        初始化数据集

        Args:
            modality: 数据模态 ('FPP' 或 'TPP')
            split: 数据分割 ('train', 'validation', 'test')
            data_root: 数据根目录
            augmentation_config: 数据增强配置
            normalize: 是否标准化数据
            transform: 额外的变换
        """
        self.modality = modality
        self.split = split
        self.data_root = Path(data_root)
        self.normalize = normalize
        self.transform = transform

        # 初始化通道转换器（单通道）
        self.channel_transformer = ChannelTransformer(keep_single_channel=True)

        # 初始化数据增强器
        if augmentation_config is not None:
            self.augmentation = DataAugmentation(augmentation_config)
        else:
            self.augmentation = None

        # 设置日志
        self.logger = logging.getLogger(__name__)

        # 验证输入
        self._validate_inputs()

        # 加载文件路径和标签
        self.file_paths, self.labels = self._discover_files()

        # 记录数据集信息
        self._log_dataset_info()

    def _validate_inputs(self) -> None:
        """Validate input parameters."""
        # Validate modality
        valid_modalities = ['FPP', 'TPP']
        if self.modality not in valid_modalities:
            raise ValueError(f"Invalid modality: {self.modality}. Must be one of {valid_modalities}")

        # Validate split
        valid_splits = ['train', 'validation', 'test']
        if self.split not in valid_splits:
            raise ValueError(f"Invalid split: {self.split}. Must be one of {valid_splits}")

        # Check data directory exists
        data_dir = self.data_root / self.modality / self.split
        if not data_dir.exists():
            raise DataLoadError(f"Data directory does not exist: {data_dir}")

    def _discover_files(self) -> Tuple[List[Path], List[int]]:
        """
        Discover data files and parse labels from filenames.

        Expected filename patterns:
        - Positive samples: *_positive.npy
        - Negative samples: *_negative.npy

        Returns:
            Tuple of (file_paths, labels)
        """
        data_dir = self.data_root / self.modality / self.split

        # Find all .npy files
        npy_files = list(data_dir.glob('*.npy'))

        if not npy_files:
            raise DataLoadError(f"No .npy files found in {data_dir}")

        file_paths = []
        labels = []

        for file_path in npy_files:
            filename = file_path.name.lower()

            if 'positive' in filename:
                labels.append(1)  # Pulsar
                file_paths.append(file_path)
            elif 'negative' in filename:
                labels.append(0)  # Non-pulsar
                file_paths.append(file_path)
            else:
                self.logger.warning(f"Skipping file with unknown label pattern: {file_path}")
                continue

        if not file_paths:
            raise DataLoadError(f"No valid files found in {data_dir}")

        return file_paths, labels

    def _log_dataset_info(self) -> None:
        """Log dataset information."""
        total_files = len(self.file_paths)
        positive_count = sum(self.labels)
        negative_count = total_files - positive_count

        self.logger.info(f"Dataset {self.modality}/{self.split}:")
        self.logger.info(f"  Total files: {total_files}")
        self.logger.info(f"  Positive samples: {positive_count}")
        self.logger.info(f"  Negative samples: {negative_count}")
        self.logger.info(f"  Balance ratio: {positive_count/total_files:.2f}")

    def __len__(self) -> int:
        """Return dataset size."""
        return len(self.file_paths)

    def __getitem__(self, idx: int) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        获取数据样本

        Args:
            idx: 样本索引

        Returns:
            (data, label) 张量元组
        """
        try:
            # 加载数据
            data_path = self.file_paths[idx]
            data_1ch = np.load(data_path)

            # 验证数据形状
            if data_1ch.shape != (64, 64, 1) and data_1ch.shape != (64, 64):
                raise DataLoadError(f"Invalid data shape: {data_1ch.shape}")

            # 确保2D格式
            if data_1ch.ndim == 3:
                data_2d = data_1ch.squeeze()
            else:
                data_2d = data_1ch

            # 应用数据增强（仅在训练时且配置启用时）
            if self.split == 'train' and self.augmentation is not None:
                data_2d = self.augmentation.apply_augmentation(data_2d, self.modality)

            # 转换为单通道格式
            data_1ch_formatted = self.channel_transformer.transform(data_2d, self.modality)

            # 标准化
            if self.normalize:
                data_1ch_formatted = self._normalize_data(data_1ch_formatted)

            # 应用额外变换
            if self.transform:
                data_1ch_formatted = self.transform(data_1ch_formatted)

            # 转换为张量
            data_tensor = torch.from_numpy(data_1ch_formatted).float()
            label_tensor = torch.tensor(self.labels[idx], dtype=torch.long)

            return data_tensor, label_tensor

        except Exception as e:
            raise DataLoadError(f"Failed to load sample {idx}: {e}")

    def _normalize_data(self, data: np.ndarray) -> np.ndarray:
        """
        标准化数据到[0, 1]范围

        适配单通道数据格式

        Args:
            data: 输入数据，形状为 (1, 64, 64)

        Returns:
            标准化后的数据
        """
        normalized = data.copy()
        for i in range(data.shape[0]):
            channel = data[i]
            ch_min, ch_max = channel.min(), channel.max()
            if ch_max > ch_min:
                normalized[i] = (channel - ch_min) / (ch_max - ch_min)
            # 如果ch_max == ch_min，保持原始值

        return normalized

    def get_class_distribution(self) -> Dict[str, int]:
        """Get class distribution."""
        positive_count = sum(self.labels)
        negative_count = len(self.labels) - positive_count

        return {
            'positive': positive_count,
            'negative': negative_count,
            'total': len(self.labels)
        }

    def get_sample_info(self, idx: int) -> Dict[str, Any]:
        """Get information about a specific sample."""
        return {
            'index': idx,
            'file_path': str(self.file_paths[idx]),
            'label': self.labels[idx],
            'label_name': 'positive' if self.labels[idx] == 1 else 'negative'
        }


def create_dataloaders_with_config(
    config: Dict[str, Any]
) -> Tuple[DataLoader, DataLoader, DataLoader]:
    """
    使用新配置系统创建数据加载器

    Args:
        config: 完整配置字典

    Returns:
        (train_loader, val_loader, test_loader)
    """
    # 提取配置
    data_config = config['data']
    training_config = config['training']
    augmentation_config = config.get('augmentation')

    # 创建数据集
    train_dataset = PulsarDataset(
        modality=data_config.modality,
        split='train',
        data_root=data_config.data_root,
        augmentation_config=augmentation_config,  # 传递增强配置
        normalize=data_config.normalize
    )

    val_dataset = PulsarDataset(
        modality=data_config.modality,
        split='validation',
        data_root=data_config.data_root,
        augmentation_config=None,  # 验证集不使用增强
        normalize=data_config.normalize
    )

    test_dataset = PulsarDataset(
        modality=data_config.modality,
        split='test',
        data_root=data_config.data_root,
        augmentation_config=None,  # 测试集不使用增强
        normalize=data_config.normalize
    )

    # 创建数据加载器
    batch_size = training_config['batch_size']
    num_workers = data_config.num_workers
    pin_memory = data_config.pin_memory

    train_loader = DataLoader(
        train_dataset,
        batch_size=batch_size,
        shuffle=True,
        num_workers=num_workers,
        pin_memory=pin_memory and torch.cuda.is_available(),
        persistent_workers=num_workers > 0,
        prefetch_factor=2 if num_workers > 0 else 2
    )

    val_loader = DataLoader(
        val_dataset,
        batch_size=batch_size,
        shuffle=False,
        num_workers=num_workers,
        pin_memory=pin_memory and torch.cuda.is_available(),
        persistent_workers=num_workers > 0,
        prefetch_factor=2 if num_workers > 0 else 2
    )

    test_loader = DataLoader(
        test_dataset,
        batch_size=batch_size,
        shuffle=False,
        num_workers=num_workers,
        pin_memory=pin_memory and torch.cuda.is_available(),
        persistent_workers=num_workers > 0,
        prefetch_factor=2 if num_workers > 0 else 2
    )

    return train_loader, val_loader, test_loader


def create_dataloaders(
    config: Dict[str, Any],
    batch_size: Optional[int] = None,
    num_workers: Optional[int] = None
) -> Tuple[DataLoader, DataLoader, DataLoader]:
    """
    Create train, validation, and test dataloaders.

    Args:
        config: Configuration dictionary
        batch_size: Override batch size from config
        num_workers: Override num_workers from config

    Returns:
        Tuple of (train_loader, val_loader, test_loader)
    """
    # Extract configuration
    data_config = config['data']
    training_config = config['training']

    # 兼容新旧配置系统
    if hasattr(training_config, 'batch_size'):
        batch_size = batch_size or training_config.batch_size
    else:
        batch_size = batch_size or training_config['batch_size']

    if hasattr(data_config, 'num_workers'):
        num_workers = num_workers or data_config.num_workers
        pin_memory = data_config.pin_memory
    else:
        num_workers = num_workers or data_config.get('num_workers', 4)
        pin_memory = data_config.get('pin_memory', True)

    # Create datasets (legacy function - use create_dataloaders_with_config for new code)
    # 兼容新旧配置系统
    if hasattr(data_config, 'modality'):
        modality = data_config.modality
        data_root = data_config.data_root
        normalize = data_config.normalize
    else:
        modality = data_config['modality']
        data_root = data_config['data_root']
        normalize = data_config.get('normalize', True)

    train_dataset = PulsarDataset(
        modality=modality,
        split='train',
        data_root=data_root,
        normalize=normalize
    )

    val_dataset = PulsarDataset(
        modality=modality,
        split='validation',
        data_root=data_root,
        normalize=normalize
    )

    test_dataset = PulsarDataset(
        modality=modality,
        split='test',
        data_root=data_root,
        normalize=normalize
    )

    # Create dataloaders
    train_loader = DataLoader(
        train_dataset,
        batch_size=batch_size,
        shuffle=True,
        num_workers=num_workers,
        pin_memory=pin_memory and torch.cuda.is_available(),
        persistent_workers=num_workers > 0,
        prefetch_factor=2 if num_workers > 0 else 2
    )

    val_loader = DataLoader(
        val_dataset,
        batch_size=batch_size,
        shuffle=False,
        num_workers=num_workers,
        pin_memory=pin_memory and torch.cuda.is_available(),
        persistent_workers=num_workers > 0,
        prefetch_factor=2 if num_workers > 0 else 2
    )

    test_loader = DataLoader(
        test_dataset,
        batch_size=batch_size,
        shuffle=False,
        num_workers=num_workers,
        pin_memory=pin_memory and torch.cuda.is_available(),
        persistent_workers=num_workers > 0,
        prefetch_factor=2 if num_workers > 0 else 2
    )

    return train_loader, val_loader, test_loader


def get_dataset_info(config: Dict[str, Any]) -> Dict[str, Any]:
    """
    Get comprehensive dataset information.

    Args:
        config: Configuration dictionary

    Returns:
        Dataset information dictionary
    """
    data_config = config['data']

    info = {
        'modality': data_config.modality,
        'channel_strategy': 'single_channel',  # 统一使用单通道策略
        'data_root': data_config.data_root,
        'splits': {}
    }

    # Get info for each split
    for split in ['train', 'validation', 'test']:
        try:
            dataset = PulsarDataset(
                modality=data_config.modality,
                split=split,
                data_root=data_config.data_root
            )

            distribution = dataset.get_class_distribution()
            info['splits'][split] = {
                'size': len(dataset),
                'positive': distribution['positive'],
                'negative': distribution['negative'],
                'balance_ratio': distribution['positive'] / distribution['total']
            }

        except Exception as e:
            info['splits'][split] = {'error': str(e)}

    return info
