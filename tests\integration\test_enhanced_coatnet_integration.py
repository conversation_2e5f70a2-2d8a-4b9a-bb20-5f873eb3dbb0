"""
Enhanced CoAtNet Integration Tests
测试增强版CoAtNet的完整集成功能
"""

import torch
import torch.nn as nn
import unittest
import sys
import os
import yaml

# 添加路径以便导入模块
sys.path.append('pulsar_trainer/models')

from coatnet import CoAtNet, create_coatnet_from_config
from enhancement_manager import EnhancementManager


class MockConfig:
    """模拟配置对象"""
    def __init__(self, with_enhancement=True):
        self.image_size = [64, 64]
        self.in_channels = 1
        self.num_blocks = [2, 2, 3, 5, 2]
        self.channels = [64, 96, 192, 384, 768]
        self.num_classes = 2
        self.block_types = ['C', 'C', 'T', 'T']
        
        if with_enhancement:
            self.enhancement_modules = {
                'fpp': {
                    'freq_fusion_config': {
                        'compressed_channels': 24,
                        'use_high_pass': True,
                        'use_low_pass': True
                    },
                    'enabled': True
                },
                'tpp': {
                    'cbam_config': {
                        'ratio': 16,
                        'kernel_size': 7
                    },
                    'loss_config': {
                        'beta': 1.0,
                        'alpha': 0.5,
                        'gamma': 0.3
                    },
                    'enabled': True
                }
            }
        else:
            self.enhancement_modules = None
    
    def validate(self):
        """配置验证"""
        pass


class TestEnhancedCoAtNetIntegration(unittest.TestCase):
    """增强版CoAtNet集成测试"""
    
    def setUp(self):
        """测试初始化"""
        self.batch_size = 2
        self.input_shape = (self.batch_size, 1, 64, 64)
        self.num_classes = 2
    
    def test_enhanced_model_creation(self):
        """测试增强版模型创建"""
        config = MockConfig(with_enhancement=True)
        model = create_coatnet_from_config(config)
        
        self.assertIsInstance(model, CoAtNet)
        self.assertIsNotNone(model.enhancement_manager)
        self.assertIsInstance(model.enhancement_manager, EnhancementManager)
        
        print("✓ Enhanced CoAtNet creation test passed")
    
    def test_non_enhanced_model_creation(self):
        """测试非增强版模型创建"""
        config = MockConfig(with_enhancement=False)
        model = create_coatnet_from_config(config)
        
        self.assertIsInstance(model, CoAtNet)
        self.assertIsNone(model.enhancement_manager)
        
        print("✓ Non-enhanced CoAtNet creation test passed")
    
    def test_forward_pass_with_enhancements(self):
        """测试带增强的前向传播"""
        config = MockConfig(with_enhancement=True)
        model = create_coatnet_from_config(config)
        
        x = torch.randn(*self.input_shape)
        
        with torch.no_grad():
            output = model(x)
        
        expected_shape = (self.batch_size, self.num_classes)
        self.assertEqual(output.shape, expected_shape)
        
        print(f"✓ Enhanced forward pass test passed: {self.input_shape} -> {output.shape}")
    
    def test_forward_pass_without_enhancements(self):
        """测试不带增强的前向传播"""
        config = MockConfig(with_enhancement=False)
        model = create_coatnet_from_config(config)
        
        x = torch.randn(*self.input_shape)
        
        with torch.no_grad():
            output = model(x)
        
        expected_shape = (self.batch_size, self.num_classes)
        self.assertEqual(output.shape, expected_shape)
        
        print(f"✓ Non-enhanced forward pass test passed: {self.input_shape} -> {output.shape}")
    
    def test_enhancement_activation_by_stage(self):
        """测试不同阶段的增强激活"""
        config = MockConfig(with_enhancement=True)
        model = create_coatnet_from_config(config)
        
        x = torch.randn(*self.input_shape)
        
        # 重置统计
        model.reset_enhancement_stats()
        
        # 执行前向传播
        with torch.no_grad():
            output = model(x)
        
        # 检查统计信息
        stats = model.get_enhancement_stats()
        
        self.assertIsNotNone(stats)
        self.assertIn('manager', stats)
        self.assertGreater(stats['manager']['total_forwards'], 0)
        
        print("✓ Enhancement activation by stage test passed")
    
    def test_modality_switching(self):
        """测试模态切换功能"""
        config = MockConfig(with_enhancement=True)
        model = create_coatnet_from_config(config)
        
        x = torch.randn(*self.input_shape)
        
        # 测试不同模态
        modalities = ['FPP', 'TPP', 'AUTO']
        
        for modality in modalities:
            model.set_enhancement_modality(modality)
            
            with torch.no_grad():
                output = model(x)
            
            expected_shape = (self.batch_size, self.num_classes)
            self.assertEqual(output.shape, expected_shape)
        
        print("✓ Modality switching test passed")
    
    def test_enhancement_enable_disable(self):
        """测试增强功能启用/禁用"""
        config = MockConfig(with_enhancement=True)
        model = create_coatnet_from_config(config)
        
        x = torch.randn(*self.input_shape)
        
        # 启用状态
        model.enable_enhancements()
        with torch.no_grad():
            output_enabled = model(x)
        
        # 禁用状态
        model.disable_enhancements()
        with torch.no_grad():
            output_disabled = model(x)
        
        # 两种状态的输出形状应该相同
        self.assertEqual(output_enabled.shape, output_disabled.shape)
        
        print("✓ Enhancement enable/disable test passed")
    
    def test_enhanced_loss_computation(self):
        """测试增强损失计算"""
        config = MockConfig(with_enhancement=True)
        model = create_coatnet_from_config(config)
        
        predictions = torch.randn(self.batch_size, self.num_classes)
        targets = torch.randint(0, self.num_classes, (self.batch_size,))
        
        # 计算增强损失
        enhanced_loss = model.compute_enhanced_loss(predictions, targets)
        
        if enhanced_loss is not None:
            self.assertIsInstance(enhanced_loss, torch.Tensor)
            self.assertEqual(enhanced_loss.dim(), 0)  # 标量
            self.assertGreater(enhanced_loss.item(), 0)
        
        print("✓ Enhanced loss computation test passed")
    
    def test_loss_components_analysis(self):
        """测试损失组件分析"""
        config = MockConfig(with_enhancement=True)
        model = create_coatnet_from_config(config)
        
        predictions = torch.randn(self.batch_size, self.num_classes)
        targets = torch.randint(0, self.num_classes, (self.batch_size,))
        
        # 获取损失组件
        loss_components = model.get_loss_components(predictions, targets)
        
        if loss_components is not None:
            self.assertIsInstance(loss_components, dict)
            
            expected_components = ['ce_loss', 'fd_loss', 'vd_loss', 'total_loss']
            for component in expected_components:
                if component in loss_components:
                    self.assertIsInstance(loss_components[component], float)
        
        print("✓ Loss components analysis test passed")
    
    def test_gradient_flow(self):
        """测试梯度流"""
        config = MockConfig(with_enhancement=True)
        model = create_coatnet_from_config(config)
        
        x = torch.randn(*self.input_shape, requires_grad=True)
        predictions = model(x)
        targets = torch.randint(0, self.num_classes, (self.batch_size,))
        
        # 标准损失
        standard_loss = nn.CrossEntropyLoss()(predictions, targets)
        
        # 增强损失
        enhanced_loss = model.compute_enhanced_loss(predictions, targets)
        
        # 组合损失
        if enhanced_loss is not None:
            total_loss = 0.7 * standard_loss + 0.3 * enhanced_loss
        else:
            total_loss = standard_loss
        
        # 反向传播
        total_loss.backward()
        
        # 检查梯度
        self.assertIsNotNone(x.grad)
        self.assertFalse(torch.isnan(x.grad).any())
        self.assertFalse(torch.isinf(x.grad).any())
        
        print("✓ Gradient flow test passed")


class TestConfigurationIntegration(unittest.TestCase):
    """配置集成测试"""
    
    def test_yaml_config_loading(self):
        """测试YAML配置加载"""
        try:
            with open('pulsar_trainer/config/coatnet_config.yaml', 'r', encoding='utf-8') as f:
                config_dict = yaml.safe_load(f)
            
            # 检查增强模块配置
            self.assertIn('enhancement_modules', config_dict)
            enhancement_config = config_dict['enhancement_modules']
            
            self.assertIn('enabled', enhancement_config)
            self.assertIn('fpp', enhancement_config)
            self.assertIn('tpp', enhancement_config)
            
            print("✓ YAML config loading test passed")
            
        except Exception as e:
            self.fail(f"YAML config loading failed: {e}")
    
    def test_config_driven_model_creation(self):
        """测试配置驱动的模型创建"""
        # 这里应该使用实际的配置类，但为了测试简化使用MockConfig
        config = MockConfig(with_enhancement=True)
        
        try:
            model = create_coatnet_from_config(config)
            self.assertIsInstance(model, CoAtNet)
            
            # 验证增强模块配置
            if model.enhancement_manager:
                info = model.enhancement_manager.get_module_info()
                self.assertIn('fpp_available', info)
                self.assertIn('tpp_available', info)
            
            print("✓ Config-driven model creation test passed")
            
        except Exception as e:
            self.fail(f"Config-driven model creation failed: {e}")


class TestPerformanceRegression(unittest.TestCase):
    """性能回归测试"""
    
    def test_parameter_overhead(self):
        """测试参数开销"""
        # 基础模型
        config_base = MockConfig(with_enhancement=False)
        model_base = create_coatnet_from_config(config_base)
        base_params = sum(p.numel() for p in model_base.parameters() if p.requires_grad)
        
        # 增强模型
        config_enhanced = MockConfig(with_enhancement=True)
        model_enhanced = create_coatnet_from_config(config_enhanced)
        enhanced_params = sum(p.numel() for p in model_enhanced.parameters() if p.requires_grad)
        
        # 计算开销
        overhead = enhanced_params - base_params
        relative_overhead = overhead / base_params * 100
        
        # 开销应该合理（< 5%）
        self.assertLess(relative_overhead, 5.0, "Parameter overhead should be < 5%")
        
        print(f"✓ Parameter overhead test passed: {overhead:,} params ({relative_overhead:.2f}%)")
    
    def test_inference_speed(self):
        """测试推理速度"""
        config = MockConfig(with_enhancement=True)
        model = create_coatnet_from_config(config)
        model.eval()
        
        x = torch.randn(4, 1, 64, 64)  # 稍大的批次
        
        # 预热
        with torch.no_grad():
            for _ in range(5):
                _ = model(x)
        
        # 计时
        import time
        start_time = time.time()
        
        with torch.no_grad():
            for _ in range(10):
                _ = model(x)
        
        end_time = time.time()
        avg_time = (end_time - start_time) / 10
        
        # 推理时间应该合理（< 0.1秒每批次）
        self.assertLess(avg_time, 0.1, "Inference time should be < 0.1s per batch")
        
        print(f"✓ Inference speed test passed: {avg_time:.4f}s per batch")


def run_integration_tests():
    """运行所有集成测试"""
    print("="*60)
    print("Enhanced CoAtNet Integration Tests")
    print("="*60)
    
    # 创建测试套件
    test_classes = [
        TestEnhancedCoAtNetIntegration,
        TestConfigurationIntegration,
        TestPerformanceRegression
    ]
    
    total_tests = 0
    passed_tests = 0
    
    for test_class in test_classes:
        print(f"\n--- Testing {test_class.__name__} ---")
        
        # 获取测试方法
        test_methods = [method for method in dir(test_class) if method.startswith('test_')]
        
        for method_name in test_methods:
            total_tests += 1
            try:
                # 创建测试实例并运行测试
                test_instance = test_class()
                test_instance.setUp() if hasattr(test_instance, 'setUp') else None
                test_method = getattr(test_instance, method_name)
                test_method()
                passed_tests += 1
            except Exception as e:
                print(f"✗ {method_name} failed: {e}")
    
    print("\n" + "="*60)
    print(f"Integration Tests Results: {passed_tests}/{total_tests} tests passed")
    print("="*60)
    
    return passed_tests == total_tests


if __name__ == '__main__':
    success = run_integration_tests()
    
    if success:
        print("\n🎉 All integration tests passed!")
    else:
        print("\n❌ Some integration tests failed!")
        exit(1)
