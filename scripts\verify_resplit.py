#!/usr/bin/env python3
"""
Verify the resplit dataset meets all requirements.
"""

import numpy as np
from pathlib import Path
import json

def verify_dataset(data_root: str, expected_splits: dict):
    """Verify dataset meets all requirements."""
    data_root = Path(data_root)
    
    verification = {
        'overall_success': True,
        'modalities': {},
        'issues': []
    }
    
    for modality in ['FPP', 'TPP']:
        modality_path = data_root / modality
        modality_verification = {
            'success': True,
            'splits': {},
            'issues': []
        }
        
        for split_name, expected_size in expected_splits.items():
            split_path = modality_path / split_name
            
            if not split_path.exists():
                issue = f"Missing split directory: {modality}/{split_name}"
                modality_verification['issues'].append(issue)
                modality_verification['success'] = False
                continue
            
            # Count files
            positive_files = list(split_path.glob('*positive*.npy'))
            negative_files = list(split_path.glob('*negative*.npy'))
            
            total_files = len(positive_files) + len(negative_files)
            
            split_verification = {
                'positive': len(positive_files),
                'negative': len(negative_files),
                'total': total_files,
                'expected': expected_size,
                'size_correct': total_files == expected_size,
                'balance_correct': len(positive_files) == len(negative_files),
                'balance_ratio': len(positive_files) / total_files if total_files > 0 else 0
            }
            
            # Check requirements
            if not split_verification['size_correct']:
                issue = f"{modality}/{split_name}: Size mismatch. Got {total_files}, expected {expected_size}"
                modality_verification['issues'].append(issue)
                modality_verification['success'] = False
            
            if not split_verification['balance_correct']:
                issue = f"{modality}/{split_name}: Balance incorrect. Positive: {len(positive_files)}, Negative: {len(negative_files)}"
                modality_verification['issues'].append(issue)
                modality_verification['success'] = False
            
            # Verify data integrity (sample a few files)
            sample_files = positive_files[:3] + negative_files[:3]
            for file_path in sample_files:
                try:
                    data = np.load(file_path)
                    if data.shape not in [(64, 64), (64, 64, 1)]:
                        issue = f"Invalid data shape in {file_path}: {data.shape}"
                        modality_verification['issues'].append(issue)
                        modality_verification['success'] = False
                except Exception as e:
                    issue = f"Cannot load {file_path}: {e}"
                    modality_verification['issues'].append(issue)
                    modality_verification['success'] = False
            
            modality_verification['splits'][split_name] = split_verification
        
        verification['modalities'][modality] = modality_verification
        if not modality_verification['success']:
            verification['overall_success'] = False
        
        verification['issues'].extend(modality_verification['issues'])
    
    return verification

def generate_verification_report(verification: dict):
    """Generate a human-readable verification report."""
    report = []
    
    report.append("# 数据集重新划分验证报告")
    report.append("=" * 50)
    report.append("")
    
    if verification['overall_success']:
        report.append("🎉 **整体验证结果: 成功**")
    else:
        report.append("❌ **整体验证结果: 失败**")
    
    report.append("")
    
    # Detailed results for each modality
    for modality, mod_data in verification['modalities'].items():
        report.append(f"## {modality} 模态")
        
        if mod_data['success']:
            report.append("✅ **验证通过**")
        else:
            report.append("❌ **验证失败**")
        
        report.append("")
        report.append("### 分割详情")
        
        for split_name, split_data in mod_data['splits'].items():
            report.append(f"**{split_name}:**")
            report.append(f"- 正样本: {split_data['positive']}")
            report.append(f"- 负样本: {split_data['negative']}")
            report.append(f"- 总计: {split_data['total']} (期望: {split_data['expected']})")
            report.append(f"- 平衡比例: {split_data['balance_ratio']:.3f}")
            
            if split_data['size_correct'] and split_data['balance_correct']:
                report.append("- 状态: ✅ 正确")
            else:
                report.append("- 状态: ❌ 错误")
            
            report.append("")
        
        if mod_data['issues']:
            report.append("### 发现的问题")
            for issue in mod_data['issues']:
                report.append(f"- {issue}")
            report.append("")
    
    # Overall summary
    report.append("## 总结")
    total_modalities = len(verification['modalities'])
    successful_modalities = sum(1 for mod_data in verification['modalities'].values() if mod_data['success'])
    
    report.append(f"- 总模态数: {total_modalities}")
    report.append(f"- 成功验证: {successful_modalities}")
    report.append(f"- 失败验证: {total_modalities - successful_modalities}")
    
    if verification['issues']:
        report.append(f"- 总问题数: {len(verification['issues'])}")
    
    return "\n".join(report)

if __name__ == "__main__":
    data_root = "D:/pulsarSuanfa/datasets/HTRU"
    expected_splits = {
        'train': 1674,
        'validation': 360,
        'test': 358
    }
    
    print("🔍 开始验证重新划分的数据集...")
    
    verification = verify_dataset(data_root, expected_splits)
    
    # Save verification results
    with open("dataset_verification.json", "w") as f:
        json.dump(verification, f, indent=2)
    
    print("🔍 数据集验证结果:")
    print(json.dumps(verification, indent=2))
    
    # Generate and save report
    report = generate_verification_report(verification)
    with open("dataset_verification_report.md", "w", encoding='utf-8') as f:
        f.write(report)
    
    print(f"\n📋 详细报告已保存至: dataset_verification_report.md")
    
    if verification['overall_success']:
        print("\n🎉 数据集验证完全通过！")
        print("✅ 所有分割的样本数量正确")
        print("✅ 所有分割的正负样本比例为1:1")
        print("✅ 数据文件完整性验证通过")
    else:
        print("\n❌ 数据集验证失败，存在以下问题:")
        for issue in verification['issues']:
            print(f"  - {issue}")
    
    # Print summary statistics
    print(f"\n📊 数据集统计:")
    for modality, mod_data in verification['modalities'].items():
        print(f"\n{modality} 模态:")
        for split_name, split_data in mod_data['splits'].items():
            print(f"  {split_name}: {split_data['positive']}正 + {split_data['negative']}负 = {split_data['total']}总")
