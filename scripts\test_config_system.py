#!/usr/bin/env python3
"""
Test script for validating the corrected configuration system.
"""

import sys
import torch
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from pulsar_trainer.models.coatnet import create_coatnet_from_config
from pulsar_trainer.utils.config import load_config, validate_config

def test_config_system():
    """Test the corrected configuration system."""
    print("🧪 测试配置系统修正...")
    
    # Test configurations
    test_configs = [
        {
            'name': '全卷积',
            'block_types': ['C', 'C', 'C', 'C']
        },
        {
            'name': '卷积为主',
            'block_types': ['C', 'C', 'C', 'T']
        },
        {
            'name': '当前配置',
            'block_types': ['C', 'C', 'T', 'T']
        },
        {
            'name': 'Transformer为主',
            'block_types': ['C', 'T', 'T', 'T']
        },
        {
            'name': '全Transformer',
            'block_types': ['T', 'T', 'T', 'T']
        }
    ]
    
    base_config = {
        'num_blocks': [2, 2, 6, 8, 2],
        'channels': [96, 128, 256, 512, 1024],
        'image_size': [64, 64],
        'in_channels': 1,
        'num_classes': 2,
        'dropout': 0.2
    }
    
    results = []
    
    for test_config in test_configs:
        print(f"\n📋 测试配置: {test_config['name']}")
        
        # Create model config
        model_config = base_config.copy()
        model_config['block_types'] = test_config['block_types']
        
        try:
            # Create model
            model = create_coatnet_from_config(model_config)
            
            # Count parameters
            total_params = sum(p.numel() for p in model.parameters())
            
            # Test forward pass
            dummy_input = torch.randn(1, 1, 64, 64)
            with torch.no_grad():
                output = model(dummy_input)
            
            result = {
                'name': test_config['name'],
                'block_types': test_config['block_types'],
                'total_params': total_params,
                'output_shape': output.shape,
                'success': True
            }
            
            print(f"✅ 成功 - 参数数量: {total_params:,}")
            print(f"   输出形状: {output.shape}")
            
        except Exception as e:
            result = {
                'name': test_config['name'],
                'block_types': test_config['block_types'],
                'error': str(e),
                'success': False
            }
            print(f"❌ 失败: {e}")
        
        results.append(result)
    
    # Summary
    print("\n📊 测试结果汇总:")
    successful = sum(1 for r in results if r['success'])
    print(f"成功: {successful}/{len(results)}")
    
    # Check parameter differences
    if successful > 1:
        param_counts = [r['total_params'] for r in results if r['success']]
        unique_counts = set(param_counts)
        print(f"不同的参数数量: {len(unique_counts)} 种")
        
        if len(unique_counts) > 1:
            print("✅ 不同配置产生了不同的模型架构！")
        else:
            print("⚠️ 所有配置产生了相同的参数数量，可能存在问题。")
    
    if successful == len(results):
        print("🎉 所有配置测试通过！")
        return True
    else:
        print("⚠️ 部分配置测试失败，需要检查。")
        return False

def test_validation_functions():
    """Test configuration validation functions."""
    print("\n🔍 测试配置验证功能...")
    
    # Test valid configuration
    valid_config = {
        'num_blocks': [2, 2, 6, 8, 2],
        'channels': [96, 128, 256, 512, 1024],
        'block_types': ['C', 'C', 'T', 'T'],
        'image_size': [64, 64],
        'in_channels': 1,
        'num_classes': 2,
        'dropout': 0.2
    }
    
    try:
        model = create_coatnet_from_config(valid_config)
        print("✅ 有效配置验证通过")
    except Exception as e:
        print(f"❌ 有效配置验证失败: {e}")
        return False
    
    # Test invalid configurations
    invalid_configs = [
        {
            'name': 'num_blocks长度错误',
            'config': {**valid_config, 'num_blocks': [2, 2, 6, 8]},  # 缺少一个元素
            'expected_error': 'num_blocks must have 5 elements'
        },
        {
            'name': 'block_types长度错误',
            'config': {**valid_config, 'block_types': ['C', 'C', 'T']},  # 缺少一个元素
            'expected_error': 'block_types must have 4 elements'
        },
        {
            'name': '无效的block_type',
            'config': {**valid_config, 'block_types': ['C', 'C', 'X', 'T']},  # 无效的块类型
            'expected_error': 'Invalid block type'
        }
    ]
    
    validation_success = True
    for invalid_config in invalid_configs:
        try:
            model = create_coatnet_from_config(invalid_config['config'])
            print(f"❌ {invalid_config['name']}: 应该失败但成功了")
            validation_success = False
        except Exception as e:
            if invalid_config['expected_error'] in str(e):
                print(f"✅ {invalid_config['name']}: 正确捕获错误")
            else:
                print(f"⚠️ {invalid_config['name']}: 错误信息不匹配 - {e}")
                validation_success = False
    
    return validation_success

if __name__ == "__main__":
    print("🚀 开始配置系统测试...")
    
    # Test main functionality
    main_success = test_config_system()
    
    # Test validation
    validation_success = test_validation_functions()
    
    overall_success = main_success and validation_success
    
    print(f"\n{'='*60}")
    if overall_success:
        print("🎉 配置系统修正验证完全成功！")
        print("✅ 不同配置能够创建不同的模型架构")
        print("✅ 配置验证功能正常工作")
    else:
        print("❌ 配置系统修正验证失败")
        if not main_success:
            print("  - 主要功能测试失败")
        if not validation_success:
            print("  - 验证功能测试失败")
    
    sys.exit(0 if overall_success else 1)
