"""
Enhancement Manager - 增强模块统一管理器
负责协调FPP和TPP增强模块的工作，提供统一的接口和控制逻辑

功能:
- 统一管理FPP和TPP增强模块
- 模态感知的条件激活
- 性能监控和统计
- 配置热更新支持

版本: 1.0.0
作者: Pulsar Classification Enhancement Team
日期: 2025-01-31
"""

import torch
import torch.nn as nn
from typing import Optional, Dict, Any, Union, Tuple
import warnings

try:
    from .fpp_enhancement_module import FPPEnhancementModule, create_fpp_enhancement, validate_fpp_config
    from .tpp_enhancement_module import TPPEnhancementModule, create_tpp_enhancement, validate_tpp_config
except ImportError:
    # 用于测试时的绝对导入
    from fpp_enhancement_module import FPPEnhancementModule, create_fpp_enhancement, validate_fpp_config
    from tpp_enhancement_module import TPPEnhancementModule, create_tpp_enhancement, validate_tpp_config


class EnhancementManager(nn.Module):
    """
    增强模块管理器

    统一管理FPP和TPP增强模块，根据当前模态和训练阶段
    自动激活相应的增强功能，提供完整的性能监控和配置管理。

    Args:
        config: 增强模块配置字典
        modality: 当前数据模态 ('FPP', 'TPP', 'AUTO')
        enabled: 是否启用增强功能
    """

    def __init__(self,
                 config: Dict[str, Any],
                 modality: str = 'AUTO',
                 enabled: bool = True):
        super().__init__()

        self.config = config
        self.current_modality = modality.upper()
        self.enabled = enabled

        # 验证模态参数
        assert self.current_modality in ['FPP', 'TPP', 'AUTO'], \
            f"Modality must be 'FPP', 'TPP', or 'AUTO', got {self.current_modality}"

        # 初始化FPP增强模块
        self.fpp_module = None
        if 'fpp' in config and config['fpp'].get('enabled', True):
            try:
                self.fpp_module = create_fpp_enhancement(config['fpp'])
                print("✓ FPP Enhancement Module initialized successfully")
            except Exception as e:
                warnings.warn(f"Failed to initialize FPP module: {e}")

        # 初始化TPP增强模块
        self.tpp_module = None
        if 'tpp' in config and config['tpp'].get('enabled', True):
            try:
                self.tpp_module = create_tpp_enhancement(config['tpp'])
                print("✓ TPP Enhancement Module initialized successfully")
            except Exception as e:
                warnings.warn(f"Failed to initialize TPP module: {e}")

        # 性能监控
        self.register_buffer('total_forwards', torch.tensor(0, dtype=torch.long))
        self.register_buffer('fpp_forwards', torch.tensor(0, dtype=torch.long))
        self.register_buffer('tpp_forwards', torch.tensor(0, dtype=torch.long))
        self.register_buffer('loss_computations', torch.tensor(0, dtype=torch.long))

        # 阶段映射：定义在哪些阶段激活哪些模块
        # 阶段1调整：FPP从s2迁移到s3，获得更丰富的语义信息
        self.stage_mapping = {
            's2': 'NONE',  # s2阶段不激活（原FPP阶段）
            's3': 'FPP',   # s3阶段激活FPP增强（处理s2输出，192通道）
            's4': 'TPP'    # s4阶段激活TPP增强（处理s3输出，384通道）
        }

    def forward(self, x: torch.Tensor, stage: str = 's2') -> torch.Tensor:
        """
        前向传播 - 根据阶段和模态激活相应的增强模块

        Args:
            x: 输入特征张量 [B, C, H, W]
            stage: 当前网络阶段 ('s2', 's3', 's4')

        Returns:
            enhanced_x: 增强后的特征张量
        """
        self.total_forwards += 1

        if not self.enabled:
            return x

        # 确定应该激活的模块类型
        target_modality = self._get_target_modality(stage)

        if target_modality == 'FPP' and self.fpp_module is not None:
            self.fpp_forwards += 1
            return self.fpp_module(x, modality='FPP')
        elif target_modality == 'TPP' and self.tpp_module is not None:
            self.tpp_forwards += 1
            return self.tpp_module(x, modality='TPP')
        else:
            # 如果没有对应的模块或不应该激活，直接返回输入
            return x

    def compute_enhanced_loss(self, predictions: torch.Tensor, targets: torch.Tensor) -> Optional[torch.Tensor]:
        """
        计算增强损失函数

        Args:
            predictions: 模型预测logits [B, 2]
            targets: 真实标签 [B] 或 [B, 1]

        Returns:
            loss: 增强损失值（如果TPP模块可用）或None
        """
        if not self.enabled or self.tpp_module is None:
            return None

        # 只有在TPP模态下才使用增强损失
        if self._should_use_enhanced_loss():
            self.loss_computations += 1
            return self.tpp_module.compute_loss(predictions, targets)

        return None

    def get_loss_components(self, predictions: torch.Tensor, targets: torch.Tensor) -> Optional[Dict[str, float]]:
        """获取损失组件详情"""
        if not self.enabled or self.tpp_module is None:
            return None

        if self._should_use_enhanced_loss():
            return self.tpp_module.get_loss_components(predictions, targets)

        return None

    def _get_target_modality(self, stage: str) -> str:
        """根据阶段和当前模态确定目标模态"""
        if self.current_modality == 'AUTO':
            # 自动模式：根据阶段映射确定
            return self.stage_mapping.get(stage, 'NONE')
        else:
            # 手动模式：只在对应阶段激活对应模态
            stage_modality = self.stage_mapping.get(stage, 'NONE')
            if stage_modality == self.current_modality:
                return self.current_modality
            else:
                return 'NONE'

    def _should_use_enhanced_loss(self) -> bool:
        """判断是否应该使用增强损失函数"""
        # 当前模态是TPP或AUTO模式时使用增强损失
        return self.current_modality in ['TPP', 'AUTO']

    def set_modality(self, modality: str):
        """设置当前模态"""
        modality = modality.upper()
        assert modality in ['FPP', 'TPP', 'AUTO'], \
            f"Modality must be 'FPP', 'TPP', or 'AUTO', got {modality}"
        self.current_modality = modality

    def enable(self):
        """启用增强管理器"""
        self.enabled = True
        if self.fpp_module:
            self.fpp_module.enable()
        if self.tpp_module:
            self.tpp_module.enable()

    def disable(self):
        """禁用增强管理器"""
        self.enabled = False
        if self.fpp_module:
            self.fpp_module.disable()
        if self.tpp_module:
            self.tpp_module.disable()

    def enable_fpp_only(self):
        """只启用FPP增强"""
        self.enabled = True
        if self.fpp_module:
            self.fpp_module.enable()
        if self.tpp_module:
            self.tpp_module.disable()

    def enable_tpp_only(self):
        """只启用TPP增强"""
        self.enabled = True
        if self.fpp_module:
            self.fpp_module.disable()
        if self.tpp_module:
            self.tpp_module.enable()

    def get_comprehensive_stats(self) -> Dict[str, Any]:
        """获取综合统计信息"""
        total_forwards = self.total_forwards.item()
        fpp_forwards = self.fpp_forwards.item()
        tpp_forwards = self.tpp_forwards.item()
        loss_computations = self.loss_computations.item()

        stats = {
            'manager': {
                'enabled': self.enabled,
                'current_modality': self.current_modality,
                'total_forwards': total_forwards,
                'fpp_forwards': fpp_forwards,
                'tpp_forwards': tpp_forwards,
                'loss_computations': loss_computations,
                'fpp_activation_rate': fpp_forwards / max(total_forwards, 1),
                'tpp_activation_rate': tpp_forwards / max(total_forwards, 1)
            }
        }

        # FPP模块统计
        if self.fpp_module:
            stats['fpp_module'] = self.fpp_module.get_stats()
        else:
            stats['fpp_module'] = {'status': 'not_initialized'}

        # TPP模块统计
        if self.tpp_module:
            stats['tpp_module'] = self.tpp_module.get_stats()
        else:
            stats['tpp_module'] = {'status': 'not_initialized'}

        return stats

    def reset_stats(self):
        """重置所有统计信息"""
        self.total_forwards.zero_()
        self.fpp_forwards.zero_()
        self.tpp_forwards.zero_()
        self.loss_computations.zero_()

        if self.fpp_module:
            self.fpp_module.reset_stats()
        if self.tpp_module:
            self.tpp_module.reset_stats()

    def update_config(self, new_config: Dict[str, Any]):
        """
        热更新配置（部分支持）

        Args:
            new_config: 新的配置字典
        """
        # 更新模态设置
        if 'modality' in new_config:
            self.set_modality(new_config['modality'])

        # 更新启用状态
        if 'enabled' in new_config:
            if new_config['enabled']:
                self.enable()
            else:
                self.disable()

        # 更新阶段映射
        if 'stage_mapping' in new_config:
            self.stage_mapping.update(new_config['stage_mapping'])

        print("✓ Configuration updated successfully")

    def get_module_info(self) -> Dict[str, Any]:
        """获取模块信息摘要"""
        info = {
            'manager_enabled': self.enabled,
            'current_modality': self.current_modality,
            'fpp_available': self.fpp_module is not None,
            'tpp_available': self.tpp_module is not None,
            'stage_mapping': self.stage_mapping
        }

        if self.fpp_module:
            info['fpp_parameters'] = sum(p.numel() for p in self.fpp_module.parameters() if p.requires_grad)

        if self.tpp_module:
            info['tpp_parameters'] = sum(p.numel() for p in self.tpp_module.parameters() if p.requires_grad)

        return info


# ============================================================================
# 工厂函数和配置验证
# ============================================================================

def validate_enhancement_config(config: Dict[str, Any]) -> bool:
    """验证增强模块配置"""
    # 检查基本结构
    if not isinstance(config, dict):
        return False

    # 验证FPP配置
    if 'fpp' in config:
        if not validate_fpp_config(config['fpp']):
            return False

    # 验证TPP配置
    if 'tpp' in config:
        if not validate_tpp_config(config['tpp']):
            return False

    # 至少需要一个模块
    if 'fpp' not in config and 'tpp' not in config:
        return False

    return True


def create_enhancement_manager(config: Dict[str, Any],
                             modality: str = 'AUTO',
                             enabled: bool = True) -> EnhancementManager:
    """
    创建增强模块管理器

    Args:
        config: 增强模块配置
        modality: 数据模态
        enabled: 是否启用

    Returns:
        EnhancementManager: 增强模块管理器实例
    """
    if not validate_enhancement_config(config):
        raise ValueError("Invalid enhancement configuration")

    return EnhancementManager(config, modality, enabled)


# 测试代码
if __name__ == '__main__':
    print("Testing Enhancement Manager...")

    # 测试配置
    config = {
        'fpp': {
            'channels': 256,
            'fft_dim': 256,
            'freq_fusion_config': {
                'compressed_channels': 64,
                'use_high_pass': True,
                'use_low_pass': True
            },
            'enabled': True
        },
        'tpp': {
            'channels': 512,
            'cbam_config': {
                'ratio': 16,
                'kernel_size': 7
            },
            'loss_config': {
                'beta': 1.0,
                'alpha': 0.5,
                'gamma': 0.3
            },
            'enabled': True
        }
    }

    # 创建管理器
    manager = create_enhancement_manager(config, modality='AUTO', enabled=True)

    # 获取模块信息
    info = manager.get_module_info()
    print(f"Module info: {info}")

    # 测试不同阶段的前向传播
    batch_size = 2

    # s2阶段测试（应该激活FPP）
    x_s2 = torch.randn(batch_size, 256, 16, 16)
    output_s2 = manager(x_s2, stage='s2')
    print(f"s2 stage - Input: {x_s2.shape}, Output: {output_s2.shape}")

    # s3阶段测试（应该激活TPP）
    x_s3 = torch.randn(batch_size, 512, 8, 8)
    output_s3 = manager(x_s3, stage='s3')
    print(f"s3 stage - Input: {x_s3.shape}, Output: {output_s3.shape}")

    # 测试损失计算
    predictions = torch.randn(batch_size, 2)
    targets = torch.randint(0, 2, (batch_size,))

    enhanced_loss = manager.compute_enhanced_loss(predictions, targets)
    if enhanced_loss is not None:
        print(f"Enhanced loss: {enhanced_loss.item():.4f}")

    # 获取综合统计
    stats = manager.get_comprehensive_stats()
    print(f"Comprehensive stats: {stats}")

    print("✅ Enhancement Manager test completed successfully!")
