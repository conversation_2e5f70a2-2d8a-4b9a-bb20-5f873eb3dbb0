2025-07-31 16:23:34 - pulsar_trainer - INFO - <PERSON><PERSON> initialized. Log file: D:\pulsarSuanfa\outputs_FPP\logs\pulsar_trainer_20250731_162334.log
2025-07-31 16:23:34 - pulsar_trainer - INFO - 随机种子设置为: 42
2025-07-31 16:23:34 - pulsar_trainer - INFO - 使用配置文件参数创建模型
2025-07-31 16:23:34 - pulsar_trainer - INFO - 增强模块配置已加载: FPP=True, TPP=True
2025-07-31 16:23:34 - pulsar_trainer - INFO - 模型配置 - num_blocks: [2, 2, 6, 8, 2]
2025-07-31 16:23:34 - pulsar_trainer - INFO - 模型配置 - channels: [96, 128, 256, 512, 1024]
2025-07-31 16:23:34 - pulsar_trainer - INFO - 模型配置 - in_channels: 1
2025-07-31 16:23:34 - pulsar_trainer - INFO - ✓ 增强模块已启用
2025-07-31 16:23:34 - pulsar_trainer - INFO -   - FPP模块可用: True
2025-07-31 16:23:34 - pulsar_trainer - INFO -   - TPP模块可用: True
2025-07-31 16:23:34 - pulsar_trainer - INFO -   - 当前模态: AUTO
2025-07-31 16:23:34 - pulsar_trainer - INFO - 模型架构详情:
2025-07-31 16:23:34 - pulsar_trainer - INFO -   总参数数量: 38,773,938
2025-07-31 16:23:34 - pulsar_trainer - INFO -   可训练参数: 38,773,938
2025-07-31 16:23:34 - pulsar_trainer - INFO -   模型大小: 147.91 MB
2025-07-31 16:23:34 - pulsar_trainer - INFO -   块类型配置: ['C', 'C', 'T', 'T']
2025-07-31 16:23:34 - pulsar_trainer - INFO -   各阶段块数: [2, 2, 6, 8, 2]
2025-07-31 16:23:34 - pulsar_trainer - INFO -   各阶段通道数: [96, 128, 256, 512, 1024]
2025-07-31 16:23:34 - pulsar_trainer - INFO - Using standard cross entropy loss
2025-07-31 16:23:34 - pulsar_trainer - INFO - 🖥️ 使用GPU: NVIDIA GeForce RTX 4050 Laptop GPU
2025-07-31 16:23:34 - pulsar_trainer - INFO - 💾 GPU内存: 6.4GB
2025-07-31 16:23:34 - pulsar_trainer - INFO - 📋 模型参数总数: 38,773,938
2025-07-31 16:23:34 - pulsar_trainer - INFO - 🏗️ 模型架构: CoAtNet
2025-07-31 16:23:34 - pulsar_trainer - INFO - 📚 数据集大小:
2025-07-31 16:23:34 - pulsar_trainer - INFO -   - 训练集: 1674
2025-07-31 16:23:34 - pulsar_trainer - INFO -   - 验证集: 360
2025-07-31 16:23:34 - pulsar_trainer - INFO -   - 测试集: 358
2025-07-31 16:23:34 - pulsar_trainer - INFO -   - 总计: 2392
2025-07-31 16:23:34 - pulsar_trainer - INFO - ============================================================
2025-07-31 16:23:34 - pulsar_trainer - INFO - 🚀 开始脉冲星分类训练
2025-07-31 16:23:34 - pulsar_trainer - INFO - 📊 模态: FPP
2025-07-31 16:23:34 - pulsar_trainer - INFO - 🧠 模型: coatnet
2025-07-31 16:23:34 - pulsar_trainer - INFO - 📦 批次大小: 32
2025-07-31 16:23:34 - pulsar_trainer - INFO - 🎯 学习率: 0.001
2025-07-31 16:23:34 - pulsar_trainer - INFO - 🔄 训练轮数: 100
2025-07-31 16:23:34 - pulsar_trainer - INFO - ============================================================
2025-07-31 16:23:57 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.5863109827041626, 'fd_loss': 0.4374171197414398, 'vd_loss': 0.26353538036346436, 'confidence_penalty': 0.0, 'total_loss': 0.884080171585083}
2025-07-31 16:24:25 - pulsar_trainer - INFO - Epoch   1: Train Loss=3.2706, Val Loss=0.3328, Train Acc=77.96%, Val Acc=94.72%
2025-07-31 16:24:25 - pulsar_trainer - INFO - Validation F1: 94.7189, Precision: 94.8343, Recall: 94.7222
2025-07-31 16:24:26 - pulsar_trainer - INFO - 模型已保存: D:\pulsarSuanfa\outputs_FPP\models\best_model.pth
2025-07-31 16:24:26 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 1.2921292781829834, 'fd_loss': 0.1583700329065323, 'vd_loss': 0.06594395637512207, 'confidence_penalty': 0.004775381181389093, 'total_loss': 1.3958728313446045}
2025-07-31 16:24:31 - pulsar_trainer - INFO - Epoch   2: Train Loss=0.2978, Val Loss=0.2103, Train Acc=91.40%, Val Acc=95.56%
2025-07-31 16:24:31 - pulsar_trainer - INFO - Validation F1: 95.5550, Precision: 95.5781, Recall: 95.5556
2025-07-31 16:24:31 - pulsar_trainer - INFO - 模型已保存: D:\pulsarSuanfa\outputs_FPP\models\best_model.pth
2025-07-31 16:24:31 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.2739153206348419, 'fd_loss': 0.05192860960960388, 'vd_loss': 0.07288715243339539, 'confidence_penalty': 0.004811156075447798, 'total_loss': 0.326556921005249}
2025-07-31 16:24:36 - pulsar_trainer - INFO - Epoch   3: Train Loss=0.2194, Val Loss=0.2338, Train Acc=93.97%, Val Acc=92.22%
2025-07-31 16:24:36 - pulsar_trainer - INFO - Validation F1: 92.1749, Precision: 93.2692, Recall: 92.2222
2025-07-31 16:24:36 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.09757983684539795, 'fd_loss': 0.05196278914809227, 'vd_loss': 0.09709760546684265, 'confidence_penalty': 0.004283554386347532, 'total_loss': 0.15697406232357025}
2025-07-31 16:24:41 - pulsar_trainer - INFO - Epoch   4: Train Loss=0.1188, Val Loss=0.1107, Train Acc=96.48%, Val Acc=97.22%
2025-07-31 16:24:41 - pulsar_trainer - INFO - Validation F1: 97.2219, Precision: 97.2456, Recall: 97.2222
2025-07-31 16:24:41 - pulsar_trainer - INFO - 模型已保存: D:\pulsarSuanfa\outputs_FPP\models\best_model.pth
2025-07-31 16:24:41 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.23022863268852234, 'fd_loss': 0.06289524585008621, 'vd_loss': 0.11906477808952332, 'confidence_penalty': 0.004645524080842733, 'total_loss': 0.3020412027835846}
2025-07-31 16:24:46 - pulsar_trainer - INFO - Epoch   5: Train Loss=0.1294, Val Loss=0.1187, Train Acc=96.30%, Val Acc=96.94%
2025-07-31 16:24:46 - pulsar_trainer - INFO - Validation F1: 96.9439, Precision: 96.9807, Recall: 96.9444
2025-07-31 16:24:46 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.011462826281785965, 'fd_loss': 0.010455746203660965, 'vd_loss': 0.011361837387084961, 'confidence_penalty': 0.004815549589693546, 'total_loss': 0.024914801120758057}
2025-07-31 16:24:51 - pulsar_trainer - INFO - Epoch   6: Train Loss=0.1114, Val Loss=0.1100, Train Acc=96.00%, Val Acc=97.50%
2025-07-31 16:24:51 - pulsar_trainer - INFO - Validation F1: 97.4995, Precision: 97.5367, Recall: 97.5000
2025-07-31 16:24:51 - pulsar_trainer - INFO - 模型已保存: D:\pulsarSuanfa\outputs_FPP\models\best_model.pth
2025-07-31 16:24:51 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.09592165052890778, 'fd_loss': 0.06216546148061752, 'vd_loss': 0.12145859003067017, 'confidence_penalty': 0.0048377434723079205, 'total_loss': 0.16827969253063202}
2025-07-31 16:24:56 - pulsar_trainer - INFO - Epoch   7: Train Loss=0.0924, Val Loss=0.1236, Train Acc=97.55%, Val Acc=95.83%
2025-07-31 16:24:56 - pulsar_trainer - INFO - Validation F1: 95.8333, Precision: 95.8347, Recall: 95.8333
2025-07-31 16:24:56 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.028707632794976234, 'fd_loss': 0.026586707681417465, 'vd_loss': 0.046305298805236816, 'confidence_penalty': 0.004295920487493277, 'total_loss': 0.06018849462270737}
2025-07-31 16:25:01 - pulsar_trainer - INFO - Epoch   8: Train Loss=0.0935, Val Loss=0.1264, Train Acc=97.73%, Val Acc=95.56%
2025-07-31 16:25:01 - pulsar_trainer - INFO - Validation F1: 95.5521, Precision: 95.6966, Recall: 95.5556
2025-07-31 16:25:01 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.027930239215493202, 'fd_loss': 0.02200908772647381, 'vd_loss': 0.03781631588935852, 'confidence_penalty': 0.004712094087153673, 'total_loss': 0.054991770535707474}
2025-07-31 16:25:06 - pulsar_trainer - INFO - Epoch   9: Train Loss=0.0873, Val Loss=0.2129, Train Acc=97.25%, Val Acc=93.33%
2025-07-31 16:25:06 - pulsar_trainer - INFO - Validation F1: 93.3083, Precision: 93.9905, Recall: 93.3333
2025-07-31 16:25:06 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.0335865244269371, 'fd_loss': 0.029657673090696335, 'vd_loss': 0.05749353766441345, 'confidence_penalty': 0.004634107928723097, 'total_loss': 0.07029753178358078}
2025-07-31 16:25:11 - pulsar_trainer - INFO - Epoch  10: Train Loss=0.1295, Val Loss=0.2174, Train Acc=96.83%, Val Acc=95.28%
2025-07-31 16:25:11 - pulsar_trainer - INFO - Validation F1: 95.2734, Precision: 95.4475, Recall: 95.2778
2025-07-31 16:25:11 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.18448925018310547, 'fd_loss': 0.03197655454277992, 'vd_loss': 0.06294888257980347, 'confidence_penalty': 0.004909835290163755, 'total_loss': 0.22427202761173248}
2025-07-31 16:25:16 - pulsar_trainer - INFO - Epoch  11: Train Loss=0.1020, Val Loss=0.0741, Train Acc=97.43%, Val Acc=97.50%
2025-07-31 16:25:16 - pulsar_trainer - INFO - Validation F1: 97.5000, Precision: 97.5015, Recall: 97.5000
2025-07-31 16:25:16 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.0036840387620031834, 'fd_loss': 0.0036527991760522127, 'vd_loss': 0.004027664661407471, 'confidence_penalty': 0.0046347216702997684, 'total_loss': 0.01135345920920372}
2025-07-31 16:25:20 - pulsar_trainer - INFO - Epoch  12: Train Loss=0.0677, Val Loss=0.0849, Train Acc=97.97%, Val Acc=98.06%
2025-07-31 16:25:20 - pulsar_trainer - INFO - Validation F1: 98.0555, Precision: 98.0570, Recall: 98.0556
2025-07-31 16:25:21 - pulsar_trainer - INFO - 模型已保存: D:\pulsarSuanfa\outputs_FPP\models\best_model.pth
2025-07-31 16:25:21 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.02730068750679493, 'fd_loss': 0.01830834150314331, 'vd_loss': 0.03657662868499756, 'confidence_penalty': 0.004981653764843941, 'total_loss': 0.05240949988365173}
2025-07-31 16:25:26 - pulsar_trainer - INFO - Epoch  13: Train Loss=0.0619, Val Loss=0.0947, Train Acc=98.39%, Val Acc=97.22%
2025-07-31 16:25:26 - pulsar_trainer - INFO - Validation F1: 97.2215, Precision: 97.2747, Recall: 97.2222
2025-07-31 16:25:26 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.014779901131987572, 'fd_loss': 0.013322816230356693, 'vd_loss': 0.0022119879722595215, 'confidence_penalty': 0.004905829671770334, 'total_loss': 0.027010735124349594}
2025-07-31 16:25:31 - pulsar_trainer - INFO - Epoch  14: Train Loss=0.0540, Val Loss=0.1999, Train Acc=98.86%, Val Acc=96.39%
2025-07-31 16:25:31 - pulsar_trainer - INFO - Validation F1: 96.3855, Precision: 96.5628, Recall: 96.3889
2025-07-31 16:25:31 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.004437243100255728, 'fd_loss': 0.004151482135057449, 'vd_loss': 0.008264899253845215, 'confidence_penalty': 0.004988237749785185, 'total_loss': 0.013980692252516747}
2025-07-31 16:25:36 - pulsar_trainer - INFO - Epoch  15: Train Loss=0.0831, Val Loss=0.0918, Train Acc=98.09%, Val Acc=97.50%
2025-07-31 16:25:36 - pulsar_trainer - INFO - Validation F1: 97.4991, Precision: 97.5719, Recall: 97.5000
2025-07-31 16:25:36 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.00566721148788929, 'fd_loss': 0.005574315786361694, 'vd_loss': 0.004387378692626953, 'confidence_penalty': 0.004615632817149162, 'total_loss': 0.014386216178536415}
2025-07-31 16:25:40 - pulsar_trainer - INFO - Epoch  16: Train Loss=0.0877, Val Loss=0.0724, Train Acc=97.55%, Val Acc=97.22%
2025-07-31 16:25:40 - pulsar_trainer - INFO - Validation F1: 97.2221, Precision: 97.2281, Recall: 97.2222
2025-07-31 16:25:40 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.08599769324064255, 'fd_loss': 0.032234080135822296, 'vd_loss': 0.062353670597076416, 'confidence_penalty': 0.004670343827456236, 'total_loss': 0.1254911720752716}
2025-07-31 16:25:45 - pulsar_trainer - INFO - Epoch  17: Train Loss=0.0615, Val Loss=0.0673, Train Acc=98.45%, Val Acc=97.22%
2025-07-31 16:25:45 - pulsar_trainer - INFO - Validation F1: 97.2215, Precision: 97.2747, Recall: 97.2222
2025-07-31 16:25:45 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.10139693319797516, 'fd_loss': 0.04184377193450928, 'vd_loss': 0.07565975189208984, 'confidence_penalty': 0.004540863446891308, 'total_loss': 0.14955760538578033}
2025-07-31 16:25:50 - pulsar_trainer - INFO - Epoch  18: Train Loss=0.0527, Val Loss=0.1049, Train Acc=99.10%, Val Acc=98.06%
2025-07-31 16:25:50 - pulsar_trainer - INFO - Validation F1: 98.0554, Precision: 98.0689, Recall: 98.0556
2025-07-31 16:25:50 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.21894975006580353, 'fd_loss': 0.03412291407585144, 'vd_loss': 0.0681157112121582, 'confidence_penalty': 0.004884568974375725, 'total_loss': 0.2613304853439331}
2025-07-31 16:25:55 - pulsar_trainer - INFO - Epoch  19: Train Loss=0.0630, Val Loss=0.0683, Train Acc=98.51%, Val Acc=98.33%
2025-07-31 16:25:55 - pulsar_trainer - INFO - Validation F1: 98.3333, Precision: 98.3333, Recall: 98.3333
2025-07-31 16:25:55 - pulsar_trainer - INFO - 模型已保存: D:\pulsarSuanfa\outputs_FPP\models\best_model.pth
2025-07-31 16:25:56 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.004566805437207222, 'fd_loss': 0.0045051975175738335, 'vd_loss': 0.0035364627838134766, 'confidence_penalty': 0.004702901002019644, 'total_loss': 0.01258324459195137}
2025-07-31 16:26:00 - pulsar_trainer - INFO - Epoch  20: Train Loss=0.9135, Val Loss=0.9566, Train Acc=92.41%, Val Acc=93.33%
2025-07-31 16:26:00 - pulsar_trainer - INFO - Validation F1: 93.3300, Precision: 93.4191, Recall: 93.3333
2025-07-31 16:26:00 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 3.537543535232544, 'fd_loss': 0.32200518250465393, 'vd_loss': 0.46264007687568665, 'confidence_penalty': 0.004863307811319828, 'total_loss': 3.8422014713287354}
2025-07-31 16:26:05 - pulsar_trainer - INFO - Epoch  21: Train Loss=63.2411, Val Loss=58.4189, Train Acc=67.74%, Val Acc=80.83%
2025-07-31 16:26:05 - pulsar_trainer - INFO - Validation F1: 80.2278, Precision: 85.1377, Recall: 80.8333
2025-07-31 16:26:05 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 108.27616119384766, 'fd_loss': 0.21875, 'vd_loss': 0.4375, 'confidence_penalty': 0.005000001285225153, 'total_loss': 108.52178192138672}
2025-07-31 16:26:10 - pulsar_trainer - INFO - Epoch  22: Train Loss=9.3693, Val Loss=4.7859, Train Acc=83.57%, Val Acc=84.72%
2025-07-31 16:26:10 - pulsar_trainer - INFO - Validation F1: 84.4338, Precision: 87.5013, Recall: 84.7222
2025-07-31 16:26:10 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.9044761061668396, 'fd_loss': 0.05479489266872406, 'vd_loss': 0.015785932540893555, 'confidence_penalty': 0.004990306217223406, 'total_loss': 0.9415996074676514}
2025-07-31 16:26:15 - pulsar_trainer - INFO - Epoch  23: Train Loss=2.5243, Val Loss=1.1344, Train Acc=85.30%, Val Acc=83.06%
2025-07-31 16:26:15 - pulsar_trainer - INFO - Validation F1: 82.6506, Precision: 86.4596, Recall: 83.0556
2025-07-31 16:26:15 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.6542171835899353, 'fd_loss': 0.17075136303901672, 'vd_loss': 0.24806123971939087, 'confidence_penalty': 0.004722186364233494, 'total_loss': 0.8187333941459656}
2025-07-31 16:26:20 - pulsar_trainer - INFO - Epoch  24: Train Loss=0.7005, Val Loss=0.9468, Train Acc=88.71%, Val Acc=85.00%
2025-07-31 16:26:20 - pulsar_trainer - INFO - Validation F1: 84.7285, Precision: 87.6794, Recall: 85.0000
2025-07-31 16:26:20 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.3296735882759094, 'fd_loss': 0.11333131790161133, 'vd_loss': 0.2266327142715454, 'confidence_penalty': 0.004754797089844942, 'total_loss': 0.4590838849544525}
2025-07-31 16:26:25 - pulsar_trainer - INFO - Epoch  25: Train Loss=0.4733, Val Loss=0.2788, Train Acc=90.44%, Val Acc=93.33%
2025-07-31 16:26:25 - pulsar_trainer - INFO - Validation F1: 93.3166, Precision: 93.7710, Recall: 93.3333
2025-07-31 16:26:25 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.02828999236226082, 'fd_loss': 0.023722108453512192, 'vd_loss': 0.03381660580635071, 'confidence_penalty': 0.004590214230120182, 'total_loss': 0.054886240512132645}
2025-07-31 16:26:29 - pulsar_trainer - INFO - Epoch  26: Train Loss=0.2216, Val Loss=0.1972, Train Acc=93.25%, Val Acc=96.11%
2025-07-31 16:26:29 - pulsar_trainer - INFO - Validation F1: 96.1110, Precision: 96.1168, Recall: 96.1111
2025-07-31 16:26:29 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.007138042710721493, 'fd_loss': 0.006780915893614292, 'vd_loss': 0.009775996208190918, 'confidence_penalty': 0.004753950051963329, 'total_loss': 0.018215250223875046}
2025-07-31 16:26:34 - pulsar_trainer - INFO - Epoch  27: Train Loss=0.1847, Val Loss=0.1858, Train Acc=94.38%, Val Acc=95.56%
2025-07-31 16:26:34 - pulsar_trainer - INFO - Validation F1: 95.5543, Precision: 95.6062, Recall: 95.5556
2025-07-31 16:26:34 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.03629641607403755, 'fd_loss': 0.03012395091354847, 'vd_loss': 0.020860910415649414, 'confidence_penalty': 0.004295201972126961, 'total_loss': 0.06191186606884003}
2025-07-31 16:26:39 - pulsar_trainer - INFO - Epoch  28: Train Loss=0.1932, Val Loss=0.2015, Train Acc=93.73%, Val Acc=95.00%
2025-07-31 16:26:39 - pulsar_trainer - INFO - Validation F1: 94.9944, Precision: 95.2009, Recall: 95.0000
2025-07-31 16:26:39 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.17054645717144012, 'fd_loss': 0.09622108936309814, 'vd_loss': 0.1882627010345459, 'confidence_penalty': 0.004676453303545713, 'total_loss': 0.2798122763633728}
2025-07-31 16:26:44 - pulsar_trainer - INFO - Epoch  29: Train Loss=0.1795, Val Loss=0.1848, Train Acc=95.10%, Val Acc=94.72%
2025-07-31 16:26:44 - pulsar_trainer - INFO - Validation F1: 94.7153, Precision: 94.9567, Recall: 94.7222
2025-07-31 16:26:44 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.09618960320949554, 'fd_loss': 0.06577548384666443, 'vd_loss': 0.012577593326568604, 'confidence_penalty': 0.0046911779791116714, 'total_loss': 0.13754180073738098}
2025-07-31 16:26:49 - pulsar_trainer - INFO - Epoch  30: Train Loss=0.1479, Val Loss=0.1517, Train Acc=95.40%, Val Acc=97.22%
2025-07-31 16:26:49 - pulsar_trainer - INFO - Validation F1: 97.2221, Precision: 97.2281, Recall: 97.2222
2025-07-31 16:26:49 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.1622707098722458, 'fd_loss': 0.08986594527959824, 'vd_loss': 0.06040260195732117, 'confidence_penalty': 0.004373972769826651, 'total_loss': 0.22969843447208405}
2025-07-31 16:26:54 - pulsar_trainer - INFO - Epoch  31: Train Loss=0.1562, Val Loss=0.1512, Train Acc=95.34%, Val Acc=97.78%
2025-07-31 16:26:54 - pulsar_trainer - INFO - Validation F1: 97.7777, Precision: 97.7837, Recall: 97.7778
2025-07-31 16:26:54 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.07571198046207428, 'fd_loss': 0.05955507606267929, 'vd_loss': 0.11900785565376282, 'confidence_penalty': 0.004851939622312784, 'total_loss': 0.1460438221693039}
2025-07-31 16:26:58 - pulsar_trainer - INFO - Epoch  32: Train Loss=0.1365, Val Loss=0.1746, Train Acc=96.24%, Val Acc=96.67%
2025-07-31 16:26:58 - pulsar_trainer - INFO - Validation F1: 96.6657, Precision: 96.7186, Recall: 96.6667
2025-07-31 16:26:58 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.007316481322050095, 'fd_loss': 0.007066822610795498, 'vd_loss': 0.009664148092269897, 'confidence_penalty': 0.004629737231880426, 'total_loss': 0.01837887428700924}
2025-07-31 16:27:03 - pulsar_trainer - INFO - Epoch  33: Train Loss=0.1424, Val Loss=0.1395, Train Acc=96.48%, Val Acc=98.06%
2025-07-31 16:27:03 - pulsar_trainer - INFO - Validation F1: 98.0555, Precision: 98.0570, Recall: 98.0556
2025-07-31 16:27:03 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.42902687191963196, 'fd_loss': 0.13587504625320435, 'vd_loss': 0.1381133794784546, 'confidence_penalty': 0.004398707300424576, 'total_loss': 0.5427970886230469}
2025-07-31 16:27:08 - pulsar_trainer - INFO - Epoch  34: Train Loss=0.1478, Val Loss=0.1886, Train Acc=95.94%, Val Acc=96.11%
2025-07-31 16:27:08 - pulsar_trainer - INFO - Validation F1: 96.1092, Precision: 96.2024, Recall: 96.1111
2025-07-31 16:27:08 - pulsar_trainer - INFO - 早停触发，在第 34 轮停止训练
2025-07-31 16:27:08 - pulsar_trainer - INFO - ============================================================
2025-07-31 16:27:08 - pulsar_trainer - INFO - ✅ 训练完成
2025-07-31 16:27:08 - pulsar_trainer - INFO - 🏆 最佳验证准确率: 98.3333
2025-07-31 16:27:08 - pulsar_trainer - INFO - ⏱️ 总训练时间: 213.64秒
2025-07-31 16:27:08 - pulsar_trainer - INFO - ============================================================
2025-07-31 16:27:08 - pulsar_trainer - INFO - 🔍 开始最终评估...
2025-07-31 16:27:08 - pulsar_trainer - INFO - 模型已加载: D:\pulsarSuanfa\outputs_FPP\models\best_model.pth
2025-07-31 16:27:31 - pulsar_trainer.utils.comprehensive_misclassification_analysis - INFO - 开始全面误分类分析...
2025-07-31 16:27:31 - pulsar_trainer.utils.comprehensive_misclassification_analysis - INFO - 综合误分类分析报告已生成: D:\pulsarSuanfa\outputs_FPP\results\comprehensive_misclassification_analysis\comprehensive_misclassification_report.txt
2025-07-31 16:27:31 - pulsar_trainer.utils.comprehensive_misclassification_analysis - INFO - 全面误分类分析完成。报告保存至: D:\pulsarSuanfa\outputs_FPP\results\comprehensive_misclassification_analysis
2025-07-31 16:27:31 - pulsar_trainer - INFO - 📊 评估结果:
2025-07-31 16:27:31 - pulsar_trainer - INFO -   - accuracy: 0.9860
2025-07-31 16:27:31 - pulsar_trainer - INFO -   - precision: 0.9833
2025-07-31 16:27:31 - pulsar_trainer - INFO -   - recall: 0.9888
2025-07-31 16:27:31 - pulsar_trainer - INFO -   - specificity: 0.9832
2025-07-31 16:27:31 - pulsar_trainer - INFO -   - f1_score: 0.9861
2025-07-31 16:27:31 - pulsar_trainer - INFO -   - false_positive_rate: 0.0168
2025-07-31 16:27:31 - pulsar_trainer - INFO - 📊 增强模块统计信息:
2025-07-31 16:27:31 - pulsar_trainer - INFO -   - 总前向传播次数: 3741
2025-07-31 16:27:31 - pulsar_trainer - INFO -   - FPP激活次数: 1247
2025-07-31 16:27:31 - pulsar_trainer - INFO -   - TPP激活次数: 1247
2025-07-31 16:27:31 - pulsar_trainer - INFO -   - 损失计算次数: 1007
2025-07-31 16:27:31 - pulsar_trainer - INFO -   - FPP激活率: 33.33%
2025-07-31 16:27:31 - pulsar_trainer - INFO -   - TPP激活率: 33.33%
2025-07-31 16:27:33 - pulsar_trainer - INFO - 所有结果已保存到: D:\pulsarSuanfa\outputs_FPP\results
2025-07-31 16:27:33 - pulsar_trainer - INFO - 可视化图表已保存到: D:\pulsarSuanfa\outputs_FPP\plots
