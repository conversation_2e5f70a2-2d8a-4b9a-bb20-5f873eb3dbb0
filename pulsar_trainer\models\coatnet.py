"""
Enhanced CoAtNet implementation for pulsar classification.
Based on the paper: "CoAtNet: Marrying Convolution and Attention for All Data Sizes"

Enhanced with FPP and TPP enhancement modules for improved pulsar classification performance.
Version: 2.0.0 (Enhanced)
Date: 2025-01-31
"""

import torch
import torch.nn as nn

from einops import rearrange
from einops.layers.torch import Rearrange

# 导入增强模块管理器
try:
    from .enhancement_manager import EnhancementManager
except ImportError:
    # 用于测试时的绝对导入
    from enhancement_manager import EnhancementManager


def conv_3x3_bn(inp, oup, image_size, downsample=False):
    stride = 1 if downsample == False else 2
    return nn.Sequential(
        nn.Conv2d(inp, oup, 3, stride, 1, bias=False),
        nn.BatchNorm2d(oup),
        nn.GELU()
    )


class PreNorm(nn.Module):
    def __init__(self, dim, fn, norm):
        super().__init__()
        self.norm = norm(dim)
        self.fn = fn

    def forward(self, x, **kwargs):
        return self.fn(self.norm(x), **kwargs)


class SE(nn.Module):
    def __init__(self, inp, oup, expansion=0.25):
        super().__init__()
        self.avg_pool = nn.AdaptiveAvgPool2d(1)
        self.fc = nn.Sequential(
            nn.Linear(oup, int(inp * expansion), bias=False),
            nn.GELU(),
            nn.Linear(int(inp * expansion), oup, bias=False),
            nn.Sigmoid()
        )

    def forward(self, x):
        b, c, _, _ = x.size()
        y = self.avg_pool(x).view(b, c)
        y = self.fc(y).view(b, c, 1, 1)
        return x * y


class FeedForward(nn.Module):
    def __init__(self, dim, hidden_dim, dropout=0.):
        super().__init__()
        self.net = nn.Sequential(
            nn.Linear(dim, hidden_dim),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim, dim),
            nn.Dropout(dropout)
        )

    def forward(self, x):
        return self.net(x)


class MBConv(nn.Module):
    def __init__(self, inp, oup, image_size, downsample=False, expansion=4):
        super().__init__()
        self.downsample = downsample
        stride = 1 if self.downsample == False else 2
        hidden_dim = int(inp * expansion)

        if self.downsample:
            self.pool = nn.MaxPool2d(3, 2, 1)
            self.proj = nn.Conv2d(inp, oup, 1, 1, 0, bias=False)

        if expansion == 1:
            self.conv = nn.Sequential(
                # dw
                nn.Conv2d(hidden_dim, hidden_dim, 3, stride,
                          1, groups=hidden_dim, bias=False),
                nn.BatchNorm2d(hidden_dim),
                nn.GELU(),
                # pw-linear
                nn.Conv2d(hidden_dim, oup, 1, 1, 0, bias=False),
                nn.BatchNorm2d(oup),
            )
        else:
            self.conv = nn.Sequential(
                # pw
                # down-sample in the first conv
                nn.Conv2d(inp, hidden_dim, 1, stride, 0, bias=False),
                nn.BatchNorm2d(hidden_dim),
                nn.GELU(),
                # dw
                nn.Conv2d(hidden_dim, hidden_dim, 3, 1, 1,
                          groups=hidden_dim, bias=False),
                nn.BatchNorm2d(hidden_dim),
                nn.GELU(),
                SE(inp, hidden_dim),
                # pw-linear
                nn.Conv2d(hidden_dim, oup, 1, 1, 0, bias=False),
                nn.BatchNorm2d(oup),
            )

        self.conv = PreNorm(inp, self.conv, nn.BatchNorm2d)

    def forward(self, x):
        if self.downsample:
            return self.proj(self.pool(x)) + self.conv(x)
        else:
            return x + self.conv(x)


class Attention(nn.Module):
    def __init__(self, inp, oup, image_size, heads=8, dim_head=32, dropout=0.):
        super().__init__()
        inner_dim = dim_head * heads
        project_out = not (heads == 1 and dim_head == inp)

        self.ih, self.iw = image_size

        self.heads = heads
        self.scale = dim_head ** -0.5

        # parameter table of relative position bias
        self.relative_bias_table = nn.Parameter(
            torch.zeros((2 * self.ih - 1) * (2 * self.iw - 1), heads))

        coords = torch.meshgrid((torch.arange(self.ih), torch.arange(self.iw)))
        coords = torch.flatten(torch.stack(coords), 1)
        relative_coords = coords[:, :, None] - coords[:, None, :]

        relative_coords[0] += self.ih - 1
        relative_coords[1] += self.iw - 1
        relative_coords[0] *= 2 * self.iw - 1
        relative_coords = rearrange(relative_coords, 'c h w -> h w c')
        relative_index = relative_coords.sum(-1).flatten().unsqueeze(1)
        self.register_buffer("relative_index", relative_index)

        self.attend = nn.Softmax(dim=-1)
        self.to_qkv = nn.Linear(inp, inner_dim * 3, bias=False)

        self.to_out = nn.Sequential(
            nn.Linear(inner_dim, oup),
            nn.Dropout(dropout)
        ) if project_out else nn.Identity()

    def forward(self, x):
        qkv = self.to_qkv(x).chunk(3, dim=-1)
        q, k, v = map(lambda t: rearrange(
            t, 'b n (h d) -> b h n d', h=self.heads), qkv)

        dots = torch.matmul(q, k.transpose(-1, -2)) * self.scale

        # Use "gather" for more efficiency on GPUs
        relative_bias = self.relative_bias_table.gather(
            0, self.relative_index.repeat(1, self.heads))
        relative_bias = rearrange(
            relative_bias, '(h w) c -> 1 c h w', h=self.ih*self.iw, w=self.ih*self.iw)
        dots = dots + relative_bias

        attn = self.attend(dots)
        out = torch.matmul(attn, v)
        out = rearrange(out, 'b h n d -> b n (h d)')
        out = self.to_out(out)
        return out


class Transformer(nn.Module):
    def __init__(self, inp, oup, image_size, heads=8, dim_head=32, downsample=False, dropout=0.):
        super().__init__()
        hidden_dim = int(inp * 4)

        self.ih, self.iw = image_size
        self.downsample = downsample

        if self.downsample:
            self.pool1 = nn.MaxPool2d(3, 2, 1)
            self.pool2 = nn.MaxPool2d(3, 2, 1)
            self.proj = nn.Conv2d(inp, oup, 1, 1, 0, bias=False)

        self.attn = Attention(inp, oup, image_size, heads, dim_head, dropout)
        self.ff = FeedForward(oup, hidden_dim, dropout)

        self.attn = nn.Sequential(
            Rearrange('b c ih iw -> b (ih iw) c'),
            PreNorm(inp, self.attn, nn.LayerNorm),
            Rearrange('b (ih iw) c -> b c ih iw', ih=self.ih, iw=self.iw)
        )

        self.ff = nn.Sequential(
            Rearrange('b c ih iw -> b (ih iw) c'),
            PreNorm(oup, self.ff, nn.LayerNorm),
            Rearrange('b (ih iw) c -> b c ih iw', ih=self.ih, iw=self.iw)
        )

    def forward(self, x):
        if self.downsample:
            x = self.proj(self.pool1(x)) + self.attn(self.pool2(x))
        else:
            x = x + self.attn(x)
        x = x + self.ff(x)
        return x


class CoAtNet(nn.Module):
    """
    Enhanced CoAtNet with integrated FPP and TPP enhancement modules

    Enhancements:
    - FPP Enhancement: FFT-based frequency domain processing + FreqFusion (s2 stage)
    - TPP Enhancement: CBAM attention mechanism + CF_Loss (s3, s4 stages)
    - Modality-aware conditional activation
    - Performance monitoring and statistics
    """

    def __init__(self, image_size, in_channels, num_blocks, channels, num_classes=1000,
                 block_types=['C', 'C', 'T', 'T'], enhancement_config=None):
        super().__init__()
        ih, iw = image_size
        block = {'C': MBConv, 'T': Transformer}

        # 原有的网络层
        self.s0 = self._make_layer(
            conv_3x3_bn, in_channels, channels[0], num_blocks[0], (ih // 2, iw // 2))
        self.s1 = self._make_layer(
            block[block_types[0]], channels[0], channels[1], num_blocks[1], (ih // 4, iw // 4))
        self.s2 = self._make_layer(
            block[block_types[1]], channels[1], channels[2], num_blocks[2], (ih // 8, iw // 8))
        self.s3 = self._make_layer(
            block[block_types[2]], channels[2], channels[3], num_blocks[3], (ih // 16, iw // 16))
        self.s4 = self._make_layer(
            block[block_types[3]], channels[3], channels[4], num_blocks[4], (ih // 32, iw // 32))

        self.pool = nn.AvgPool2d(ih // 32, 1)
        self.fc = nn.Linear(channels[-1], num_classes, bias=False)

        # 增强模块管理器
        if enhancement_config:
            # 自动配置增强模块的通道数
            # FPP增强在s2阶段前，所以使用s1阶段的输出通道数（即s2的输入通道数）
            if 'fpp' in enhancement_config and 'channels' not in enhancement_config['fpp']:
                enhancement_config['fpp']['channels'] = channels[1]  # s1阶段的输出通道数
                enhancement_config['fpp']['fft_dim'] = channels[1]   # FFT维度匹配
            # TPP增强在s3阶段前，使用s2阶段的输出通道数（即s3的输入通道数）
            if 'tpp' in enhancement_config and 'channels' not in enhancement_config['tpp']:
                enhancement_config['tpp']['channels'] = channels[2]  # s2阶段的输出通道数

            print(f"Debug: Auto-configured enhancement channels - FPP: {enhancement_config.get('fpp', {}).get('channels')}, TPP: {enhancement_config.get('tpp', {}).get('channels')}")

            self.enhancement_manager = EnhancementManager(enhancement_config)
            print(f"✓ CoAtNet enhanced with {self.enhancement_manager.get_module_info()}")
        else:
            self.enhancement_manager = None
            print("✓ CoAtNet initialized without enhancement modules")

    def forward(self, x):
        """
        Enhanced forward pass with integrated enhancement modules

        Enhancement integration points:
        - After s1, before s2: FPP enhancement (frequency domain processing)
        - After s2, before s3: TPP enhancement (attention mechanism)
        - After s3, before s4: TPP enhancement (attention mechanism)
        """
        x = self.s0(x)
        x = self.s1(x)

        # FPP增强模块集成点（s2阶段前）
        if self.enhancement_manager:
            x = self.enhancement_manager.forward(x, stage='s2')
        x = self.s2(x)

        # TPP增强模块集成点（s3阶段前）
        if self.enhancement_manager:
            x = self.enhancement_manager.forward(x, stage='s3')
        x = self.s3(x)

        # TPP增强模块集成点（s4阶段前）
        if self.enhancement_manager:
            x = self.enhancement_manager.forward(x, stage='s4')
        x = self.s4(x)

        x = self.pool(x).view(-1, x.shape[1])
        x = self.fc(x)
        return x

    def compute_enhanced_loss(self, predictions, targets):
        """
        计算增强损失函数

        Args:
            predictions: 模型预测logits [B, num_classes]
            targets: 真实标签 [B] 或 [B, 1]

        Returns:
            enhanced_loss: 增强损失值（如果TPP模块可用）或None
        """
        if self.enhancement_manager:
            enhanced_loss = self.enhancement_manager.compute_enhanced_loss(predictions, targets)
            if enhanced_loss is not None:
                return enhanced_loss
        return None

    def get_loss_components(self, predictions, targets):
        """
        获取损失组件详情（用于调试和监控）

        Args:
            predictions: 模型预测logits [B, num_classes]
            targets: 真实标签 [B] 或 [B, 1]

        Returns:
            loss_components: 损失组件字典或None
        """
        if self.enhancement_manager:
            return self.enhancement_manager.get_loss_components(predictions, targets)
        return None

    def get_enhancement_stats(self):
        """
        获取增强模块统计信息

        Returns:
            stats: 增强模块统计信息字典或None
        """
        if self.enhancement_manager:
            return self.enhancement_manager.get_comprehensive_stats()
        return None

    def set_enhancement_modality(self, modality):
        """
        设置增强模块的数据模态

        Args:
            modality: 数据模态 ('FPP', 'TPP', 'AUTO')
        """
        if self.enhancement_manager:
            self.enhancement_manager.set_modality(modality)

    def enable_enhancements(self):
        """启用所有增强模块"""
        if self.enhancement_manager:
            self.enhancement_manager.enable()

    def disable_enhancements(self):
        """禁用所有增强模块"""
        if self.enhancement_manager:
            self.enhancement_manager.disable()

    def reset_enhancement_stats(self):
        """重置增强模块统计信息"""
        if self.enhancement_manager:
            self.enhancement_manager.reset_stats()

    def _make_layer(self, block, inp, oup, depth, image_size):
        layers = nn.ModuleList([])
        for i in range(depth):
            if i == 0:
                layers.append(block(inp, oup, image_size, downsample=True))
            else:
                layers.append(block(oup, oup, image_size))
        return nn.Sequential(*layers)


def coatnet_0():
    num_blocks = [2, 2, 3, 5, 2]            # L
    channels = [64, 96, 192, 384, 768]      # D
    return CoAtNet((224, 224), 3, num_blocks, channels, num_classes=1000)


def coatnet_1():
    num_blocks = [2, 2, 6, 14, 2]           # L
    channels = [64, 96, 192, 384, 768]      # D
    return CoAtNet((224, 224), 3, num_blocks, channels, num_classes=1000)


def coatnet_2():
    num_blocks = [2, 2, 6, 14, 2]           # L
    channels = [128, 128, 256, 512, 1026]   # D
    return CoAtNet((224, 224), 3, num_blocks, channels, num_classes=1000)


def coatnet_3():
    num_blocks = [2, 2, 6, 14, 2]           # L
    channels = [192, 192, 384, 768, 1536]   # D
    return CoAtNet((224, 224), 3, num_blocks, channels, num_classes=1000)


def coatnet_4():
    num_blocks = [2, 2, 12, 28, 2]          # L
    channels = [192, 192, 384, 768, 1536]   # D
    return CoAtNet((224, 224), 3, num_blocks, channels, num_classes=1000)








def create_coatnet_from_config(model_config, enhancement_config=None) -> 'CoAtNet':
    """
    从配置创建增强版CoAtNet模型（唯一的模型创建方式）

    Args:
        model_config: 模型配置对象
        enhancement_config: 增强模块配置字典（可选）

    Returns:
        配置的增强版CoAtNet模型实例
    """
    # 验证配置
    model_config.validate()

    # 创建增强版模型
    model = CoAtNet(
        image_size=tuple(model_config.image_size),
        in_channels=model_config.in_channels,  # 统一为1
        num_blocks=model_config.num_blocks,
        channels=model_config.channels,
        num_classes=model_config.num_classes,
        block_types=model_config.block_types,
        enhancement_config=enhancement_config  # 新增：增强模块配置
    )

    return model


def count_parameters(model):
    return sum(p.numel() for p in model.parameters() if p.requires_grad)


if __name__ == '__main__':
    img = torch.randn(1, 3, 224, 224)

    net = coatnet_0()
    out = net(img)
    print(out.shape, count_parameters(net))

    net = coatnet_1()
    out = net(img)
    print(out.shape, count_parameters(net))

    net = coatnet_2()
    out = net(img)
    print(out.shape, count_parameters(net))

    net = coatnet_3()
    out = net(img)
    print(out.shape, count_parameters(net))

    net = coatnet_4()
    out = net(img)
    print(out.shape, count_parameters(net))
