"""
Complete Test Suite Runner
运行所有增强模块的测试，生成完整的测试报告
"""

import sys
import os
import time
import traceback
from typing import Dict, List, Tuple

# 添加路径
sys.path.append('tests/models')
sys.path.append('tests/integration')
sys.path.append('tests/performance')
sys.path.append('tests/compatibility')

# 导入所有测试模块
try:
    from test_fpp_enhancement import run_fpp_tests
    from test_tpp_enhancement import run_tpp_tests
    from test_enhanced_coatnet_integration import run_integration_tests
    from test_performance_regression import run_performance_regression_tests
    from test_backward_compatibility import run_compatibility_tests
except ImportError as e:
    print(f"Warning: Could not import some test modules: {e}")
    print("Some tests may be skipped.")


class TestSuiteRunner:
    """测试套件运行器"""
    
    def __init__(self):
        self.results = {}
        self.start_time = None
        self.end_time = None
    
    def run_test_suite(self, test_name: str, test_function) -> bool:
        """运行单个测试套件"""
        print(f"\n{'='*80}")
        print(f"Running {test_name}")
        print(f"{'='*80}")
        
        start_time = time.time()
        
        try:
            success = test_function()
            end_time = time.time()
            duration = end_time - start_time
            
            self.results[test_name] = {
                'success': success,
                'duration': duration,
                'error': None
            }
            
            status = "✅ PASSED" if success else "❌ FAILED"
            print(f"\n{test_name}: {status} (Duration: {duration:.2f}s)")
            
            return success
            
        except Exception as e:
            end_time = time.time()
            duration = end_time - start_time
            
            self.results[test_name] = {
                'success': False,
                'duration': duration,
                'error': str(e)
            }
            
            print(f"\n{test_name}: ❌ FAILED with exception (Duration: {duration:.2f}s)")
            print(f"Error: {e}")
            print(f"Traceback: {traceback.format_exc()}")
            
            return False
    
    def run_all_tests(self) -> bool:
        """运行所有测试"""
        self.start_time = time.time()
        
        print("🚀 Starting Complete Test Suite for Enhanced CoAtNet")
        print(f"Start time: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 定义测试套件
        test_suites = [
            ("FPP Enhancement Module Tests", run_fpp_tests),
            ("TPP Enhancement Module Tests", run_tpp_tests),
            ("Integration Tests", run_integration_tests),
            ("Performance Regression Tests", run_performance_regression_tests),
            ("Backward Compatibility Tests", run_compatibility_tests)
        ]
        
        # 运行所有测试
        all_passed = True
        
        for test_name, test_function in test_suites:
            try:
                success = self.run_test_suite(test_name, test_function)
                if not success:
                    all_passed = False
            except Exception as e:
                print(f"Failed to run {test_name}: {e}")
                all_passed = False
        
        self.end_time = time.time()
        
        # 生成测试报告
        self.generate_report()
        
        return all_passed
    
    def generate_report(self):
        """生成测试报告"""
        total_duration = self.end_time - self.start_time
        
        print(f"\n{'='*80}")
        print("📊 COMPLETE TEST SUITE REPORT")
        print(f"{'='*80}")
        
        print(f"Total execution time: {total_duration:.2f} seconds")
        print(f"End time: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 统计结果
        total_tests = len(self.results)
        passed_tests = sum(1 for result in self.results.values() if result['success'])
        failed_tests = total_tests - passed_tests
        
        print(f"\n📈 SUMMARY:")
        print(f"  Total test suites: {total_tests}")
        print(f"  Passed: {passed_tests}")
        print(f"  Failed: {failed_tests}")
        print(f"  Success rate: {(passed_tests/total_tests*100):.1f}%")
        
        # 详细结果
        print(f"\n📋 DETAILED RESULTS:")
        for test_name, result in self.results.items():
            status = "✅ PASS" if result['success'] else "❌ FAIL"
            duration = result['duration']
            print(f"  {test_name}: {status} ({duration:.2f}s)")
            
            if result['error']:
                print(f"    Error: {result['error']}")
        
        # 性能分析
        print(f"\n⏱️  PERFORMANCE ANALYSIS:")
        sorted_results = sorted(
            self.results.items(), 
            key=lambda x: x[1]['duration'], 
            reverse=True
        )
        
        for test_name, result in sorted_results:
            duration = result['duration']
            percentage = (duration / total_duration) * 100
            print(f"  {test_name}: {duration:.2f}s ({percentage:.1f}%)")
        
        # 最终状态
        if failed_tests == 0:
            print(f"\n🎉 ALL TESTS PASSED!")
            print("✅ Enhanced CoAtNet is ready for deployment!")
            print("\n📋 Next Steps:")
            print("  1. Review performance improvements")
            print("  2. Update documentation")
            print("  3. Deploy to production environment")
            print("  4. Monitor performance metrics")
        else:
            print(f"\n⚠️  {failed_tests} TEST SUITE(S) FAILED!")
            print("❌ Please fix the issues before deployment.")
            print("\n📋 Action Items:")
            
            for test_name, result in self.results.items():
                if not result['success']:
                    print(f"  - Fix issues in: {test_name}")
                    if result['error']:
                        print(f"    Error: {result['error']}")
        
        # 保存报告到文件
        self.save_report_to_file()
    
    def save_report_to_file(self):
        """保存报告到文件"""
        try:
            report_filename = f"test_report_{int(time.time())}.txt"
            
            with open(report_filename, 'w', encoding='utf-8') as f:
                f.write("Enhanced CoAtNet Test Suite Report\n")
                f.write("="*50 + "\n\n")
                
                f.write(f"Execution time: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(self.start_time))}\n")
                f.write(f"Total duration: {self.end_time - self.start_time:.2f} seconds\n\n")
                
                # 写入结果
                for test_name, result in self.results.items():
                    status = "PASS" if result['success'] else "FAIL"
                    f.write(f"{test_name}: {status} ({result['duration']:.2f}s)\n")
                    
                    if result['error']:
                        f.write(f"  Error: {result['error']}\n")
                
                # 统计信息
                total_tests = len(self.results)
                passed_tests = sum(1 for result in self.results.values() if result['success'])
                
                f.write(f"\nSummary: {passed_tests}/{total_tests} test suites passed\n")
            
            print(f"\n📄 Test report saved to: {report_filename}")
            
        except Exception as e:
            print(f"Warning: Could not save report to file: {e}")


def main():
    """主函数"""
    print("Enhanced CoAtNet Test Suite")
    print("Version: 1.0.0")
    print("Date: 2025-01-31")
    
    # 检查Python版本
    if sys.version_info < (3, 7):
        print("Error: Python 3.7+ is required")
        sys.exit(1)
    
    # 检查PyTorch
    try:
        import torch
        print(f"PyTorch version: {torch.__version__}")
    except ImportError:
        print("Error: PyTorch is required")
        sys.exit(1)
    
    # 运行测试
    runner = TestSuiteRunner()
    success = runner.run_all_tests()
    
    # 退出码
    sys.exit(0 if success else 1)


if __name__ == '__main__':
    main()
