2025-07-31 14:40:36 - pulsar_trainer - INFO - <PERSON><PERSON> initialized. Log file: D:\pulsarSuanfa\outputs_TPP\logs\pulsar_trainer_20250731_144036.log
2025-07-31 14:40:36 - pulsar_trainer - INFO - 随机种子设置为: 42
2025-07-31 14:40:36 - pulsar_trainer - INFO - 使用配置文件参数创建模型
2025-07-31 14:40:36 - pulsar_trainer - INFO - 模型配置 - num_blocks: [2, 2, 6, 8, 2]
2025-07-31 14:40:36 - pulsar_trainer - INFO - 模型配置 - channels: [96, 128, 256, 512, 1024]
2025-07-31 14:40:36 - pulsar_trainer - INFO - 模型配置 - in_channels: 1
2025-07-31 14:40:37 - pulsar_trainer - INFO - 模型架构详情:
2025-07-31 14:40:37 - pulsar_trainer - INFO -   总参数数量: 38,726,128
2025-07-31 14:40:37 - pulsar_trainer - INFO -   可训练参数: 38,726,128
2025-07-31 14:40:37 - pulsar_trainer - INFO -   模型大小: 147.73 MB
2025-07-31 14:40:37 - pulsar_trainer - INFO -   块类型配置: ['C', 'C', 'T', 'T']
2025-07-31 14:40:37 - pulsar_trainer - INFO -   各阶段块数: [2, 2, 6, 8, 2]
2025-07-31 14:40:37 - pulsar_trainer - INFO -   各阶段通道数: [96, 128, 256, 512, 1024]
2025-07-31 14:40:37 - pulsar_trainer - INFO - Using standard cross entropy loss
2025-07-31 14:40:37 - pulsar_trainer - INFO - 🖥️ 使用GPU: NVIDIA GeForce RTX 4050 Laptop GPU
2025-07-31 14:40:37 - pulsar_trainer - INFO - 💾 GPU内存: 6.4GB
2025-07-31 14:40:37 - pulsar_trainer - INFO - 📋 模型参数总数: 38,726,128
2025-07-31 14:40:37 - pulsar_trainer - INFO - 🏗️ 模型架构: CoAtNet
2025-07-31 14:40:37 - pulsar_trainer - INFO - 📚 数据集大小:
2025-07-31 14:40:37 - pulsar_trainer - INFO -   - 训练集: 1674
2025-07-31 14:40:37 - pulsar_trainer - INFO -   - 验证集: 360
2025-07-31 14:40:37 - pulsar_trainer - INFO -   - 测试集: 358
2025-07-31 14:40:37 - pulsar_trainer - INFO -   - 总计: 2392
2025-07-31 14:40:37 - pulsar_trainer - INFO - ============================================================
2025-07-31 14:40:37 - pulsar_trainer - INFO - 🚀 开始脉冲星分类训练
2025-07-31 14:40:37 - pulsar_trainer - INFO - 📊 模态: TPP
2025-07-31 14:40:37 - pulsar_trainer - INFO - 🧠 模型: coatnet
2025-07-31 14:40:37 - pulsar_trainer - INFO - 📦 批次大小: 32
2025-07-31 14:40:37 - pulsar_trainer - INFO - 🎯 学习率: 0.001
2025-07-31 14:40:37 - pulsar_trainer - INFO - 🔄 训练轮数: 100
2025-07-31 14:40:37 - pulsar_trainer - INFO - ============================================================
2025-07-31 14:41:25 - pulsar_trainer - INFO - Epoch   1: Train Loss=2.7294, Val Loss=0.6311, Train Acc=79.63%, Val Acc=81.39%
2025-07-31 14:41:25 - pulsar_trainer - INFO - Validation F1: 80.8009, Precision: 85.7708, Recall: 81.3889
2025-07-31 14:41:25 - pulsar_trainer - INFO - 模型已保存: D:\pulsarSuanfa\outputs_TPP\models\best_model.pth
2025-07-31 14:41:29 - pulsar_trainer - INFO - Epoch   2: Train Loss=0.4867, Val Loss=0.5211, Train Acc=88.05%, Val Acc=75.83%
2025-07-31 14:41:29 - pulsar_trainer - INFO - Validation F1: 74.3344, Precision: 83.7079, Recall: 75.8333
2025-07-31 14:41:34 - pulsar_trainer - INFO - Epoch   3: Train Loss=0.1375, Val Loss=0.1643, Train Acc=94.98%, Val Acc=97.50%
2025-07-31 14:41:34 - pulsar_trainer - INFO - Validation F1: 97.4991, Precision: 97.5719, Recall: 97.5000
2025-07-31 14:41:34 - pulsar_trainer - INFO - 模型已保存: D:\pulsarSuanfa\outputs_TPP\models\best_model.pth
2025-07-31 14:41:38 - pulsar_trainer - INFO - Epoch   4: Train Loss=0.1141, Val Loss=0.0750, Train Acc=96.18%, Val Acc=98.06%
2025-07-31 14:41:38 - pulsar_trainer - INFO - Validation F1: 98.0554, Precision: 98.0689, Recall: 98.0556
2025-07-31 14:41:39 - pulsar_trainer - INFO - 模型已保存: D:\pulsarSuanfa\outputs_TPP\models\best_model.pth
2025-07-31 14:41:43 - pulsar_trainer - INFO - Epoch   5: Train Loss=0.1071, Val Loss=0.0436, Train Acc=96.54%, Val Acc=98.89%
2025-07-31 14:41:43 - pulsar_trainer - INFO - Validation F1: 98.8889, Precision: 98.8949, Recall: 98.8889
2025-07-31 14:41:44 - pulsar_trainer - INFO - 模型已保存: D:\pulsarSuanfa\outputs_TPP\models\best_model.pth
2025-07-31 14:41:48 - pulsar_trainer - INFO - Epoch   6: Train Loss=0.0668, Val Loss=0.1124, Train Acc=97.73%, Val Acc=96.39%
2025-07-31 14:41:48 - pulsar_trainer - INFO - Validation F1: 96.3866, Precision: 96.5052, Recall: 96.3889
2025-07-31 14:41:52 - pulsar_trainer - INFO - Epoch   7: Train Loss=0.0628, Val Loss=0.0676, Train Acc=98.15%, Val Acc=97.22%
2025-07-31 14:41:52 - pulsar_trainer - INFO - Validation F1: 97.2208, Precision: 97.3157, Recall: 97.2222
2025-07-31 14:41:56 - pulsar_trainer - INFO - Epoch   8: Train Loss=0.0355, Val Loss=0.0473, Train Acc=98.98%, Val Acc=97.78%
2025-07-31 14:41:56 - pulsar_trainer - INFO - Validation F1: 97.7772, Precision: 97.8309, Recall: 97.7778
2025-07-31 14:42:01 - pulsar_trainer - INFO - Epoch   9: Train Loss=0.0287, Val Loss=0.0570, Train Acc=99.16%, Val Acc=98.61%
2025-07-31 14:42:01 - pulsar_trainer - INFO - Validation F1: 98.6108, Precision: 98.6486, Recall: 98.6111
2025-07-31 14:42:05 - pulsar_trainer - INFO - Epoch  10: Train Loss=0.0113, Val Loss=0.1241, Train Acc=99.52%, Val Acc=96.94%
2025-07-31 14:42:05 - pulsar_trainer - INFO - Validation F1: 96.9439, Precision: 96.9807, Recall: 96.9444
2025-07-31 14:42:09 - pulsar_trainer - INFO - Epoch  11: Train Loss=0.0181, Val Loss=0.1183, Train Acc=99.34%, Val Acc=96.11%
2025-07-31 14:42:09 - pulsar_trainer - INFO - Validation F1: 96.1111, Precision: 96.1111, Recall: 96.1111
2025-07-31 14:42:13 - pulsar_trainer - INFO - Epoch  12: Train Loss=0.0250, Val Loss=0.1864, Train Acc=99.28%, Val Acc=93.61%
2025-07-31 14:42:13 - pulsar_trainer - INFO - Validation F1: 93.5933, Precision: 94.1025, Recall: 93.6111
2025-07-31 14:42:18 - pulsar_trainer - INFO - Epoch  13: Train Loss=0.0414, Val Loss=0.4929, Train Acc=98.57%, Val Acc=95.00%
2025-07-31 14:42:18 - pulsar_trainer - INFO - Validation F1: 94.9875, Precision: 95.4545, Recall: 95.0000
2025-07-31 14:42:22 - pulsar_trainer - INFO - Epoch  14: Train Loss=0.0714, Val Loss=0.0721, Train Acc=98.09%, Val Acc=98.06%
2025-07-31 14:42:22 - pulsar_trainer - INFO - Validation F1: 98.0548, Precision: 98.1283, Recall: 98.0556
2025-07-31 14:42:26 - pulsar_trainer - INFO - Epoch  15: Train Loss=0.0287, Val Loss=0.1077, Train Acc=99.10%, Val Acc=95.28%
2025-07-31 14:42:26 - pulsar_trainer - INFO - Validation F1: 95.2696, Precision: 95.5944, Recall: 95.2778
2025-07-31 14:42:30 - pulsar_trainer - INFO - Epoch  16: Train Loss=0.0069, Val Loss=0.0459, Train Acc=99.70%, Val Acc=98.33%
2025-07-31 14:42:30 - pulsar_trainer - INFO - Validation F1: 98.3331, Precision: 98.3572, Recall: 98.3333
2025-07-31 14:42:35 - pulsar_trainer - INFO - Epoch  17: Train Loss=0.0194, Val Loss=0.0455, Train Acc=99.28%, Val Acc=98.61%
2025-07-31 14:42:35 - pulsar_trainer - INFO - Validation F1: 98.6111, Precision: 98.6126, Recall: 98.6111
2025-07-31 14:42:39 - pulsar_trainer - INFO - Epoch  18: Train Loss=0.0165, Val Loss=0.0411, Train Acc=99.40%, Val Acc=98.61%
2025-07-31 14:42:39 - pulsar_trainer - INFO - Validation F1: 98.6108, Precision: 98.6486, Recall: 98.6111
2025-07-31 14:42:43 - pulsar_trainer - INFO - Epoch  19: Train Loss=0.0100, Val Loss=0.0414, Train Acc=99.70%, Val Acc=98.89%
2025-07-31 14:42:43 - pulsar_trainer - INFO - Validation F1: 98.8889, Precision: 98.8889, Recall: 98.8889
2025-07-31 14:42:48 - pulsar_trainer - INFO - Epoch  20: Train Loss=0.0006, Val Loss=0.0486, Train Acc=100.00%, Val Acc=98.61%
2025-07-31 14:42:48 - pulsar_trainer - INFO - Validation F1: 98.6110, Precision: 98.6246, Recall: 98.6111
2025-07-31 14:42:48 - pulsar_trainer - INFO - 早停触发，在第 20 轮停止训练
2025-07-31 14:42:48 - pulsar_trainer - INFO - ============================================================
2025-07-31 14:42:48 - pulsar_trainer - INFO - ✅ 训练完成
2025-07-31 14:42:48 - pulsar_trainer - INFO - 🏆 最佳验证准确率: 98.8889
2025-07-31 14:42:48 - pulsar_trainer - INFO - ⏱️ 总训练时间: 130.99秒
2025-07-31 14:42:48 - pulsar_trainer - INFO - ============================================================
2025-07-31 14:42:48 - pulsar_trainer - INFO - 🔍 开始最终评估...
2025-07-31 14:42:48 - pulsar_trainer - INFO - 模型已加载: D:\pulsarSuanfa\outputs_TPP\models\best_model.pth
2025-07-31 14:43:10 - pulsar_trainer.utils.comprehensive_misclassification_analysis - INFO - 开始全面误分类分析...
2025-07-31 14:43:10 - pulsar_trainer.utils.comprehensive_misclassification_analysis - INFO - 综合误分类分析报告已生成: D:\pulsarSuanfa\outputs_TPP\results\comprehensive_misclassification_analysis\comprehensive_misclassification_report.txt
2025-07-31 14:43:10 - pulsar_trainer.utils.comprehensive_misclassification_analysis - INFO - 全面误分类分析完成。报告保存至: D:\pulsarSuanfa\outputs_TPP\results\comprehensive_misclassification_analysis
2025-07-31 14:43:10 - pulsar_trainer - INFO - 📊 评估结果:
2025-07-31 14:43:10 - pulsar_trainer - INFO -   - accuracy: 0.9721
2025-07-31 14:43:10 - pulsar_trainer - INFO -   - precision: 0.9568
2025-07-31 14:43:10 - pulsar_trainer - INFO -   - recall: 0.9888
2025-07-31 14:43:10 - pulsar_trainer - INFO -   - specificity: 0.9553
2025-07-31 14:43:10 - pulsar_trainer - INFO -   - f1_score: 0.9725
2025-07-31 14:43:10 - pulsar_trainer - INFO -   - false_positive_rate: 0.0447
2025-07-31 14:43:13 - pulsar_trainer - INFO - 所有结果已保存到: D:\pulsarSuanfa\outputs_TPP\results
2025-07-31 14:43:13 - pulsar_trainer - INFO - 可视化图表已保存到: D:\pulsarSuanfa\outputs_TPP\plots
