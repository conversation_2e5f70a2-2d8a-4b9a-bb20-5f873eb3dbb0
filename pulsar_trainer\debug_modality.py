#!/usr/bin/env python3
"""
模态检测调试脚本
"""

import sys
import os
from pathlib import Path

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

from utils.config import load_config_with_classes

def debug_modality_detection():
    """调试模态检测功能"""

    # 加载配置
    config_path = "config/coatnet_config.yaml"
    config = load_config_with_classes(config_path)

    print("=== 配置对象调试信息 ===")
    print(f"配置对象类型: {type(config)}")

    print(f"\n数据配置类型: {type(config.data)}")

    # 检查modality属性
    if hasattr(config.data, 'modality'):
        print(f"✅ 找到modality属性: {config.data.modality}")
    else:
        print("❌ 未找到modality属性")
        print(f"数据配置属性: {[attr for attr in dir(config.data) if not attr.startswith('_')]}")

    # 检查__dict__
    if hasattr(config.data, '__dict__'):
        print(f"数据配置__dict__: {config.data.__dict__}")

    # 检查配置对象的__dict__
    if hasattr(config, '__dict__'):
        print(f"配置对象__dict__键: {list(config.__dict__.keys())}")

if __name__ == '__main__':
    debug_modality_detection()
