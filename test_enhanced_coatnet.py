"""
测试增强版CoAtNet的集成结果
验证所有增强功能是否正确工作
"""

import torch
import sys
import os

# 添加路径以便导入模块
sys.path.append('pulsar_trainer/models')

from coatnet import CoAtNet, create_coatnet_from_config


class MockConfig:
    """模拟配置对象"""
    def __init__(self):
        self.image_size = [64, 64]
        self.in_channels = 1
        self.num_blocks = [2, 2, 3, 5, 2]
        self.channels = [64, 96, 192, 384, 768]
        print(f"Debug: channels configuration = {self.channels}")
        print(f"Debug: s2 stage channels (index 2) = {self.channels[2]}")
        print(f"Debug: s3 stage channels (index 3) = {self.channels[3]}")
        self.num_classes = 2
        self.block_types = ['C', 'C', 'T', 'T']

        # 增强模块配置（通道数会在CoAtNet中自动配置）
        self.enhancement_modules = {
            'fpp': {
                'freq_fusion_config': {
                    'compressed_channels': 48,
                    'use_high_pass': True,
                    'use_low_pass': True
                },
                'enabled': True
            },
            'tpp': {
                'cbam_config': {
                    'ratio': 16,
                    'kernel_size': 7
                },
                'loss_config': {
                    'beta': 1.0,
                    'alpha': 0.5,
                    'gamma': 0.3
                },
                'enabled': True
            }
        }

    def validate(self):
        """配置验证"""
        pass


def test_enhanced_coatnet():
    """测试增强版CoAtNet"""
    print("="*60)
    print("Testing Enhanced CoAtNet Integration")
    print("="*60)

    # 创建配置
    config = MockConfig()

    # 测试1: 创建增强版模型
    print("\n1. Testing enhanced model creation...")
    try:
        model = create_coatnet_from_config(config, config.enhancement_modules)
        print("✓ Enhanced CoAtNet created successfully")

        # 获取模型信息
        if model.enhancement_manager:
            info = model.enhancement_manager.get_module_info()
            print(f"✓ Enhancement modules info: {info}")

    except Exception as e:
        print(f"✗ Model creation failed: {e}")
        return False

    # 测试2: 前向传播
    print("\n2. Testing forward pass...")
    try:
        batch_size = 2
        x = torch.randn(batch_size, 1, 64, 64)

        with torch.no_grad():
            output = model(x)

        print(f"✓ Forward pass successful")
        print(f"  Input shape: {x.shape}")
        print(f"  Output shape: {output.shape}")

        expected_output_shape = (batch_size, config.num_classes)
        if output.shape == expected_output_shape:
            print(f"✓ Output shape correct: {output.shape}")
        else:
            print(f"✗ Output shape incorrect: expected {expected_output_shape}, got {output.shape}")
            return False

    except Exception as e:
        print(f"✗ Forward pass failed: {e}")
        return False

    # 测试3: 增强损失计算
    print("\n3. Testing enhanced loss computation...")
    try:
        predictions = torch.randn(batch_size, config.num_classes)
        targets = torch.randint(0, config.num_classes, (batch_size,))

        enhanced_loss = model.compute_enhanced_loss(predictions, targets)

        if enhanced_loss is not None:
            print(f"✓ Enhanced loss computed: {enhanced_loss.item():.4f}")

            # 获取损失组件
            loss_components = model.get_loss_components(predictions, targets)
            if loss_components:
                print(f"✓ Loss components: {loss_components}")
        else:
            print("✓ Enhanced loss is None (expected for some configurations)")

    except Exception as e:
        print(f"✗ Enhanced loss computation failed: {e}")
        return False

    # 测试4: 增强模块统计
    print("\n4. Testing enhancement statistics...")
    try:
        stats = model.get_enhancement_stats()
        if stats:
            print("✓ Enhancement statistics retrieved:")
            for key, value in stats.items():
                if isinstance(value, dict):
                    print(f"  {key}: {len(value)} items")
                else:
                    print(f"  {key}: {value}")
        else:
            print("✓ No enhancement statistics (expected for some configurations)")

    except Exception as e:
        print(f"✗ Enhancement statistics failed: {e}")
        return False

    # 测试5: 模态切换
    print("\n5. Testing modality switching...")
    try:
        # 测试FPP模态
        model.set_enhancement_modality('FPP')
        output_fpp = model(x)
        print(f"✓ FPP modality test passed, output shape: {output_fpp.shape}")

        # 测试TPP模态
        model.set_enhancement_modality('TPP')
        output_tpp = model(x)
        print(f"✓ TPP modality test passed, output shape: {output_tpp.shape}")

        # 测试AUTO模态
        model.set_enhancement_modality('AUTO')
        output_auto = model(x)
        print(f"✓ AUTO modality test passed, output shape: {output_auto.shape}")

    except Exception as e:
        print(f"✗ Modality switching failed: {e}")
        return False

    # 测试6: 增强模块启用/禁用
    print("\n6. Testing enhancement enable/disable...")
    try:
        # 禁用增强
        model.disable_enhancements()
        output_disabled = model(x)
        print(f"✓ Enhancements disabled, output shape: {output_disabled.shape}")

        # 启用增强
        model.enable_enhancements()
        output_enabled = model(x)
        print(f"✓ Enhancements enabled, output shape: {output_enabled.shape}")

    except Exception as e:
        print(f"✗ Enhancement enable/disable failed: {e}")
        return False

    # 测试7: 无增强模块的模型
    print("\n7. Testing model without enhancements...")
    try:
        config_no_enhancement = MockConfig()
        config_no_enhancement.enhancement_modules = None

        model_no_enhancement = create_coatnet_from_config(config_no_enhancement, config_no_enhancement.enhancement_modules)
        output_no_enhancement = model_no_enhancement(x)

        print(f"✓ Model without enhancements works, output shape: {output_no_enhancement.shape}")

        # 验证增强功能返回None
        enhanced_loss = model_no_enhancement.compute_enhanced_loss(predictions, targets)
        if enhanced_loss is None:
            print("✓ Enhanced loss correctly returns None for non-enhanced model")

    except Exception as e:
        print(f"✗ Non-enhanced model test failed: {e}")
        return False

    print("\n" + "="*60)
    print("✅ All Enhanced CoAtNet Integration Tests Passed!")
    print("="*60)

    return True


def test_parameter_count():
    """测试参数数量"""
    print("\nTesting parameter counts...")

    config = MockConfig()

    # 无增强模块的模型
    config.enhancement_modules = None
    model_base = create_coatnet_from_config(config, config.enhancement_modules)
    base_params = sum(p.numel() for p in model_base.parameters() if p.requires_grad)

    # 有增强模块的模型
    config.enhancement_modules = {
        'fpp': {'enabled': True},
        'tpp': {'enabled': True}
    }
    model_enhanced = create_coatnet_from_config(config, config.enhancement_modules)
    enhanced_params = sum(p.numel() for p in model_enhanced.parameters() if p.requires_grad)

    print(f"Base model parameters: {base_params:,}")
    print(f"Enhanced model parameters: {enhanced_params:,}")
    print(f"Enhancement overhead: {enhanced_params - base_params:,} parameters")
    print(f"Relative increase: {((enhanced_params - base_params) / base_params * 100):.2f}%")


if __name__ == '__main__':
    success = test_enhanced_coatnet()

    if success:
        test_parameter_count()
        print("\n🎉 Enhanced CoAtNet integration completed successfully!")
        print("Ready to proceed to configuration management phase.")
    else:
        print("\n❌ Enhanced CoAtNet integration failed!")
        print("Please check the implementation and fix issues.")
