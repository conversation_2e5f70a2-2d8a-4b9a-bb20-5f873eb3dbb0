"""
Backward Compatibility Tests
确保增强版CoAtNet与现有系统完全兼容，支持渐进式迁移
"""

import torch
import torch.nn as nn
import unittest
import sys
import os
import yaml
import warnings

# 添加路径以便导入模块
sys.path.append('pulsar_trainer/models')

from coatnet import CoAtNet, create_coatnet_from_config


class LegacyConfig:
    """模拟旧版配置对象（不包含增强模块）"""
    def __init__(self):
        self.image_size = [64, 64]
        self.in_channels = 1
        self.num_blocks = [2, 2, 3, 5, 2]
        self.channels = [64, 96, 192, 384, 768]
        self.num_classes = 2
        self.block_types = ['C', 'C', 'T', 'T']
        # 注意：没有enhancement_modules属性
    
    def validate(self):
        pass


class ModernConfig:
    """现代配置对象（包含增强模块）"""
    def __init__(self, enhancement_enabled=True):
        self.image_size = [64, 64]
        self.in_channels = 1
        self.num_blocks = [2, 2, 3, 5, 2]
        self.channels = [64, 96, 192, 384, 768]
        self.num_classes = 2
        self.block_types = ['C', 'C', 'T', 'T']
        
        if enhancement_enabled:
            self.enhancement_modules = {
                'fpp': {
                    'enabled': True,
                    'freq_fusion_config': {
                        'compressed_channels': 24,
                        'use_high_pass': True,
                        'use_low_pass': True
                    }
                },
                'tpp': {
                    'enabled': True,
                    'cbam_config': {'ratio': 16, 'kernel_size': 7},
                    'loss_config': {'beta': 1.0, 'alpha': 0.5, 'gamma': 0.3}
                }
            }
        else:
            self.enhancement_modules = {
                'fpp': {'enabled': False},
                'tpp': {'enabled': False}
            }
    
    def validate(self):
        pass


class TestBackwardCompatibility(unittest.TestCase):
    """向后兼容性测试"""
    
    def test_legacy_config_support(self):
        """测试旧配置文件支持"""
        legacy_config = LegacyConfig()
        
        # 旧配置应该能够正常创建模型
        try:
            model = create_coatnet_from_config(legacy_config)
            self.assertIsInstance(model, CoAtNet)
            self.assertIsNone(model.enhancement_manager, "Legacy config should not have enhancement manager")
            print("✓ Legacy config support test passed")
        except Exception as e:
            self.fail(f"Legacy config support failed: {e}")
    
    def test_legacy_model_behavior(self):
        """测试旧模型行为保持不变"""
        legacy_config = LegacyConfig()
        model = create_coatnet_from_config(legacy_config)
        
        batch_size = 2
        x = torch.randn(batch_size, 1, 64, 64)
        
        with torch.no_grad():
            output = model(x)
        
        # 输出形状应该正确
        expected_shape = (batch_size, legacy_config.num_classes)
        self.assertEqual(output.shape, expected_shape)
        
        # 增强功能应该返回None
        predictions = torch.randn(batch_size, 2)
        targets = torch.randint(0, 2, (batch_size,))
        
        enhanced_loss = model.compute_enhanced_loss(predictions, targets)
        self.assertIsNone(enhanced_loss, "Legacy model should not compute enhanced loss")
        
        stats = model.get_enhancement_stats()
        self.assertIsNone(stats, "Legacy model should not have enhancement stats")
        
        print("✓ Legacy model behavior test passed")
    
    def test_enhancement_disabled_behavior(self):
        """测试增强功能禁用时的行为"""
        config = ModernConfig(enhancement_enabled=False)
        model = create_coatnet_from_config(config)
        
        batch_size = 2
        x = torch.randn(batch_size, 1, 64, 64)
        
        with torch.no_grad():
            output = model(x)
        
        # 应该有增强管理器但功能禁用
        self.assertIsNotNone(model.enhancement_manager)
        
        # 获取统计信息
        stats = model.get_enhancement_stats()
        if stats:
            # 应该没有激活
            self.assertEqual(stats['manager']['fpp_forwards'], 0)
            self.assertEqual(stats['manager']['tpp_forwards'], 0)
        
        print("✓ Enhancement disabled behavior test passed")
    
    def test_gradual_migration_support(self):
        """测试渐进式迁移支持"""
        # 阶段1：只启用FPP增强
        config_fpp_only = ModernConfig(enhancement_enabled=True)
        config_fpp_only.enhancement_modules['tpp']['enabled'] = False
        
        model_fpp_only = create_coatnet_from_config(config_fpp_only)
        
        # 阶段2：只启用TPP增强
        config_tpp_only = ModernConfig(enhancement_enabled=True)
        config_tpp_only.enhancement_modules['fpp']['enabled'] = False
        
        model_tpp_only = create_coatnet_from_config(config_tpp_only)
        
        # 阶段3：启用所有增强
        config_full = ModernConfig(enhancement_enabled=True)
        model_full = create_coatnet_from_config(config_full)
        
        # 所有模型都应该正常工作
        x = torch.randn(2, 1, 64, 64)
        
        for model_name, model in [
            ("FPP-only", model_fpp_only),
            ("TPP-only", model_tpp_only),
            ("Full", model_full)
        ]:
            with torch.no_grad():
                output = model(x)
            self.assertEqual(output.shape, (2, 2))
            print(f"✓ {model_name} model works correctly")
        
        print("✓ Gradual migration support test passed")
    
    def test_config_hot_update(self):
        """测试配置热更新"""
        config = ModernConfig(enhancement_enabled=True)
        model = create_coatnet_from_config(config)
        
        x = torch.randn(2, 1, 64, 64)
        
        # 初始状态：增强启用
        model.enable_enhancements()
        with torch.no_grad():
            output1 = model(x)
        
        # 热更新：禁用增强
        model.disable_enhancements()
        with torch.no_grad():
            output2 = model(x)
        
        # 热更新：重新启用增强
        model.enable_enhancements()
        with torch.no_grad():
            output3 = model(x)
        
        # 所有输出形状应该相同
        self.assertEqual(output1.shape, output2.shape)
        self.assertEqual(output2.shape, output3.shape)
        
        print("✓ Config hot update test passed")
    
    def test_model_state_preservation(self):
        """测试模型状态保持"""
        config = ModernConfig(enhancement_enabled=True)
        model = create_coatnet_from_config(config)
        
        # 保存初始状态
        initial_state = model.state_dict()
        
        # 执行一些操作
        x = torch.randn(2, 1, 64, 64)
        with torch.no_grad():
            _ = model(x)
        
        # 切换增强状态
        model.disable_enhancements()
        model.enable_enhancements()
        
        # 重置统计
        model.reset_enhancement_stats()
        
        # 模型参数应该保持不变
        current_state = model.state_dict()
        
        for key in initial_state:
            if key in current_state:
                self.assertTrue(
                    torch.equal(initial_state[key], current_state[key]),
                    f"Parameter {key} should remain unchanged"
                )
        
        print("✓ Model state preservation test passed")


class TestAPICompatibility(unittest.TestCase):
    """API兼容性测试"""
    
    def test_original_api_unchanged(self):
        """测试原始API保持不变"""
        # 原始的CoAtNet构造函数应该仍然工作
        try:
            model = CoAtNet(
                image_size=(64, 64),
                in_channels=1,
                num_blocks=[2, 2, 3, 5, 2],
                channels=[64, 96, 192, 384, 768],
                num_classes=2,
                block_types=['C', 'C', 'T', 'T']
            )
            
            self.assertIsInstance(model, CoAtNet)
            self.assertIsNone(model.enhancement_manager)
            
            print("✓ Original API unchanged test passed")
            
        except Exception as e:
            self.fail(f"Original API compatibility failed: {e}")
    
    def test_enhanced_api_additions(self):
        """测试增强API添加"""
        config = ModernConfig(enhancement_enabled=True)
        model = create_coatnet_from_config(config)
        
        # 新增的方法应该存在
        new_methods = [
            'compute_enhanced_loss',
            'get_loss_components',
            'get_enhancement_stats',
            'set_enhancement_modality',
            'enable_enhancements',
            'disable_enhancements',
            'reset_enhancement_stats'
        ]
        
        for method_name in new_methods:
            self.assertTrue(
                hasattr(model, method_name),
                f"Enhanced API should have {method_name} method"
            )
        
        print("✓ Enhanced API additions test passed")
    
    def test_method_signature_compatibility(self):
        """测试方法签名兼容性"""
        legacy_config = LegacyConfig()
        modern_config = ModernConfig(enhancement_enabled=True)
        
        legacy_model = create_coatnet_from_config(legacy_config)
        modern_model = create_coatnet_from_config(modern_config)
        
        # forward方法应该有相同的签名
        x = torch.randn(2, 1, 64, 64)
        
        with torch.no_grad():
            legacy_output = legacy_model(x)
            modern_output = modern_model(x)
        
        # 输出形状应该相同
        self.assertEqual(legacy_output.shape, modern_output.shape)
        
        print("✓ Method signature compatibility test passed")


class TestConfigurationCompatibility(unittest.TestCase):
    """配置兼容性测试"""
    
    def test_yaml_config_backward_compatibility(self):
        """测试YAML配置向后兼容性"""
        # 模拟旧版YAML配置（没有enhancement_modules）
        legacy_yaml_content = """
project:
  name: "pulsar_classification"
  version: "1.0.0"

data:
  modality: "FPP"
  
model:
  name: "CoAtNet"
  image_size: [64, 64]
  in_channels: 1
  num_blocks: [2, 2, 3, 5, 2]
  channels: [64, 96, 192, 384, 768]
  num_classes: 2
  block_types: ["C", "C", "T", "T"]

training:
  batch_size: 32
  learning_rate: 0.001
"""
        
        try:
            config_dict = yaml.safe_load(legacy_yaml_content)
            
            # 应该能够正常解析
            self.assertIn('model', config_dict)
            self.assertNotIn('enhancement_modules', config_dict)
            
            print("✓ YAML config backward compatibility test passed")
            
        except Exception as e:
            self.fail(f"YAML config compatibility failed: {e}")
    
    def test_config_migration_path(self):
        """测试配置迁移路径"""
        # 旧配置 -> 新配置的迁移应该是平滑的
        
        # 步骤1：旧配置
        legacy_config = LegacyConfig()
        legacy_model = create_coatnet_from_config(legacy_config)
        
        # 步骤2：添加增强配置但禁用
        modern_config_disabled = ModernConfig(enhancement_enabled=False)
        modern_model_disabled = create_coatnet_from_config(modern_config_disabled)
        
        # 步骤3：启用增强配置
        modern_config_enabled = ModernConfig(enhancement_enabled=True)
        modern_model_enabled = create_coatnet_from_config(modern_config_enabled)
        
        # 所有模型都应该能正常工作
        x = torch.randn(2, 1, 64, 64)
        
        with torch.no_grad():
            output1 = legacy_model(x)
            output2 = modern_model_disabled(x)
            output3 = modern_model_enabled(x)
        
        # 输出形状应该一致
        self.assertEqual(output1.shape, output2.shape)
        self.assertEqual(output2.shape, output3.shape)
        
        print("✓ Config migration path test passed")


def run_compatibility_tests():
    """运行所有兼容性测试"""
    print("="*60)
    print("Backward Compatibility Tests")
    print("="*60)
    
    # 创建测试套件
    test_classes = [
        TestBackwardCompatibility,
        TestAPICompatibility,
        TestConfigurationCompatibility
    ]
    
    total_tests = 0
    passed_tests = 0
    
    for test_class in test_classes:
        print(f"\n--- Testing {test_class.__name__} ---")
        
        # 获取测试方法
        test_methods = [method for method in dir(test_class) if method.startswith('test_')]
        
        for method_name in test_methods:
            total_tests += 1
            try:
                # 创建测试实例并运行测试
                test_instance = test_class()
                test_instance.setUp() if hasattr(test_instance, 'setUp') else None
                test_method = getattr(test_instance, method_name)
                test_method()
                passed_tests += 1
            except Exception as e:
                print(f"✗ {method_name} failed: {e}")
    
    print("\n" + "="*60)
    print(f"Compatibility Tests Results: {passed_tests}/{total_tests} tests passed")
    print("="*60)
    
    return passed_tests == total_tests


if __name__ == '__main__':
    success = run_compatibility_tests()
    
    if success:
        print("\n🎉 All compatibility tests passed!")
        print("Enhanced CoAtNet is fully backward compatible!")
    else:
        print("\n❌ Some compatibility tests failed!")
        print("Please fix compatibility issues before deployment.")
        exit(1)
