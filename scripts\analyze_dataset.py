#!/usr/bin/env python3
"""
Analyze current dataset distribution and create backup.
"""

import numpy as np
import shutil
from pathlib import Path
from collections import defaultdict
import json

def analyze_dataset(data_root: str):
    """Analyze current dataset distribution."""
    data_root = Path(data_root)
    
    analysis = {
        'modalities': {},
        'total_samples': 0,
        'issues': []
    }
    
    for modality in ['FPP', 'TPP']:
        modality_path = data_root / modality
        if not modality_path.exists():
            analysis['issues'].append(f"Missing modality directory: {modality}")
            continue
        
        modality_data = {
            'splits': {},
            'total': 0
        }
        
        for split in ['train', 'validation', 'test']:
            split_path = modality_path / split
            if not split_path.exists():
                analysis['issues'].append(f"Missing split directory: {modality}/{split}")
                continue
            
            # Count files by label
            positive_files = list(split_path.glob('*positive*.npy'))
            negative_files = list(split_path.glob('*negative*.npy'))
            
            split_data = {
                'positive': len(positive_files),
                'negative': len(negative_files),
                'total': len(positive_files) + len(negative_files),
                'balance_ratio': len(positive_files) / (len(positive_files) + len(negative_files)) if (len(positive_files) + len(negative_files)) > 0 else 0
            }
            
            modality_data['splits'][split] = split_data
            modality_data['total'] += split_data['total']
        
        analysis['modalities'][modality] = modality_data
        analysis['total_samples'] += modality_data['total']
    
    return analysis

def backup_dataset(data_root: str, backup_root: str):
    """Create backup of current dataset."""
    data_root = Path(data_root)
    backup_root = Path(backup_root)
    
    print(f"📦 创建数据集备份: {data_root} -> {backup_root}")
    
    if backup_root.exists():
        print(f"⚠️ 备份目录已存在，将被覆盖: {backup_root}")
        shutil.rmtree(backup_root)
    
    shutil.copytree(data_root, backup_root)
    print(f"✅ 备份完成")

def verify_data_integrity(data_root: str, sample_size: int = 10):
    """Verify data file integrity by sampling files."""
    data_root = Path(data_root)
    
    integrity_report = {
        'total_checked': 0,
        'valid_files': 0,
        'invalid_files': 0,
        'errors': []
    }
    
    for modality in ['FPP', 'TPP']:
        modality_path = data_root / modality
        if not modality_path.exists():
            continue
            
        for split in ['train', 'validation', 'test']:
            split_path = modality_path / split
            if not split_path.exists():
                continue
            
            # Get all files
            all_files = list(split_path.glob('*.npy'))
            
            # Sample files for checking
            sample_files = all_files[:min(sample_size, len(all_files))]
            
            for file_path in sample_files:
                integrity_report['total_checked'] += 1
                
                try:
                    data = np.load(file_path)
                    
                    # Check data shape
                    if data.shape not in [(64, 64), (64, 64, 1)]:
                        error_msg = f"Invalid shape {data.shape} in {file_path}"
                        integrity_report['errors'].append(error_msg)
                        integrity_report['invalid_files'] += 1
                    else:
                        integrity_report['valid_files'] += 1
                        
                except Exception as e:
                    error_msg = f"Cannot load {file_path}: {e}"
                    integrity_report['errors'].append(error_msg)
                    integrity_report['invalid_files'] += 1
    
    return integrity_report

if __name__ == "__main__":
    data_root = "D:/pulsarSuanfa/datasets/HTRU"
    backup_root = "D:/pulsarSuanfa/datasets/HTRU_backup_original"
    
    print("🔍 开始数据集分析...")
    
    # Analyze current dataset
    analysis = analyze_dataset(data_root)
    
    # Save analysis
    with open("dataset_analysis.json", "w") as f:
        json.dump(analysis, f, indent=2)
    
    print("📊 数据集分析结果:")
    print(json.dumps(analysis, indent=2))
    
    # Verify data integrity
    print("\n🔍 验证数据完整性...")
    integrity = verify_data_integrity(data_root)
    
    print(f"检查文件数: {integrity['total_checked']}")
    print(f"有效文件: {integrity['valid_files']}")
    print(f"无效文件: {integrity['invalid_files']}")
    
    if integrity['errors']:
        print("发现的错误:")
        for error in integrity['errors']:
            print(f"  - {error}")
    
    # Create backup
    print(f"\n📦 创建数据集备份...")
    backup_dataset(data_root, backup_root)
    
    print("\n✅ 数据集分析和备份完成！")
