"""
CF_Loss_2D - 适配2D二分类的临床相关特征优化损失函数
基于原始CF_Loss_3D适配，专门用于脉冲星分类中的TPP数据处理

原始论文: CF-Loss: Clinically-relevant feature optimised loss function (Elsevier 2024)
适配用途: 脉冲星TPP数据的类别不平衡损失优化，解决系统性偏差问题
"""

import torch
import torch.nn as nn
import torch.nn.functional as F


def encode_mask_2d(ground_truth, num_classes=2):
    """
    将2D标签编码为one-hot格式
    
    Args:
        ground_truth: [B, 1] 或 [B] - 类别标签
        num_classes: 类别数量，脉冲星分类为2
        
    Returns:
        one_hot: [B, num_classes] - one-hot编码
    """
    if len(ground_truth.shape) > 1:
        ground_truth = ground_truth.squeeze()
    
    batch_size = ground_truth.size(0)
    device = ground_truth.device
    
    one_hot = torch.zeros((batch_size, num_classes), device=device)
    ground_truth = ground_truth.long()
    one_hot = one_hot.scatter_(1, ground_truth.unsqueeze(1), 1)
    
    return one_hot


class CF_Loss_2D(nn.Module):
    """
    2D二分类的临床相关特征优化损失函数
    
    专门适配脉冲星分类任务的类别不平衡问题：
    - 从3D多类分割适配为2D二分类
    - 结合交叉熵损失、特征差异损失和体积差异损失
    - 解决脉冲星分类中的系统性偏差和高置信度误分类问题
    
    损失组成：
    1. CE Loss: 标准交叉熵损失，保证基本分类能力
    2. FD Loss: 特征差异损失，增强特征判别能力
    3. VD Loss: 体积差异损失，处理类别不平衡
    
    Args:
        beta: 交叉熵损失权重
        alpha: 特征差异损失权重
        gamma: 体积差异损失权重
        
    Input:
        prediction: [B, 2] - 模型预测logits
        ground_truth: [B] 或 [B, 1] - 真实标签
        
    Output:
        loss_value: 标量 - 综合损失值
    """
    
    def __init__(self, beta=1.0, alpha=0.5, gamma=0.3):
        super(CF_Loss_2D, self).__init__()
        self.beta = beta
        self.alpha = alpha
        self.gamma = gamma
        
        # 交叉熵损失
        self.CE = nn.CrossEntropyLoss()
        
        # 动态设备检测
        self.register_buffer('dummy', torch.tensor(0.0))
        
        # 特征尺度参数（适配2D）
        self.register_buffer('base_size', torch.tensor(2.0))
        
    def get_device(self):
        """获取当前设备"""
        return self.dummy.device
    
    def compute_feature_difference_loss(self, prediction_softmax, ground_truth_encoded):
        """
        计算特征差异损失
        
        Args:
            prediction_softmax: [B, 2] - 预测概率
            ground_truth_encoded: [B, 2] - one-hot标签
            
        Returns:
            fd_loss: 特征差异损失
        """
        device = self.get_device()
        batch_size = prediction_softmax.shape[0]
        
        # 计算预测和真实标签的差异
        diff_0 = torch.abs(prediction_softmax[:, 0] - ground_truth_encoded[:, 0])
        diff_1 = torch.abs(prediction_softmax[:, 1] - ground_truth_encoded[:, 1])
        
        # 特征差异损失：鼓励预测概率接近真实标签
        fd_loss = (diff_0.sum() + diff_1.sum()) / (2 * batch_size)
        
        return fd_loss
    
    def compute_volume_difference_loss(self, prediction_softmax, ground_truth_encoded):
        """
        计算体积差异损失（类别不平衡处理）
        
        Args:
            prediction_softmax: [B, 2] - 预测概率
            ground_truth_encoded: [B, 2] - one-hot标签
            
        Returns:
            vd_loss: 体积差异损失
        """
        batch_size = prediction_softmax.shape[0]
        
        # 计算每个类别的总预测量和真实量
        pred_class_0 = prediction_softmax[:, 0].sum()
        pred_class_1 = prediction_softmax[:, 1].sum()
        
        true_class_0 = ground_truth_encoded[:, 0].sum()
        true_class_1 = ground_truth_encoded[:, 1].sum()
        
        # 体积差异损失：平衡类别分布
        vd_loss = (torch.abs(pred_class_0 - true_class_0) + 
                   torch.abs(pred_class_1 - true_class_1)) / batch_size
        
        return vd_loss
    
    def compute_confidence_penalty(self, prediction_softmax):
        """
        计算置信度惩罚，减少过度自信的预测
        
        Args:
            prediction_softmax: [B, 2] - 预测概率
            
        Returns:
            confidence_penalty: 置信度惩罚项
        """
        # 计算预测的最大概率（置信度）
        max_probs, _ = torch.max(prediction_softmax, dim=1)
        
        # 对过度自信的预测进行惩罚
        # 当置信度 > 0.95 时开始惩罚
        high_confidence_mask = max_probs > 0.95
        confidence_penalty = torch.mean(max_probs[high_confidence_mask] - 0.95) if high_confidence_mask.any() else torch.tensor(0.0, device=self.get_device())
        
        return confidence_penalty * 0.1  # 较小的权重
    
    def forward(self, prediction, ground_truth):
        """
        前向传播计算损失
        
        Args:
            prediction: [B, 2] - 模型预测logits
            ground_truth: [B] 或 [B, 1] - 真实标签
            
        Returns:
            loss_value: 综合损失值
        """
        # 输入验证
        assert len(prediction.shape) == 2 and prediction.shape[1] == 2, \
            f"Prediction must be [B, 2], got shape {prediction.shape}"
        
        if len(ground_truth.shape) > 1:
            ground_truth = ground_truth.squeeze()
        
        assert len(ground_truth.shape) == 1, \
            f"Ground truth must be [B], got shape {ground_truth.shape}"
        
        # 编码ground truth为one-hot
        ground_truth_encoded = encode_mask_2d(ground_truth, num_classes=2)
        
        # 计算softmax概率
        prediction_softmax = F.softmax(prediction, dim=1)
        
        # 1. 交叉熵损失
        loss_CE = self.CE(prediction, ground_truth)
        
        # 2. 特征差异损失
        loss_FD = self.compute_feature_difference_loss(prediction_softmax, ground_truth_encoded)
        
        # 3. 体积差异损失（类别不平衡处理）
        loss_VD = self.compute_volume_difference_loss(prediction_softmax, ground_truth_encoded)
        
        # 4. 置信度惩罚（可选，减少过度自信）
        confidence_penalty = self.compute_confidence_penalty(prediction_softmax)
        
        # 综合损失
        loss_value = (self.beta * loss_CE + 
                     self.alpha * loss_FD + 
                     self.gamma * loss_VD + 
                     confidence_penalty)
        
        return loss_value
    
    def get_loss_components(self, prediction, ground_truth):
        """
        获取各个损失组件的详细信息（用于调试和监控）
        
        Returns:
            dict: 包含各个损失组件的字典
        """
        with torch.no_grad():
            if len(ground_truth.shape) > 1:
                ground_truth = ground_truth.squeeze()
            
            ground_truth_encoded = encode_mask_2d(ground_truth, num_classes=2)
            prediction_softmax = F.softmax(prediction, dim=1)
            
            loss_CE = self.CE(prediction, ground_truth)
            loss_FD = self.compute_feature_difference_loss(prediction_softmax, ground_truth_encoded)
            loss_VD = self.compute_volume_difference_loss(prediction_softmax, ground_truth_encoded)
            confidence_penalty = self.compute_confidence_penalty(prediction_softmax)
            
            return {
                'ce_loss': loss_CE.item(),
                'fd_loss': loss_FD.item(),
                'vd_loss': loss_VD.item(),
                'confidence_penalty': confidence_penalty.item(),
                'total_loss': (self.beta * loss_CE + self.alpha * loss_FD + 
                              self.gamma * loss_VD + confidence_penalty).item()
            }


# 测试代码
if __name__ == '__main__':
    # 测试CF_Loss_2D
    batch_size = 32
    num_classes = 2
    
    # 创建测试数据
    predictions = torch.randn(batch_size, num_classes)  # 模型输出logits
    ground_truth = torch.randint(0, num_classes, (batch_size,))  # 真实标签
    
    print(f"Predictions shape: {predictions.shape}")
    print(f"Ground truth shape: {ground_truth.shape}")
    print(f"Ground truth sample: {ground_truth[:5]}")
    
    # 初始化损失函数
    cf_loss = CF_Loss_2D(beta=1.0, alpha=0.5, gamma=0.3)
    print(f"CF_Loss_2D initialized with beta={cf_loss.beta}, alpha={cf_loss.alpha}, gamma={cf_loss.gamma}")
    
    # 计算损失
    loss = cf_loss(predictions, ground_truth)
    print(f"Total loss: {loss.item():.4f}")
    
    # 获取损失组件详情
    loss_components = cf_loss.get_loss_components(predictions, ground_truth)
    print("Loss components:")
    for key, value in loss_components.items():
        print(f"  {key}: {value:.4f}")
    
    print("CF_Loss_2D adaptation completed successfully!")
