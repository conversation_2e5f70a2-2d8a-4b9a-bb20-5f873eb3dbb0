2025-07-31 17:48:26 - pulsar_trainer - INFO - <PERSON><PERSON> initialized. Log file: D:\pulsarSuanfa\outputs_FPP\logs\pulsar_trainer_20250731_174826.log
2025-07-31 17:48:26 - pulsar_trainer - INFO - 随机种子设置为: 42
2025-07-31 17:48:26 - pulsar_trainer - INFO - 使用配置文件参数创建模型
2025-07-31 17:48:26 - pulsar_trainer - INFO - 增强模块配置已加载: FPP=True, TPP=True
2025-07-31 17:48:26 - pulsar_trainer - INFO - 模型配置 - num_blocks: [2, 2, 6, 8, 2]
2025-07-31 17:48:26 - pulsar_trainer - INFO - 模型配置 - channels: [96, 128, 256, 512, 1024]
2025-07-31 17:48:26 - pulsar_trainer - INFO - 模型配置 - in_channels: 1
2025-07-31 17:48:26 - pulsar_trainer - INFO - ✓ 增强模块已启用
2025-07-31 17:48:26 - pulsar_trainer - INFO -   - FPP模块可用: True
2025-07-31 17:48:26 - pulsar_trainer - INFO -   - TPP模块可用: True
2025-07-31 17:48:26 - pulsar_trainer - INFO -   - 当前模态: AUTO
2025-07-31 17:48:26 - pulsar_trainer - INFO - 模型架构详情:
2025-07-31 17:48:26 - pulsar_trainer - INFO -   总参数数量: 39,096,359
2025-07-31 17:48:26 - pulsar_trainer - INFO -   可训练参数: 39,096,359
2025-07-31 17:48:26 - pulsar_trainer - INFO -   模型大小: 149.14 MB
2025-07-31 17:48:26 - pulsar_trainer - INFO -   块类型配置: ['C', 'C', 'T', 'T']
2025-07-31 17:48:26 - pulsar_trainer - INFO -   各阶段块数: [2, 2, 6, 8, 2]
2025-07-31 17:48:26 - pulsar_trainer - INFO -   各阶段通道数: [96, 128, 256, 512, 1024]
2025-07-31 17:48:26 - pulsar_trainer - INFO - Using standard cross entropy loss
2025-07-31 17:48:26 - pulsar_trainer - INFO - 🖥️ 使用GPU: NVIDIA GeForce RTX 4050 Laptop GPU
2025-07-31 17:48:26 - pulsar_trainer - INFO - 💾 GPU内存: 6.4GB
2025-07-31 17:48:26 - pulsar_trainer - INFO - 📋 模型参数总数: 39,096,359
2025-07-31 17:48:26 - pulsar_trainer - INFO - 🏗️ 模型架构: CoAtNet
2025-07-31 17:48:26 - pulsar_trainer - INFO - 📚 数据集大小:
2025-07-31 17:48:26 - pulsar_trainer - INFO -   - 训练集: 1674
2025-07-31 17:48:26 - pulsar_trainer - INFO -   - 验证集: 360
2025-07-31 17:48:26 - pulsar_trainer - INFO -   - 测试集: 358
2025-07-31 17:48:26 - pulsar_trainer - INFO -   - 总计: 2392
2025-07-31 17:48:26 - pulsar_trainer - INFO - ============================================================
2025-07-31 17:48:26 - pulsar_trainer - INFO - 🚀 开始脉冲星分类训练
2025-07-31 17:48:26 - pulsar_trainer - INFO - 📊 模态: FPP
2025-07-31 17:48:26 - pulsar_trainer - INFO - 🧠 模型: coatnet
2025-07-31 17:48:26 - pulsar_trainer - INFO - 📦 批次大小: 32
2025-07-31 17:48:26 - pulsar_trainer - INFO - 🎯 学习率: 0.001
2025-07-31 17:48:26 - pulsar_trainer - INFO - 🔄 训练轮数: 100
2025-07-31 17:48:26 - pulsar_trainer - INFO - ============================================================
2025-07-31 17:48:51 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.8932035565376282, 'fd_loss': 0.5440161228179932, 'vd_loss': 0.6090659499168396, 'confidence_penalty': 0.0, 'total_loss': 1.3479315042495728}
2025-07-31 17:49:21 - pulsar_trainer - INFO - Epoch   1: Train Loss=3.3720, Val Loss=0.1656, Train Acc=67.86%, Val Acc=94.72%
2025-07-31 17:49:21 - pulsar_trainer - INFO - Validation F1: 94.7212, Precision: 94.7568, Recall: 94.7222
2025-07-31 17:49:22 - pulsar_trainer - INFO - 模型已保存: D:\pulsarSuanfa\outputs_FPP\models\best_model.pth
2025-07-31 17:49:22 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.5112698674201965, 'fd_loss': 0.1944449245929718, 'vd_loss': 0.20907187461853027, 'confidence_penalty': 0.0033959762658923864, 'total_loss': 0.6746098399162292}
2025-07-31 17:49:28 - pulsar_trainer - INFO - Epoch   2: Train Loss=0.2805, Val Loss=0.4161, Train Acc=92.29%, Val Acc=80.83%
2025-07-31 17:49:28 - pulsar_trainer - INFO - Validation F1: 80.1874, Precision: 85.4570, Recall: 80.8333
2025-07-31 17:49:28 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.4350283741950989, 'fd_loss': 0.26360076665878296, 'vd_loss': 0.4779698848724365, 'confidence_penalty': 0.004225362557917833, 'total_loss': 0.7144450545310974}
2025-07-31 17:49:34 - pulsar_trainer - INFO - Epoch   3: Train Loss=0.3373, Val Loss=0.1140, Train Acc=91.16%, Val Acc=97.22%
2025-07-31 17:49:34 - pulsar_trainer - INFO - Validation F1: 97.2219, Precision: 97.2456, Recall: 97.2222
2025-07-31 17:49:34 - pulsar_trainer - INFO - 模型已保存: D:\pulsarSuanfa\outputs_FPP\models\best_model.pth
2025-07-31 17:49:34 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.0051435064524412155, 'fd_loss': 0.0051164040341973305, 'vd_loss': 0.0003943443298339844, 'confidence_penalty': 0.004488361068069935, 'total_loss': 0.012308373115956783}
2025-07-31 17:49:40 - pulsar_trainer - INFO - Epoch   4: Train Loss=0.1551, Val Loss=0.1045, Train Acc=96.30%, Val Acc=96.67%
2025-07-31 17:49:40 - pulsar_trainer - INFO - Validation F1: 96.6663, Precision: 96.6897, Recall: 96.6667
2025-07-31 17:49:40 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.02183140069246292, 'fd_loss': 0.021318204700946808, 'vd_loss': 0.025760412216186523, 'confidence_penalty': 0.003139074658975005, 'total_loss': 0.04335770383477211}
2025-07-31 17:49:46 - pulsar_trainer - INFO - Epoch   5: Train Loss=0.1058, Val Loss=0.0819, Train Acc=97.01%, Val Acc=97.78%
2025-07-31 17:49:46 - pulsar_trainer - INFO - Validation F1: 97.7778, Precision: 97.7778, Recall: 97.7778
2025-07-31 17:49:46 - pulsar_trainer - INFO - 模型已保存: D:\pulsarSuanfa\outputs_FPP\models\best_model.pth
2025-07-31 17:49:47 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.136790931224823, 'fd_loss': 0.07340626418590546, 'vd_loss': 0.14343303442001343, 'confidence_penalty': 0.004650026094168425, 'total_loss': 0.22117400169372559}
2025-07-31 17:49:52 - pulsar_trainer - INFO - Epoch   6: Train Loss=0.1087, Val Loss=0.0959, Train Acc=96.83%, Val Acc=97.78%
2025-07-31 17:49:52 - pulsar_trainer - INFO - Validation F1: 97.7778, Precision: 97.7778, Recall: 97.7778
2025-07-31 17:49:52 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.003395124338567257, 'fd_loss': 0.003319808281958103, 'vd_loss': 0.0023910999298095703, 'confidence_penalty': 0.0048741805367171764, 'total_loss': 0.010646538808941841}
2025-07-31 17:49:58 - pulsar_trainer - INFO - Epoch   7: Train Loss=0.0888, Val Loss=0.0818, Train Acc=97.61%, Val Acc=98.33%
2025-07-31 17:49:58 - pulsar_trainer - INFO - Validation F1: 98.3331, Precision: 98.3572, Recall: 98.3333
2025-07-31 17:49:59 - pulsar_trainer - INFO - 模型已保存: D:\pulsarSuanfa\outputs_FPP\models\best_model.pth
2025-07-31 17:49:59 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.015270985662937164, 'fd_loss': 0.014842251315712929, 'vd_loss': 0.01264345645904541, 'confidence_penalty': 0.0040380582213401794, 'total_loss': 0.030523205175995827}
2025-07-31 17:50:04 - pulsar_trainer - INFO - Epoch   8: Train Loss=0.0813, Val Loss=0.0870, Train Acc=97.85%, Val Acc=97.50%
2025-07-31 17:50:04 - pulsar_trainer - INFO - Validation F1: 97.4998, Precision: 97.5132, Recall: 97.5000
2025-07-31 17:50:04 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.028178105130791664, 'fd_loss': 0.022249553352594376, 'vd_loss': 0.018192768096923828, 'confidence_penalty': 0.004468963947147131, 'total_loss': 0.04922967404127121}
2025-07-31 17:50:10 - pulsar_trainer - INFO - Epoch   9: Train Loss=0.0999, Val Loss=0.0714, Train Acc=97.61%, Val Acc=98.61%
2025-07-31 17:50:10 - pulsar_trainer - INFO - Validation F1: 98.6111, Precision: 98.6126, Recall: 98.6111
2025-07-31 17:50:11 - pulsar_trainer - INFO - 模型已保存: D:\pulsarSuanfa\outputs_FPP\models\best_model.pth
2025-07-31 17:50:11 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.26117709279060364, 'fd_loss': 0.03786798566579819, 'vd_loss': 0.07534390687942505, 'confidence_penalty': 0.0046650441363453865, 'total_loss': 0.3073793053627014}
2025-07-31 17:50:16 - pulsar_trainer - INFO - Epoch  10: Train Loss=0.0761, Val Loss=0.0784, Train Acc=98.09%, Val Acc=97.78%
2025-07-31 17:50:16 - pulsar_trainer - INFO - Validation F1: 97.7778, Precision: 97.7778, Recall: 97.7778
2025-07-31 17:50:16 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.00405246252194047, 'fd_loss': 0.0039845299907028675, 'vd_loss': 0.0008243322372436523, 'confidence_penalty': 0.004751124884933233, 'total_loss': 0.011043151840567589}
2025-07-31 17:50:22 - pulsar_trainer - INFO - Epoch  11: Train Loss=0.1449, Val Loss=0.1042, Train Acc=97.37%, Val Acc=97.50%
2025-07-31 17:50:22 - pulsar_trainer - INFO - Validation F1: 97.5000, Precision: 97.5015, Recall: 97.5000
2025-07-31 17:50:22 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.09147471934556961, 'fd_loss': 0.03839360922574997, 'vd_loss': 0.05087113380432129, 'confidence_penalty': 0.00402832543477416, 'total_loss': 0.12996117770671844}
2025-07-31 17:50:28 - pulsar_trainer - INFO - Epoch  12: Train Loss=0.0990, Val Loss=0.0876, Train Acc=97.49%, Val Acc=96.67%
2025-07-31 17:50:28 - pulsar_trainer - INFO - Validation F1: 96.6630, Precision: 96.8750, Recall: 96.6667
2025-07-31 17:50:28 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.0314810611307621, 'fd_loss': 0.02967819571495056, 'vd_loss': 0.056340157985687256, 'confidence_penalty': 0.004206263460218906, 'total_loss': 0.06742846965789795}
2025-07-31 17:50:34 - pulsar_trainer - INFO - Epoch  13: Train Loss=0.0964, Val Loss=0.0717, Train Acc=97.91%, Val Acc=98.06%
2025-07-31 17:50:34 - pulsar_trainer - INFO - Validation F1: 98.0555, Precision: 98.0570, Recall: 98.0556
2025-07-31 17:50:34 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.008399231359362602, 'fd_loss': 0.00810306891798973, 'vd_loss': 0.006038844585418701, 'confidence_penalty': 0.004569663666188717, 'total_loss': 0.01883208379149437}
2025-07-31 17:50:39 - pulsar_trainer - INFO - Epoch  14: Train Loss=0.1124, Val Loss=0.0810, Train Acc=97.13%, Val Acc=98.06%
2025-07-31 17:50:39 - pulsar_trainer - INFO - Validation F1: 98.0555, Precision: 98.0570, Recall: 98.0556
2025-07-31 17:50:39 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.016891874372959137, 'fd_loss': 0.01599266193807125, 'vd_loss': 0.005126208066940308, 'confidence_penalty': 0.004262004978954792, 'total_loss': 0.030688073486089706}
2025-07-31 17:50:45 - pulsar_trainer - INFO - Epoch  15: Train Loss=0.0877, Val Loss=0.0738, Train Acc=98.03%, Val Acc=98.33%
2025-07-31 17:50:45 - pulsar_trainer - INFO - Validation F1: 98.3333, Precision: 98.3393, Recall: 98.3333
2025-07-31 17:50:45 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.005602565128356218, 'fd_loss': 0.0054564569145441055, 'vd_loss': 0.005418181419372559, 'confidence_penalty': 0.004724828060716391, 'total_loss': 0.014681076630949974}
2025-07-31 17:50:51 - pulsar_trainer - INFO - Epoch  16: Train Loss=0.1049, Val Loss=0.1605, Train Acc=97.19%, Val Acc=97.22%
2025-07-31 17:50:51 - pulsar_trainer - INFO - Validation F1: 97.2215, Precision: 97.2747, Recall: 97.2222
2025-07-31 17:50:51 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.2713873088359833, 'fd_loss': 0.17606237530708313, 'vd_loss': 0.2357529103755951, 'confidence_penalty': 0.004495515953749418, 'total_loss': 0.4346398711204529}
2025-07-31 17:50:57 - pulsar_trainer - INFO - Epoch  17: Train Loss=0.0705, Val Loss=0.1059, Train Acc=98.09%, Val Acc=97.78%
2025-07-31 17:50:57 - pulsar_trainer - INFO - Validation F1: 97.7775, Precision: 97.8014, Recall: 97.7778
2025-07-31 17:50:57 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.011337527073919773, 'fd_loss': 0.010800483636558056, 'vd_loss': 0.002076178789138794, 'confidence_penalty': 0.004587200935930014, 'total_loss': 0.021947823464870453}
2025-07-31 17:51:03 - pulsar_trainer - INFO - Epoch  18: Train Loss=0.0707, Val Loss=0.0463, Train Acc=98.27%, Val Acc=98.89%
2025-07-31 17:51:03 - pulsar_trainer - INFO - Validation F1: 98.8889, Precision: 98.8949, Recall: 98.8889
2025-07-31 17:51:03 - pulsar_trainer - INFO - 模型已保存: D:\pulsarSuanfa\outputs_FPP\models\best_model.pth
2025-07-31 17:51:03 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.0004970243899151683, 'fd_loss': 0.0004962536040693521, 'vd_loss': 0.00021350383758544922, 'confidence_penalty': 0.00495037529617548, 'total_loss': 0.0057595777325332165}
2025-07-31 17:51:09 - pulsar_trainer - INFO - Epoch  19: Train Loss=0.0483, Val Loss=0.0585, Train Acc=99.22%, Val Acc=98.61%
2025-07-31 17:51:09 - pulsar_trainer - INFO - Validation F1: 98.6110, Precision: 98.6246, Recall: 98.6111
2025-07-31 17:51:09 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.0013261036947369576, 'fd_loss': 0.0013186028227210045, 'vd_loss': 0.0012254714965820312, 'confidence_penalty': 0.004868140444159508, 'total_loss': 0.007221186999231577}
2025-07-31 17:51:15 - pulsar_trainer - INFO - Epoch  20: Train Loss=0.0496, Val Loss=0.0357, Train Acc=98.69%, Val Acc=98.89%
2025-07-31 17:51:15 - pulsar_trainer - INFO - Validation F1: 98.8889, Precision: 98.8949, Recall: 98.8889
2025-07-31 17:51:15 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.19347958266735077, 'fd_loss': 0.04368497058749199, 'vd_loss': 0.07474648952484131, 'confidence_penalty': 0.004637507256120443, 'total_loss': 0.24238352477550507}
2025-07-31 17:51:21 - pulsar_trainer - INFO - Epoch  21: Train Loss=0.0329, Val Loss=0.0553, Train Acc=99.40%, Val Acc=98.89%
2025-07-31 17:51:21 - pulsar_trainer - INFO - Validation F1: 98.8889, Precision: 98.8949, Recall: 98.8889
2025-07-31 17:51:21 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.10995277762413025, 'fd_loss': 0.04716933146119118, 'vd_loss': 0.09420299530029297, 'confidence_penalty': 0.0046917772851884365, 'total_loss': 0.1664901226758957}
2025-07-31 17:51:26 - pulsar_trainer - INFO - Epoch  22: Train Loss=0.0631, Val Loss=0.1031, Train Acc=98.33%, Val Acc=97.50%
2025-07-31 17:51:26 - pulsar_trainer - INFO - Validation F1: 97.4991, Precision: 97.5719, Recall: 97.5000
2025-07-31 17:51:26 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.0032403613440692425, 'fd_loss': 0.00322659220546484, 'vd_loss': 0.00625273585319519, 'confidence_penalty': 0.004677341785281897, 'total_loss': 0.011406820267438889}
2025-07-31 17:51:32 - pulsar_trainer - INFO - Epoch  23: Train Loss=0.0635, Val Loss=0.0577, Train Acc=98.33%, Val Acc=98.06%
2025-07-31 17:51:32 - pulsar_trainer - INFO - Validation F1: 98.0555, Precision: 98.0570, Recall: 98.0556
2025-07-31 17:51:32 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.18693660199642181, 'fd_loss': 0.04237502068281174, 'vd_loss': 0.042120665311813354, 'confidence_penalty': 0.00432911841198802, 'total_loss': 0.225089430809021}
2025-07-31 17:51:38 - pulsar_trainer - INFO - Epoch  24: Train Loss=0.0438, Val Loss=0.0550, Train Acc=99.04%, Val Acc=98.33%
2025-07-31 17:51:38 - pulsar_trainer - INFO - Validation F1: 98.3333, Precision: 98.3393, Recall: 98.3333
2025-07-31 17:51:38 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.0014463827246800065, 'fd_loss': 0.0014439461519941688, 'vd_loss': 0.00071677565574646, 'confidence_penalty': 0.004855606704950333, 'total_loss': 0.007238995283842087}
2025-07-31 17:51:44 - pulsar_trainer - INFO - Epoch  25: Train Loss=0.0449, Val Loss=0.0564, Train Acc=99.04%, Val Acc=98.61%
2025-07-31 17:51:44 - pulsar_trainer - INFO - Validation F1: 98.6110, Precision: 98.6246, Recall: 98.6111
2025-07-31 17:51:44 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.21813812851905823, 'fd_loss': 0.03431679308414459, 'vd_loss': 0.05642658472061157, 'confidence_penalty': 0.004686897154897451, 'total_loss': 0.25691139698028564}
2025-07-31 17:51:50 - pulsar_trainer - INFO - Epoch  26: Train Loss=0.0451, Val Loss=0.0422, Train Acc=99.10%, Val Acc=98.06%
2025-07-31 17:51:50 - pulsar_trainer - INFO - Validation F1: 98.0555, Precision: 98.0570, Recall: 98.0556
2025-07-31 17:51:50 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.006281143985688686, 'fd_loss': 0.0060576992109417915, 'vd_loss': 0.00863105058670044, 'confidence_penalty': 0.004735831171274185, 'total_loss': 0.016635140404105186}
2025-07-31 17:51:55 - pulsar_trainer - INFO - Epoch  27: Train Loss=0.0403, Val Loss=0.0552, Train Acc=99.04%, Val Acc=98.61%
2025-07-31 17:51:55 - pulsar_trainer - INFO - Validation F1: 98.6111, Precision: 98.6126, Recall: 98.6111
2025-07-31 17:51:55 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.15590707957744598, 'fd_loss': 0.03641960397362709, 'vd_loss': 0.0726391077041626, 'confidence_penalty': 0.004432294517755508, 'total_loss': 0.2003408968448639}
2025-07-31 17:52:01 - pulsar_trainer - INFO - Epoch  28: Train Loss=0.0418, Val Loss=0.0601, Train Acc=99.28%, Val Acc=98.33%
2025-07-31 17:52:01 - pulsar_trainer - INFO - Validation F1: 98.3331, Precision: 98.3572, Recall: 98.3333
2025-07-31 17:52:01 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.22814242541790009, 'fd_loss': 0.04445766657590866, 'vd_loss': 0.03692099452018738, 'confidence_penalty': 0.004864001180976629, 'total_loss': 0.26631155610084534}
2025-07-31 17:52:07 - pulsar_trainer - INFO - Epoch  29: Train Loss=0.0378, Val Loss=0.0654, Train Acc=99.22%, Val Acc=98.06%
2025-07-31 17:52:07 - pulsar_trainer - INFO - Validation F1: 98.0554, Precision: 98.0689, Recall: 98.0556
2025-07-31 17:52:07 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.005223920568823814, 'fd_loss': 0.005180700682103634, 'vd_loss': 0.00812596082687378, 'confidence_penalty': 0.00448193121701479, 'total_loss': 0.014733990654349327}
2025-07-31 17:52:13 - pulsar_trainer - INFO - Epoch  30: Train Loss=0.0386, Val Loss=0.0574, Train Acc=99.22%, Val Acc=98.33%
2025-07-31 17:52:13 - pulsar_trainer - INFO - Validation F1: 98.3331, Precision: 98.3572, Recall: 98.3333
2025-07-31 17:52:13 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.004853842314332724, 'fd_loss': 0.004788408987224102, 'vd_loss': 0.0070413947105407715, 'confidence_penalty': 0.00468195928260684, 'total_loss': 0.014042424038052559}
2025-07-31 17:52:18 - pulsar_trainer - INFO - Epoch  31: Train Loss=0.0365, Val Loss=0.0488, Train Acc=99.10%, Val Acc=98.61%
2025-07-31 17:52:18 - pulsar_trainer - INFO - Validation F1: 98.6111, Precision: 98.6126, Recall: 98.6111
2025-07-31 17:52:18 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.0029558618552982807, 'fd_loss': 0.0029367262031883, 'vd_loss': 0.005510002374649048, 'confidence_penalty': 0.004706328269094229, 'total_loss': 0.010783554054796696}
2025-07-31 17:52:24 - pulsar_trainer - INFO - Epoch  32: Train Loss=0.0428, Val Loss=0.1042, Train Acc=98.92%, Val Acc=97.22%
2025-07-31 17:52:24 - pulsar_trainer - INFO - Validation F1: 97.2215, Precision: 97.2747, Recall: 97.2222
2025-07-31 17:52:24 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.02526944875717163, 'fd_loss': 0.02290121093392372, 'vd_loss': 0.04198092222213745, 'confidence_penalty': 0.004704446066170931, 'total_loss': 0.0540187731385231}
2025-07-31 17:52:30 - pulsar_trainer - INFO - Epoch  33: Train Loss=0.0557, Val Loss=0.1013, Train Acc=98.86%, Val Acc=98.06%
2025-07-31 17:52:30 - pulsar_trainer - INFO - Validation F1: 98.0555, Precision: 98.0570, Recall: 98.0556
2025-07-31 17:52:30 - pulsar_trainer - INFO - 早停触发，在第 33 轮停止训练
2025-07-31 17:52:30 - pulsar_trainer - INFO - ============================================================
2025-07-31 17:52:30 - pulsar_trainer - INFO - ✅ 训练完成
2025-07-31 17:52:30 - pulsar_trainer - INFO - 🏆 最佳验证准确率: 98.8889
2025-07-31 17:52:30 - pulsar_trainer - INFO - ⏱️ 总训练时间: 243.09秒
2025-07-31 17:52:30 - pulsar_trainer - INFO - ============================================================
2025-07-31 17:52:30 - pulsar_trainer - INFO - 🔍 开始最终评估...
2025-07-31 17:52:30 - pulsar_trainer - INFO - 模型已加载: D:\pulsarSuanfa\outputs_FPP\models\best_model.pth
2025-07-31 17:52:54 - pulsar_trainer.utils.comprehensive_misclassification_analysis - INFO - 开始全面误分类分析...
2025-07-31 17:52:54 - pulsar_trainer.utils.comprehensive_misclassification_analysis - INFO - 综合误分类分析报告已生成: D:\pulsarSuanfa\outputs_FPP\results\comprehensive_misclassification_analysis\comprehensive_misclassification_report.txt
2025-07-31 17:52:54 - pulsar_trainer.utils.comprehensive_misclassification_analysis - INFO - 全面误分类分析完成。报告保存至: D:\pulsarSuanfa\outputs_FPP\results\comprehensive_misclassification_analysis
2025-07-31 17:52:54 - pulsar_trainer - INFO - 📊 评估结果:
2025-07-31 17:52:54 - pulsar_trainer - INFO -   - accuracy: 0.9860
2025-07-31 17:52:54 - pulsar_trainer - INFO -   - precision: 0.9780
2025-07-31 17:52:54 - pulsar_trainer - INFO -   - recall: 0.9944
2025-07-31 17:52:54 - pulsar_trainer - INFO -   - specificity: 0.9777
2025-07-31 17:52:54 - pulsar_trainer - INFO -   - f1_score: 0.9861
2025-07-31 17:52:54 - pulsar_trainer - INFO -   - false_positive_rate: 0.0223
2025-07-31 17:52:54 - pulsar_trainer - INFO - 📊 增强模块统计信息:
2025-07-31 17:52:54 - pulsar_trainer - INFO -   - 总前向传播次数: 3546
2025-07-31 17:52:54 - pulsar_trainer - INFO -   - FPP激活次数: 1182
2025-07-31 17:52:54 - pulsar_trainer - INFO -   - TPP激活次数: 1182
2025-07-31 17:52:54 - pulsar_trainer - INFO -   - 损失计算次数: 954
2025-07-31 17:52:54 - pulsar_trainer - INFO -   - FPP激活率: 33.33%
2025-07-31 17:52:54 - pulsar_trainer - INFO -   - TPP激活率: 33.33%
2025-07-31 17:52:57 - pulsar_trainer - INFO - 所有结果已保存到: D:\pulsarSuanfa\outputs_FPP\results
2025-07-31 17:52:57 - pulsar_trainer - INFO - 可视化图表已保存到: D:\pulsarSuanfa\outputs_FPP\plots
