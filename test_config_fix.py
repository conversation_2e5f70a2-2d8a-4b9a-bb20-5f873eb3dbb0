"""
测试配置修复效果
验证FPP和TPP模块现在能否正确初始化
"""

import torch
import sys
import os
import yaml

# 添加路径以便导入模块
sys.path.append('pulsar_trainer/models')
sys.path.append('pulsar_trainer/utils')

from coatnet import CoAtNet, create_coatnet_from_config


class MockModelConfig:
    """模拟ModelConfig对象"""
    def __init__(self):
        self.image_size = [64, 64]
        self.in_channels = 1
        self.num_blocks = [2, 2, 3, 5, 2]
        self.channels = [64, 96, 192, 384, 768]
        self.num_classes = 2
        self.block_types = ['C', 'C', 'T', 'T']
    
    def validate(self):
        """配置验证"""
        pass


def test_config_fix():
    """测试配置修复效果"""
    print("="*60)
    print("测试配置键名修复效果")
    print("="*60)
    
    # 测试1: 加载修复后的YAML配置
    print("\n1. 加载修复后的YAML配置...")
    try:
        with open('pulsar_trainer/config/coatnet_config.yaml', 'r', encoding='utf-8') as f:
            config_dict = yaml.safe_load(f)
        
        enhancement_config = config_dict.get('enhancement_modules', None)
        
        if enhancement_config:
            print("✓ 增强模块配置加载成功")
            
            # 检查FPP配置
            fpp_config = enhancement_config.get('fpp', {})
            print(f"  - FPP启用: {fpp_config.get('enabled', False)}")
            print(f"  - FPP配置键: {list(fpp_config.keys())}")
            
            # 检查TPP配置
            tpp_config = enhancement_config.get('tpp', {})
            print(f"  - TPP启用: {tpp_config.get('enabled', False)}")
            print(f"  - TPP配置键: {list(tpp_config.keys())}")
            
        else:
            print("✗ 增强模块配置未找到")
            return False
            
    except Exception as e:
        print(f"✗ YAML配置加载失败: {e}")
        return False
    
    # 测试2: 创建增强模型（应该不再有键名错误）
    print("\n2. 测试增强模型创建...")
    try:
        model_config = MockModelConfig()
        
        # 创建增强模型
        model = create_coatnet_from_config(model_config, enhancement_config)
        
        if model.enhancement_manager is not None:
            print("✓ 增强模型创建成功")
            
            # 获取增强模块信息
            stats = model.get_enhancement_stats()
            if stats:
                manager_stats = stats.get('manager', {})
                fpp_available = stats.get('fpp_module', {}).get('status') != 'not_initialized'
                tpp_available = stats.get('tpp_module', {}).get('status') != 'not_initialized'
                
                print(f"  - FPP模块可用: {fpp_available}")
                print(f"  - TPP模块可用: {tpp_available}")
                print(f"  - 当前模态: {manager_stats.get('current_modality', 'unknown')}")
                
                if fpp_available and tpp_available:
                    print("🎉 两个增强模块都成功初始化！")
                    return True
                else:
                    print("⚠️ 部分增强模块初始化失败")
                    return False
            else:
                print("⚠️ 无法获取增强模块统计信息")
                return False
        else:
            print("✗ 增强管理器未创建")
            return False
            
    except Exception as e:
        print(f"✗ 增强模型创建失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    # 测试3: 验证前向传播
    print("\n3. 测试前向传播...")
    try:
        x = torch.randn(2, 1, 64, 64)
        
        with torch.no_grad():
            output = model(x)
        
        print(f"✓ 前向传播成功，输出形状: {output.shape}")
        
        # 检查增强模块是否被激活
        final_stats = model.get_enhancement_stats()
        if final_stats:
            manager_stats = final_stats.get('manager', {})
            total_forwards = manager_stats.get('total_forwards', 0)
            fpp_forwards = manager_stats.get('fpp_forwards', 0)
            tpp_forwards = manager_stats.get('tpp_forwards', 0)
            
            print(f"  - 总前向传播: {total_forwards}")
            print(f"  - FPP激活次数: {fpp_forwards}")
            print(f"  - TPP激活次数: {tpp_forwards}")
            
            if total_forwards > 0:
                print("✓ 增强模块在前向传播中被激活")
            else:
                print("⚠️ 增强模块未被激活")
        
    except Exception as e:
        print(f"✗ 前向传播失败: {e}")
        return False
    
    # 测试4: 验证增强损失计算
    print("\n4. 测试增强损失计算...")
    try:
        predictions = torch.randn(2, 2)
        targets = torch.randint(0, 2, (2,))
        
        enhanced_loss = model.compute_enhanced_loss(predictions, targets)
        
        if enhanced_loss is not None:
            print(f"✓ 增强损失计算成功: {enhanced_loss.item():.4f}")
            
            # 获取损失组件
            loss_components = model.get_loss_components(predictions, targets)
            if loss_components:
                print(f"  - 损失组件: {list(loss_components.keys())}")
                for key, value in loss_components.items():
                    if isinstance(value, float):
                        print(f"    {key}: {value:.4f}")
        else:
            print("⚠️ 增强损失返回None（可能在某些条件下正常）")
        
    except Exception as e:
        print(f"✗ 增强损失计算失败: {e}")
        return False
    
    print("\n" + "="*60)
    print("✅ 配置修复验证完成！")
    print("增强模块现在应该能够正确初始化和工作。")
    print("="*60)
    
    return True


def test_parameter_comparison():
    """测试参数数量对比"""
    print("\n参数数量对比测试...")
    
    model_config = MockModelConfig()
    
    # 加载配置
    with open('pulsar_trainer/config/coatnet_config.yaml', 'r', encoding='utf-8') as f:
        config_dict = yaml.safe_load(f)
    enhancement_config = config_dict.get('enhancement_modules', None)
    
    # 创建基础模型和增强模型
    model_base = create_coatnet_from_config(model_config, None)
    model_enhanced = create_coatnet_from_config(model_config, enhancement_config)
    
    # 计算参数数量
    base_params = sum(p.numel() for p in model_base.parameters() if p.requires_grad)
    enhanced_params = sum(p.numel() for p in model_enhanced.parameters() if p.requires_grad)
    
    overhead = enhanced_params - base_params
    relative_overhead = overhead / base_params * 100
    
    print(f"基础模型参数: {base_params:,}")
    print(f"增强模型参数: {enhanced_params:,}")
    print(f"增强开销: {overhead:,} 参数 ({relative_overhead:.2f}%)")
    
    return True


if __name__ == '__main__':
    print("配置键名修复验证测试")
    print("修复YAML配置中的键名不匹配问题")
    
    success = test_config_fix()
    
    if success:
        test_parameter_comparison()
        print("\n🎉 配置修复成功！")
        print("现在可以开始训练增强版CoAtNet了！")
    else:
        print("\n❌ 配置修复失败！")
        print("请检查配置文件和模块初始化代码。")
