"""
Data transformation utilities for CoAtNet pulsar classification.

This module provides simplified channel transformation for single-channel architecture.
All data augmentation is handled by the separate DataAugmentation module.

Version 3.0.0 - Standardized single-channel architecture
"""

import numpy as np
from abc import ABC, abstractmethod
import logging


class BaseTransformer(ABC):
    """Abstract base class for data transformers."""

    @abstractmethod
    def transform(self, data: np.ndarray, modality: str) -> np.ndarray:
        """Transform data according to the strategy."""
        pass


class ChannelTransformer(BaseTransformer):
    """简化的通道转换器，仅负责通道格式转换"""

    def __init__(self, keep_single_channel: bool = True):
        """
        初始化通道转换器

        Args:
            keep_single_channel: 是否保持单通道格式
        """
        self.keep_single_channel = keep_single_channel
        self.logger = logging.getLogger(__name__)

    def transform(self, data: np.ndarray, modality: str) -> np.ndarray:
        """
        转换数据格式

        Args:
            data: 输入数据 (64, 64, 1) 或 (64, 64)
            modality: 数据模态（未使用，保持接口兼容）

        Returns:
            转换后的数据 (1, 64, 64)
        """
        # 确保数据是2D
        if data.ndim == 3:
            data = data.squeeze()
        elif data.ndim != 2:
            raise ValueError(f"Expected 2D or 3D data, got {data.ndim}D")

        # 验证形状
        if data.shape != (64, 64):
            raise ValueError(f"Expected shape (64, 64), got {data.shape}")

        # 返回单通道格式
        return np.expand_dims(data, axis=0).astype(np.float32)




class TransformerFactory:
    """Factory for creating data transformers."""

    _transformers = {
        'channel': ChannelTransformer
    }

    @classmethod
    def register(cls, name: str, transformer_class: type):
        """Register a new transformer class."""
        cls._transformers[name] = transformer_class

    @classmethod
    def create(cls, name: str, **kwargs) -> BaseTransformer:
        """Create a transformer instance."""
        if name not in cls._transformers:
            raise ValueError(f"Unknown transformer: {name}. Available: {list(cls._transformers.keys())}")

        return cls._transformers[name](**kwargs)

    @classmethod
    def get_available_transformers(cls) -> list:
        """Get list of available transformers."""
        return list(cls._transformers.keys())
