#!/usr/bin/env python3
"""
Fix dataset file naming to match original format.
"""

import shutil
from pathlib import Path
import json
import random

class DatasetNamingFixer:
    def __init__(self, data_root: str, backup_root: str):
        """
        Initialize dataset naming fixer.
        
        Args:
            data_root: Root directory of dataset to fix
            backup_root: Root directory of original backup for reference
        """
        self.data_root = Path(data_root)
        self.backup_root = Path(backup_root)
    
    def get_original_filenames(self, modality: str, split: str, label: str):
        """Get original filenames from backup for reference."""
        backup_split_path = self.backup_root / modality / split
        
        if label == 'positive':
            pattern = '*positive*.npy'
        else:
            pattern = '*negative*.npy'
        
        original_files = list(backup_split_path.glob(pattern))
        return [f.name for f in original_files]
    
    def fix_split_naming(self, modality: str, split: str):
        """Fix naming for a specific split."""
        split_path = self.data_root / modality / split
        
        if not split_path.exists():
            print(f"⚠️ Split directory does not exist: {split_path}")
            return False
        
        # Get current files
        positive_files = list(split_path.glob(f'{split}_positive_*.npy'))
        negative_files = list(split_path.glob(f'{split}_negative_*.npy'))
        
        print(f"📁 处理 {modality}/{split}:")
        print(f"   正样本文件: {len(positive_files)}")
        print(f"   负样本文件: {len(negative_files)}")
        
        # Get original filename patterns for reference
        original_positive = self.get_original_filenames(modality, split, 'positive')
        original_negative = self.get_original_filenames(modality, split, 'negative')
        
        # Shuffle to randomize assignment
        random.shuffle(original_positive)
        random.shuffle(original_negative)
        
        # Rename positive files
        for i, current_file in enumerate(positive_files):
            if i < len(original_positive):
                new_name = original_positive[i]
            else:
                # Generate pulsar-style name if we run out of original names
                new_name = f"pulsar_{1000 + i:04d}_positive.npy"
            
            new_path = split_path / new_name
            
            # Avoid conflicts
            counter = 1
            while new_path.exists() and new_path != current_file:
                base_name = new_name.replace('.npy', '')
                new_name = f"{base_name}_{counter}.npy"
                new_path = split_path / new_name
                counter += 1
            
            if current_file != new_path:
                shutil.move(current_file, new_path)
                print(f"   ✅ {current_file.name} -> {new_name}")
        
        # Rename negative files
        for i, current_file in enumerate(negative_files):
            if i < len(original_negative):
                new_name = original_negative[i]
            else:
                # Generate cand-style name if we run out of original names
                new_name = f"cand_{100000 + i:06d}_negative.npy"
            
            new_path = split_path / new_name
            
            # Avoid conflicts
            counter = 1
            while new_path.exists() and new_path != current_file:
                base_name = new_name.replace('.npy', '')
                new_name = f"{base_name}_{counter}.npy"
                new_path = split_path / new_name
                counter += 1
            
            if current_file != new_path:
                shutil.move(current_file, new_path)
                print(f"   ✅ {current_file.name} -> {new_name}")
        
        return True
    
    def fix_modality_naming(self, modality: str):
        """Fix naming for all splits in a modality."""
        print(f"\n🔧 修正 {modality} 模态文件命名...")
        
        success = True
        for split in ['train', 'validation', 'test']:
            if not self.fix_split_naming(modality, split):
                success = False
        
        return success
    
    def fix_all_naming(self):
        """Fix naming for all modalities."""
        print("🚀 开始修正数据集文件命名...")
        
        # Set random seed for reproducible filename assignment
        random.seed(42)
        
        results = {}
        
        for modality in ['FPP', 'TPP']:
            try:
                success = self.fix_modality_naming(modality)
                results[modality] = {
                    'success': success,
                    'message': 'Naming fixed successfully' if success else 'Some issues encountered'
                }
            except Exception as e:
                results[modality] = {
                    'success': False,
                    'error': str(e)
                }
                print(f"❌ {modality} 命名修正失败: {e}")
        
        return results
    
    def verify_naming(self):
        """Verify that naming matches original patterns."""
        print("\n🔍 验证文件命名格式...")
        
        verification = {
            'overall_success': True,
            'modalities': {}
        }
        
        for modality in ['FPP', 'TPP']:
            modality_verification = {
                'success': True,
                'splits': {},
                'issues': []
            }
            
            for split in ['train', 'validation', 'test']:
                split_path = self.data_root / modality / split
                
                if not split_path.exists():
                    continue
                
                # Check file patterns
                positive_files = list(split_path.glob('pulsar_*_positive.npy'))
                negative_files = list(split_path.glob('cand_*_negative.npy'))
                other_files = [f for f in split_path.glob('*.npy') 
                              if not (f.name.startswith('pulsar_') and f.name.endswith('_positive.npy')) 
                              and not (f.name.startswith('cand_') and f.name.endswith('_negative.npy'))]
                
                split_verification = {
                    'positive_correct': len(positive_files),
                    'negative_correct': len(negative_files),
                    'incorrect_naming': len(other_files),
                    'total_files': len(list(split_path.glob('*.npy')))
                }
                
                if other_files:
                    issue = f"{modality}/{split}: {len(other_files)} files with incorrect naming"
                    modality_verification['issues'].append(issue)
                    modality_verification['success'] = False
                    print(f"⚠️ {issue}")
                    for f in other_files[:5]:  # Show first 5 examples
                        print(f"     例如: {f.name}")
                
                modality_verification['splits'][split] = split_verification
            
            verification['modalities'][modality] = modality_verification
            if not modality_verification['success']:
                verification['overall_success'] = False
        
        return verification

if __name__ == "__main__":
    data_root = "D:/pulsarSuanfa/datasets/HTRU"
    backup_root = "D:/pulsarSuanfa/datasets/HTRU_backup_original"
    
    fixer = DatasetNamingFixer(data_root, backup_root)
    
    # Fix naming
    results = fixer.fix_all_naming()
    
    # Save results
    with open("naming_fix_results.json", "w") as f:
        json.dump(results, f, indent=2)
    
    print("\n📊 命名修正结果:")
    print(json.dumps(results, indent=2))
    
    # Verify naming
    verification = fixer.verify_naming()
    
    # Save verification
    with open("naming_verification.json", "w") as f:
        json.dump(verification, f, indent=2)
    
    if verification['overall_success']:
        print("\n🎉 文件命名修正完成且验证通过！")
    else:
        print("\n⚠️ 文件命名修正完成，但存在一些问题需要检查。")
    
    print(f"\n📋 验证结果已保存至: naming_verification.json")
