"""
SimplifiedFreqFusion - 简化版频域感知特征融合模块
基于原始FreqFusion简化，专门用于脉冲星分类中的FPP数据处理

原始论文: Frequency-aware Feature Fusion for Dense Image Prediction (TPAMI 2024)
简化用途: 脉冲星FPP数据的频域特征融合，移除复杂依赖
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import warnings


def normal_init(module, mean=0, std=1, bias=0):
    """标准正态分布初始化"""
    if hasattr(module, 'weight') and module.weight is not None:
        nn.init.normal_(module.weight, mean, std)
    if hasattr(module, 'bias') and module.bias is not None:
        nn.init.constant_(module.bias, bias)


def xavier_init(module, distribution='uniform'):
    """Xavier初始化"""
    if hasattr(module, 'weight') and module.weight is not None:
        if distribution == 'uniform':
            nn.init.xavier_uniform_(module.weight)
        else:
            nn.init.xavier_normal_(module.weight)


def hamming2D(M, N):
    """
    生成二维Hamming窗
    
    参数：
    - M：窗口的行数
    - N：窗口的列数
    
    返回：
    - 二维Hamming窗
    """
    hamming_x = np.hamming(M)
    hamming_y = np.hamming(N)
    hamming_2d = np.outer(hamming_x, hamming_y)
    return hamming_2d


class SimplifiedFreqFusion(nn.Module):
    """
    简化版频域感知特征融合模块
    
    专门适配脉冲星FPP数据的频域特征融合：
    - 移除CARAFE等复杂依赖，使用标准PyTorch操作
    - 保留核心的高通和低通滤波逻辑
    - 简化特征融合流程
    - 适配脉冲星信号的频域特征
    
    Args:
        hr_channels: 高分辨率特征通道数
        lr_channels: 低分辨率特征通道数（通常与hr_channels相同）
        lowpass_kernel: 低通滤波器核大小
        highpass_kernel: 高通滤波器核大小
        compressed_channels: 压缩后的通道数
        use_high_pass: 是否使用高通滤波
        use_low_pass: 是否使用低通滤波
        hamming_window: 是否使用Hamming窗
        
    Input:
        hr_feat: [B, C, H, W] - 高分辨率特征
        lr_feat: [B, C, H, W] - 低分辨率特征（可以与hr_feat相同）
        
    Output:
        fused_feat: [B, C, H, W] - 融合后的特征
    """
    
    def __init__(self,
                 hr_channels,
                 lr_channels,
                 lowpass_kernel=5,
                 highpass_kernel=3,
                 compressed_channels=64,
                 use_high_pass=True,
                 use_low_pass=True,
                 hamming_window=True):
        super().__init__()
        
        self.lowpass_kernel = lowpass_kernel
        self.highpass_kernel = highpass_kernel
        self.compressed_channels = compressed_channels
        self.use_high_pass = use_high_pass
        self.use_low_pass = use_low_pass
        self.hamming_window = hamming_window
        
        # 通道压缩器
        self.hr_channel_compressor = nn.Conv2d(hr_channels, compressed_channels, 1)
        self.lr_channel_compressor = nn.Conv2d(lr_channels, compressed_channels, 1)
        
        # 低通滤波器生成器
        if self.use_low_pass:
            self.lowpass_encoder = nn.Conv2d(
                compressed_channels,
                compressed_channels,
                kernel_size=3,
                padding=1,
                groups=1
            )
        
        # 高通滤波器生成器
        if self.use_high_pass:
            self.highpass_encoder = nn.Conv2d(
                compressed_channels,
                compressed_channels,
                kernel_size=3,
                padding=1,
                groups=1
            )
        
        # 特征融合器
        self.feature_fusion = nn.Conv2d(
            compressed_channels * 2 if use_high_pass and use_low_pass else compressed_channels,
            hr_channels,
            kernel_size=1
        )
        
        # Hamming窗
        if self.hamming_window:
            self.register_buffer('hamming_lowpass', 
                               torch.FloatTensor(hamming2D(lowpass_kernel, lowpass_kernel))[None, None,])
            self.register_buffer('hamming_highpass', 
                               torch.FloatTensor(hamming2D(highpass_kernel, highpass_kernel))[None, None,])
        else:
            self.register_buffer('hamming_lowpass', torch.FloatTensor([1.0]))
            self.register_buffer('hamming_highpass', torch.FloatTensor([1.0]))
        
        self.init_weights()
    
    def init_weights(self):
        """权重初始化"""
        for m in self.modules():
            if isinstance(m, nn.Conv2d):
                xavier_init(m, distribution='uniform')
        
        if self.use_low_pass:
            normal_init(self.lowpass_encoder, std=0.001)
        if self.use_high_pass:
            normal_init(self.highpass_encoder, std=0.001)
    
    def apply_frequency_filter(self, feat, filter_type='low'):
        """
        应用频域滤波
        
        Args:
            feat: 输入特征 [B, C, H, W]
            filter_type: 'low' 或 'high'
            
        Returns:
            filtered_feat: 滤波后的特征
        """
        if filter_type == 'low' and self.use_low_pass:
            # 低通滤波：保留低频信息
            filtered = self.lowpass_encoder(feat)
            # 使用平均池化模拟低通效果
            filtered = F.avg_pool2d(filtered, kernel_size=3, stride=1, padding=1)
        elif filter_type == 'high' and self.use_high_pass:
            # 高通滤波：提取高频信息
            filtered = self.highpass_encoder(feat)
            # 高通滤波 = 原始 - 低通
            low_freq = F.avg_pool2d(feat, kernel_size=3, stride=1, padding=1)
            filtered = feat - low_freq + filtered
        else:
            filtered = feat
            
        return filtered
    
    def forward(self, hr_feat, lr_feat=None):
        """
        前向传播
        
        Args:
            hr_feat: [B, C, H, W] - 高分辨率特征
            lr_feat: [B, C, H, W] - 低分辨率特征（可选，默认使用hr_feat）
            
        Returns:
            fused_feat: [B, C, H, W] - 融合后的特征
        """
        # 如果没有提供lr_feat，使用hr_feat
        if lr_feat is None:
            lr_feat = hr_feat
        
        # 输入验证
        assert hr_feat.shape == lr_feat.shape, f"hr_feat and lr_feat must have same shape, got {hr_feat.shape} and {lr_feat.shape}"
        
        # 通道压缩
        compressed_hr = self.hr_channel_compressor(hr_feat)
        compressed_lr = self.lr_channel_compressor(lr_feat)
        
        # 特征融合列表
        fusion_features = []
        
        # 低频特征处理
        if self.use_low_pass:
            low_freq_hr = self.apply_frequency_filter(compressed_hr, 'low')
            low_freq_lr = self.apply_frequency_filter(compressed_lr, 'low')
            low_freq_fused = (low_freq_hr + low_freq_lr) / 2
            fusion_features.append(low_freq_fused)
        
        # 高频特征处理
        if self.use_high_pass:
            high_freq_hr = self.apply_frequency_filter(compressed_hr, 'high')
            high_freq_lr = self.apply_frequency_filter(compressed_lr, 'high')
            high_freq_fused = (high_freq_hr + high_freq_lr) / 2
            fusion_features.append(high_freq_fused)
        
        # 如果没有启用任何滤波器，直接融合
        if not fusion_features:
            fusion_features.append((compressed_hr + compressed_lr) / 2)
        
        # 特征拼接和融合
        if len(fusion_features) > 1:
            fused_compressed = torch.cat(fusion_features, dim=1)
        else:
            fused_compressed = fusion_features[0]
        
        # 恢复到原始通道数
        fused_feat = self.feature_fusion(fused_compressed)
        
        # 残差连接
        fused_feat = fused_feat + hr_feat
        
        return fused_feat


# 测试代码
if __name__ == '__main__':
    # 测试简化版FreqFusion
    batch_size = 2
    channels = 256
    height, width = 16, 16
    
    # 创建测试输入
    hr_feat = torch.randn(batch_size, channels, height, width)
    lr_feat = torch.randn(batch_size, channels, height, width)
    
    print(f"HR feature shape: {hr_feat.shape}")
    print(f"LR feature shape: {lr_feat.shape}")
    
    # 初始化模块
    model = SimplifiedFreqFusion(
        hr_channels=channels,
        lr_channels=channels,
        compressed_channels=64,
        use_high_pass=True,
        use_low_pass=True
    )
    
    print(f"Model parameters: {sum(p.numel() for p in model.parameters())}")
    
    # 前向传播测试
    with torch.no_grad():
        output = model(hr_feat, lr_feat)
        print(f"Output shape: {output.shape}")
        print(f"Shape preserved: {hr_feat.shape == output.shape}")
        print("SimplifiedFreqFusion adaptation completed successfully!")
