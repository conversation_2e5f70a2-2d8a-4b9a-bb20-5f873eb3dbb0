"""
测试配置文件的有效性和完整性
"""

import yaml
import sys

def test_config_validation():
    """测试配置文件验证"""
    print("Testing configuration file validation...")
    
    try:
        # 加载配置文件
        with open('pulsar_trainer/config/coatnet_config.yaml', 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        print("✓ YAML configuration file loaded successfully")
        
        # 验证增强模块配置
        enhancement_modules = config.get('enhancement_modules', {})
        if enhancement_modules.get('enabled', False):
            print("✓ Enhancement modules enabled")
            
            # 验证FPP配置
            fpp_config = enhancement_modules.get('fpp', {})
            if fpp_config.get('enabled', False):
                print("✓ FPP enhancement module configured")
                print(f"  - FFT block enabled: {fpp_config.get('fft_block', {}).get('enabled', False)}")
                print(f"  - Freq fusion configured: {bool(fpp_config.get('freq_fusion'))}")
            
            # 验证TPP配置
            tpp_config = enhancement_modules.get('tpp', {})
            if tpp_config.get('enabled', False):
                print("✓ TPP enhancement module configured")
                print(f"  - CBAM configured: {bool(tpp_config.get('cbam'))}")
                print(f"  - CF Loss configured: {bool(tpp_config.get('cf_loss'))}")
        
        # 验证性能目标
        performance_targets = config.get('performance_targets', {})
        print(f"✓ Performance targets configured:")
        print(f"  - Accuracy target: {performance_targets.get('accuracy', 'N/A')}")
        print(f"  - F1 score target: {performance_targets.get('f1_score', 'N/A')}")
        
        # 验证模块控制
        module_control = config.get('module_control', {})
        if module_control:
            print("✓ Module control configured")
            print(f"  - FPP enhancement: {module_control.get('fpp_enhancement', {})}")
            print(f"  - TPP enhancement: {module_control.get('tpp_enhancement', {})}")
        
        # 验证训练策略
        training_strategy = config.get('training_strategy', {})
        if training_strategy:
            print("✓ Training strategy configured")
            print(f"  - Enhanced loss weight: {training_strategy.get('enhanced_loss_weight', 'N/A')}")
            print(f"  - Loss combination: {training_strategy.get('loss_combination', 'N/A')}")
        
        print("\n✅ Configuration file validation completed successfully!")
        return True
        
    except yaml.YAMLError as e:
        print(f"✗ YAML parsing error: {e}")
        return False
    except Exception as e:
        print(f"✗ Configuration validation error: {e}")
        return False

def test_config_completeness():
    """测试配置完整性"""
    print("\nTesting configuration completeness...")
    
    try:
        with open('pulsar_trainer/config/coatnet_config.yaml', 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        required_sections = [
            'project',
            'data', 
            'model',
            'training',
            'enhancement_modules',
            'performance_targets',
            'module_control'
        ]
        
        missing_sections = []
        for section in required_sections:
            if section not in config:
                missing_sections.append(section)
            else:
                print(f"✓ {section} section present")
        
        if missing_sections:
            print(f"✗ Missing sections: {missing_sections}")
            return False
        else:
            print("✅ All required sections present")
            return True
            
    except Exception as e:
        print(f"✗ Completeness check error: {e}")
        return False

if __name__ == '__main__':
    print("="*60)
    print("Configuration File Validation Test")
    print("="*60)
    
    validation_success = test_config_validation()
    completeness_success = test_config_completeness()
    
    if validation_success and completeness_success:
        print("\n🎉 All configuration tests passed!")
        print("Configuration file is ready for use.")
    else:
        print("\n❌ Configuration tests failed!")
        print("Please check and fix the configuration file.")
        sys.exit(1)
