"""
TPP Enhancement Module - TPP误分类解决方案
集成CBAM和CF_Loss_2D，专门解决脉冲星TPP数据的假阳性和假阴性问题

模块组成:
- CBAM: 双重注意力机制，提高特征判别能力
- CF_Loss_2D: 类别不平衡损失函数，解决系统性偏差

目标: 同时减少假阳性和假阴性，提升TPP数据集的所有评估指标

版本: 1.0.0
作者: Pulsar Classification Enhancement Team
日期: 2025-01-31
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Optional, Dict, Any, Tuple


# ============================================================================
# CBAM - 卷积块注意力模块
# ============================================================================

class ChannelAttention(nn.Module):
    """通道注意力模块"""
    
    def __init__(self, in_planes, ratio=16):
        super(ChannelAttention, self).__init__()
        self.avg_pool = nn.AdaptiveAvgPool2d(1)  # 全局平均池化
        self.max_pool = nn.AdaptiveMaxPool2d(1)  # 全局最大池化

        # 使用1x1卷积代替全连接层，减少参数量，ratio用于降维
        self.fc = nn.Sequential(
            nn.Conv2d(in_planes, in_planes // ratio, 1, bias=False),
            nn.ReLU(),
            nn.Conv2d(in_planes // ratio, in_planes, 1, bias=False)
        )
        self.sigmoid = nn.Sigmoid()  # 激活函数

    def forward(self, x):
        """通道注意力前向传播"""
        avg_out = self.fc(self.avg_pool(x))  # 全局平均池化分支
        max_out = self.fc(self.max_pool(x))  # 全局最大池化分支
        out = avg_out + max_out  # 融合两个池化分支
        return self.sigmoid(out)  # 返回通道权重


class SpatialAttention(nn.Module):
    """空间注意力模块"""
    
    def __init__(self, kernel_size=7):
        super(SpatialAttention, self).__init__()
        self.conv1 = nn.Conv2d(2, 1, kernel_size, padding=kernel_size // 2, bias=False)
        self.sigmoid = nn.Sigmoid()

    def forward(self, x):
        """空间注意力前向传播"""
        avg_out = torch.mean(x, dim=1, keepdim=True)  # 计算特征图的平均值
        max_out, _ = torch.max(x, dim=1, keepdim=True)  # 计算特征图的最大值
        x = torch.cat([avg_out, max_out], dim=1)  # 沿通道维度拼接
        x = self.conv1(x)  # 通过卷积层
        return self.sigmoid(x)  # 返回空间权重


class CBAM(nn.Module):
    """卷积块注意力模块 (Convolutional Block Attention Module)"""
    
    def __init__(self, in_planes, ratio=16, kernel_size=7):
        super(CBAM, self).__init__()
        self.channel_attention = ChannelAttention(in_planes, ratio)  # 通道注意力模块
        self.spatial_attention = SpatialAttention(kernel_size)  # 空间注意力模块

    def forward(self, x):
        """CBAM前向传播"""
        # 输入验证
        assert len(x.shape) == 4, f"Input must be 4D tensor [B, C, H, W], got shape {x.shape}"
        
        # 先应用通道注意力
        out = self.channel_attention(x) * x
        
        # 再应用空间注意力
        out = self.spatial_attention(out) * out
        
        return out  # 返回增强后的特征


# ============================================================================
# CF_Loss_2D - 2D二分类的临床相关特征优化损失函数
# ============================================================================

def encode_mask_2d(ground_truth, num_classes=2):
    """将2D标签编码为one-hot格式"""
    if len(ground_truth.shape) > 1:
        ground_truth = ground_truth.squeeze()
    
    batch_size = ground_truth.size(0)
    device = ground_truth.device
    
    one_hot = torch.zeros((batch_size, num_classes), device=device)
    ground_truth = ground_truth.long()
    one_hot = one_hot.scatter_(1, ground_truth.unsqueeze(1), 1)
    
    return one_hot


class CF_Loss_2D(nn.Module):
    """2D二分类的临床相关特征优化损失函数"""
    
    def __init__(self, beta=1.0, alpha=0.5, gamma=0.3):
        super(CF_Loss_2D, self).__init__()
        self.beta = beta
        self.alpha = alpha
        self.gamma = gamma
        
        # 交叉熵损失
        self.CE = nn.CrossEntropyLoss()
        
        # 动态设备检测
        self.register_buffer('dummy', torch.tensor(0.0))
        
        # 特征尺度参数（适配2D）
        self.register_buffer('base_size', torch.tensor(2.0))
        
    def get_device(self):
        """获取当前设备"""
        return self.dummy.device
    
    def compute_feature_difference_loss(self, prediction_softmax, ground_truth_encoded):
        """计算特征差异损失"""
        device = self.get_device()
        batch_size = prediction_softmax.shape[0]
        
        # 计算预测和真实标签的差异
        diff_0 = torch.abs(prediction_softmax[:, 0] - ground_truth_encoded[:, 0])
        diff_1 = torch.abs(prediction_softmax[:, 1] - ground_truth_encoded[:, 1])
        
        # 特征差异损失：鼓励预测概率接近真实标签
        fd_loss = (diff_0.sum() + diff_1.sum()) / (2 * batch_size)
        
        return fd_loss
    
    def compute_volume_difference_loss(self, prediction_softmax, ground_truth_encoded):
        """计算体积差异损失（类别不平衡处理）"""
        batch_size = prediction_softmax.shape[0]
        
        # 计算每个类别的总预测量和真实量
        pred_class_0 = prediction_softmax[:, 0].sum()
        pred_class_1 = prediction_softmax[:, 1].sum()
        
        true_class_0 = ground_truth_encoded[:, 0].sum()
        true_class_1 = ground_truth_encoded[:, 1].sum()
        
        # 体积差异损失：平衡类别分布
        vd_loss = (torch.abs(pred_class_0 - true_class_0) + 
                   torch.abs(pred_class_1 - true_class_1)) / batch_size
        
        return vd_loss
    
    def compute_confidence_penalty(self, prediction_softmax):
        """计算置信度惩罚，减少过度自信的预测"""
        # 计算预测的最大概率（置信度）
        max_probs, _ = torch.max(prediction_softmax, dim=1)
        
        # 对过度自信的预测进行惩罚
        # 当置信度 > 0.95 时开始惩罚
        high_confidence_mask = max_probs > 0.95
        confidence_penalty = torch.mean(max_probs[high_confidence_mask] - 0.95) if high_confidence_mask.any() else torch.tensor(0.0, device=self.get_device())
        
        return confidence_penalty * 0.1  # 较小的权重
    
    def forward(self, prediction, ground_truth):
        """前向传播计算损失"""
        # 输入验证
        assert len(prediction.shape) == 2 and prediction.shape[1] == 2, \
            f"Prediction must be [B, 2], got shape {prediction.shape}"
        
        if len(ground_truth.shape) > 1:
            ground_truth = ground_truth.squeeze()
        
        assert len(ground_truth.shape) == 1, \
            f"Ground truth must be [B], got shape {ground_truth.shape}"
        
        # 编码ground truth为one-hot
        ground_truth_encoded = encode_mask_2d(ground_truth, num_classes=2)
        
        # 计算softmax概率
        prediction_softmax = F.softmax(prediction, dim=1)
        
        # 1. 交叉熵损失
        loss_CE = self.CE(prediction, ground_truth)
        
        # 2. 特征差异损失
        loss_FD = self.compute_feature_difference_loss(prediction_softmax, ground_truth_encoded)
        
        # 3. 体积差异损失（类别不平衡处理）
        loss_VD = self.compute_volume_difference_loss(prediction_softmax, ground_truth_encoded)
        
        # 4. 置信度惩罚（可选，减少过度自信）
        confidence_penalty = self.compute_confidence_penalty(prediction_softmax)
        
        # 综合损失
        loss_value = (self.beta * loss_CE + 
                     self.alpha * loss_FD + 
                     self.gamma * loss_VD + 
                     confidence_penalty)
        
        return loss_value
    
    def get_loss_components(self, prediction, ground_truth):
        """获取各个损失组件的详细信息（用于调试和监控）"""
        with torch.no_grad():
            if len(ground_truth.shape) > 1:
                ground_truth = ground_truth.squeeze()
            
            ground_truth_encoded = encode_mask_2d(ground_truth, num_classes=2)
            prediction_softmax = F.softmax(prediction, dim=1)
            
            loss_CE = self.CE(prediction, ground_truth)
            loss_FD = self.compute_feature_difference_loss(prediction_softmax, ground_truth_encoded)
            loss_VD = self.compute_volume_difference_loss(prediction_softmax, ground_truth_encoded)
            confidence_penalty = self.compute_confidence_penalty(prediction_softmax)
            
            return {
                'ce_loss': loss_CE.item(),
                'fd_loss': loss_FD.item(),
                'vd_loss': loss_VD.item(),
                'confidence_penalty': confidence_penalty.item(),
                'total_loss': (self.beta * loss_CE + self.alpha * loss_FD + 
                              self.gamma * loss_VD + confidence_penalty).item()
            }


# ============================================================================
# TPPEnhancementModule - 统一的TPP增强模块
# ============================================================================

class TPPEnhancementModule(nn.Module):
    """
    TPP增强模块 - 专门解决TPP数据的假阳性和假阴性问题
    
    集成CBAM双重注意力机制和CF_Loss_2D类别不平衡损失函数，
    通过提高特征判别能力和解决系统性偏差来提升所有评估指标。
    
    Args:
        channels: 输入特征通道数
        cbam_config: CBAM模块配置
        loss_config: CF_Loss配置
        enabled: 是否启用模块
    """
    
    def __init__(self, 
                 channels: int,
                 cbam_config: Optional[Dict[str, Any]] = None,
                 loss_config: Optional[Dict[str, Any]] = None,
                 enabled: bool = True):
        super().__init__()
        
        self.channels = channels
        self.enabled = enabled
        
        # CBAM注意力模块配置
        default_cbam_config = {
            'in_planes': channels,
            'ratio': 16,
            'kernel_size': 7
        }
        
        if cbam_config:
            default_cbam_config.update(cbam_config)
        
        self.cbam = CBAM(**default_cbam_config)
        
        # CF_Loss损失函数配置
        default_loss_config = {
            'beta': 1.0,    # CE损失权重
            'alpha': 0.5,   # 特征差异损失权重
            'gamma': 0.3    # 体积差异损失权重
        }
        
        if loss_config:
            default_loss_config.update(loss_config)
        
        self.cf_loss = CF_Loss_2D(**default_loss_config)
        
        # 性能监控
        self.register_buffer('forward_count', torch.tensor(0, dtype=torch.long))
        self.register_buffer('active_count', torch.tensor(0, dtype=torch.long))
        self.register_buffer('loss_count', torch.tensor(0, dtype=torch.long))
    
    def forward(self, x: torch.Tensor, modality: str = 'TPP') -> torch.Tensor:
        """
        前向传播
        
        Args:
            x: 输入特征张量 [B, C, H, W]
            modality: 数据模态，只有'TPP'时才激活
            
        Returns:
            enhanced_x: 注意力增强后的特征张量
        """
        self.forward_count += 1
        
        # 只在TPP模态下激活
        if not self.enabled or modality.upper() != 'TPP':
            return x
        
        self.active_count += 1
        
        # 应用CBAM双重注意力机制
        x_enhanced = self.cbam(x)
        
        return x_enhanced
    
    def compute_loss(self, predictions: torch.Tensor, targets: torch.Tensor) -> torch.Tensor:
        """
        计算增强损失函数
        
        Args:
            predictions: 模型预测logits [B, 2]
            targets: 真实标签 [B] 或 [B, 1]
            
        Returns:
            loss: 综合损失值
        """
        self.loss_count += 1
        return self.cf_loss(predictions, targets)
    
    def get_loss_components(self, predictions: torch.Tensor, targets: torch.Tensor) -> Dict[str, float]:
        """获取损失组件详情"""
        return self.cf_loss.get_loss_components(predictions, targets)
    
    def enable(self):
        """启用模块"""
        self.enabled = True
    
    def disable(self):
        """禁用模块"""
        self.enabled = False
    
    def get_stats(self) -> Dict[str, Any]:
        """获取模块统计信息"""
        total_forwards = self.forward_count.item()
        active_forwards = self.active_count.item()
        loss_computations = self.loss_count.item()
        
        return {
            'enabled': self.enabled,
            'channels': self.channels,
            'total_forwards': total_forwards,
            'active_forwards': active_forwards,
            'loss_computations': loss_computations,
            'activation_rate': active_forwards / max(total_forwards, 1),
            'parameter_count': sum(p.numel() for p in self.parameters() if p.requires_grad)
        }
    
    def reset_stats(self):
        """重置统计信息"""
        self.forward_count.zero_()
        self.active_count.zero_()
        self.loss_count.zero_()


# ============================================================================
# 配置验证和工厂函数
# ============================================================================

def validate_tpp_config(config: Dict[str, Any]) -> bool:
    """验证TPP增强模块配置"""
    required_keys = ['channels']
    
    for key in required_keys:
        if key not in config:
            return False
    
    # 验证数值范围
    if config['channels'] <= 0:
        return False
    
    return True


def create_tpp_enhancement(config: Dict[str, Any]) -> TPPEnhancementModule:
    """创建TPP增强模块"""
    if not validate_tpp_config(config):
        raise ValueError("Invalid TPP enhancement configuration")
    
    return TPPEnhancementModule(**config)


# 测试代码
if __name__ == '__main__':
    print("Testing TPP Enhancement Module...")
    
    # 测试配置
    config = {
        'channels': 512,
        'cbam_config': {
            'ratio': 16,
            'kernel_size': 7
        },
        'loss_config': {
            'beta': 1.0,
            'alpha': 0.5,
            'gamma': 0.3
        },
        'enabled': True
    }
    
    # 创建模块
    tpp_module = create_tpp_enhancement(config)
    print(f"TPP Enhancement Module created with {tpp_module.get_stats()['parameter_count']} parameters")
    
    # 测试前向传播
    batch_size = 2
    channels = 512
    height, width = 8, 8
    
    x = torch.randn(batch_size, channels, height, width)
    
    # TPP模态测试
    output_tpp = tpp_module(x, modality='TPP')
    print(f"TPP mode - Input: {x.shape}, Output: {output_tpp.shape}")
    
    # FPP模态测试（应该直接返回输入）
    output_fpp = tpp_module(x, modality='FPP')
    print(f"FPP mode - Input: {x.shape}, Output: {output_fpp.shape}")
    print(f"FPP mode passthrough: {torch.equal(x, output_fpp)}")
    
    # 测试损失计算
    predictions = torch.randn(batch_size, 2)
    targets = torch.randint(0, 2, (batch_size,))
    
    loss = tpp_module.compute_loss(predictions, targets)
    loss_components = tpp_module.get_loss_components(predictions, targets)
    
    print(f"Loss: {loss.item():.4f}")
    print(f"Loss components: {loss_components}")
    
    # 统计信息
    stats = tpp_module.get_stats()
    print(f"Module stats: {stats}")
    
    print("✅ TPP Enhancement Module test completed successfully!")
