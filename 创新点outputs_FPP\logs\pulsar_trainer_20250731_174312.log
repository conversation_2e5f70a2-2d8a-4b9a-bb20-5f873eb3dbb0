2025-07-31 17:43:12 - pulsar_trainer - INFO - <PERSON><PERSON> initialized. Log file: D:\pulsarSuanfa\outputs_FPP\logs\pulsar_trainer_20250731_174312.log
2025-07-31 17:43:12 - pulsar_trainer - INFO - 随机种子设置为: 42
2025-07-31 17:43:12 - pulsar_trainer - INFO - 使用配置文件参数创建模型
2025-07-31 17:43:12 - pulsar_trainer - INFO - 增强模块配置已加载: FPP=True, TPP=True
2025-07-31 17:43:12 - pulsar_trainer - INFO - 模型配置 - num_blocks: [2, 2, 6, 8, 2]
2025-07-31 17:43:12 - pulsar_trainer - INFO - 模型配置 - channels: [96, 128, 256, 512, 1024]
2025-07-31 17:43:12 - pulsar_trainer - INFO - 模型配置 - in_channels: 1
2025-07-31 17:43:12 - pulsar_trainer - INFO - ✓ 增强模块已启用
2025-07-31 17:43:12 - pulsar_trainer - INFO -   - FPP模块可用: False
2025-07-31 17:43:12 - pulsar_trainer - INFO -   - TPP模块可用: True
2025-07-31 17:43:12 - pulsar_trainer - INFO -   - 当前模态: AUTO
2025-07-31 17:43:12 - pulsar_trainer - INFO - 模型架构详情:
2025-07-31 17:43:12 - pulsar_trainer - INFO -   总参数数量: 38,758,994
2025-07-31 17:43:12 - pulsar_trainer - INFO -   可训练参数: 38,758,994
2025-07-31 17:43:12 - pulsar_trainer - INFO -   模型大小: 147.85 MB
2025-07-31 17:43:12 - pulsar_trainer - INFO -   块类型配置: ['C', 'C', 'T', 'T']
2025-07-31 17:43:12 - pulsar_trainer - INFO -   各阶段块数: [2, 2, 6, 8, 2]
2025-07-31 17:43:12 - pulsar_trainer - INFO -   各阶段通道数: [96, 128, 256, 512, 1024]
2025-07-31 17:43:12 - pulsar_trainer - INFO - Using standard cross entropy loss
2025-07-31 17:43:12 - pulsar_trainer - INFO - 🖥️ 使用GPU: NVIDIA GeForce RTX 4050 Laptop GPU
2025-07-31 17:43:12 - pulsar_trainer - INFO - 💾 GPU内存: 6.4GB
2025-07-31 17:43:12 - pulsar_trainer - INFO - 📋 模型参数总数: 38,758,994
2025-07-31 17:43:12 - pulsar_trainer - INFO - 🏗️ 模型架构: CoAtNet
2025-07-31 17:43:12 - pulsar_trainer - INFO - 📚 数据集大小:
2025-07-31 17:43:12 - pulsar_trainer - INFO -   - 训练集: 1674
2025-07-31 17:43:12 - pulsar_trainer - INFO -   - 验证集: 360
2025-07-31 17:43:12 - pulsar_trainer - INFO -   - 测试集: 358
2025-07-31 17:43:12 - pulsar_trainer - INFO -   - 总计: 2392
2025-07-31 17:43:12 - pulsar_trainer - INFO - ============================================================
2025-07-31 17:43:12 - pulsar_trainer - INFO - 🚀 开始脉冲星分类训练
2025-07-31 17:43:12 - pulsar_trainer - INFO - 📊 模态: FPP
2025-07-31 17:43:12 - pulsar_trainer - INFO - 🧠 模型: coatnet
2025-07-31 17:43:12 - pulsar_trainer - INFO - 📦 批次大小: 32
2025-07-31 17:43:12 - pulsar_trainer - INFO - 🎯 学习率: 0.001
2025-07-31 17:43:12 - pulsar_trainer - INFO - 🔄 训练轮数: 100
2025-07-31 17:43:12 - pulsar_trainer - INFO - ============================================================
2025-07-31 17:43:35 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.6951577067375183, 'fd_loss': 0.4524206817150116, 'vd_loss': 0.3102269172668457, 'confidence_penalty': 0.0, 'total_loss': 1.01443612575531}
2025-07-31 17:44:03 - pulsar_trainer - INFO - Epoch   1: Train Loss=2.8004, Val Loss=0.6912, Train Acc=57.17%, Val Acc=50.00%
2025-07-31 17:44:03 - pulsar_trainer - INFO - Validation F1: 33.3333, Precision: 25.0000, Recall: 50.0000
2025-07-31 17:44:03 - pulsar_trainer - INFO - 模型已保存: D:\pulsarSuanfa\outputs_FPP\models\best_model.pth
2025-07-31 17:44:03 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.7015283703804016, 'fd_loss': 0.5034320950508118, 'vd_loss': 0.17991355061531067, 'confidence_penalty': 0.0, 'total_loss': 1.0072184801101685}
2025-07-31 17:44:08 - pulsar_trainer - INFO - Epoch   2: Train Loss=0.8471, Val Loss=0.6945, Train Acc=49.76%, Val Acc=50.00%
2025-07-31 17:44:08 - pulsar_trainer - INFO - Validation F1: 33.3333, Precision: 25.0000, Recall: 50.0000
2025-07-31 17:44:08 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.6979892253875732, 'fd_loss': 0.5023335218429565, 'vd_loss': 0.2686685025691986, 'confidence_penalty': 0.0, 'total_loss': 1.0297565460205078}
2025-07-31 17:44:12 - pulsar_trainer - INFO - Epoch   3: Train Loss=0.8601, Val Loss=0.8900, Train Acc=47.49%, Val Acc=50.00%
2025-07-31 17:44:12 - pulsar_trainer - INFO - Validation F1: 33.3333, Precision: 25.0000, Recall: 50.0000
2025-07-31 17:44:13 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.8172488808631897, 'fd_loss': 0.4836266040802002, 'vd_loss': 0.46144819259643555, 'confidence_penalty': 0.0, 'total_loss': 1.1974966526031494}
2025-07-31 17:44:17 - pulsar_trainer - INFO - Epoch   4: Train Loss=0.8220, Val Loss=0.7140, Train Acc=49.64%, Val Acc=50.00%
2025-07-31 17:44:17 - pulsar_trainer - INFO - Validation F1: 33.3333, Precision: 25.0000, Recall: 50.0000
2025-07-31 17:44:17 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.7328553199768066, 'fd_loss': 0.5140600204467773, 'vd_loss': 0.33747392892837524, 'confidence_penalty': 0.0, 'total_loss': 1.0911275148391724}
2025-07-31 17:44:22 - pulsar_trainer - INFO - Epoch   5: Train Loss=0.8005, Val Loss=0.7142, Train Acc=50.00%, Val Acc=50.00%
2025-07-31 17:44:22 - pulsar_trainer - INFO - Validation F1: 33.3333, Precision: 25.0000, Recall: 50.0000
2025-07-31 17:44:22 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.7142400145530701, 'fd_loss': 0.49148622155189514, 'vd_loss': 0.20994091033935547, 'confidence_penalty': 0.0, 'total_loss': 1.022965431213379}
2025-07-31 17:44:27 - pulsar_trainer - INFO - Epoch   6: Train Loss=0.7960, Val Loss=0.6991, Train Acc=50.84%, Val Acc=50.00%
2025-07-31 17:44:27 - pulsar_trainer - INFO - Validation F1: 33.3333, Precision: 25.0000, Recall: 50.0000
2025-07-31 17:44:27 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.7346733212471008, 'fd_loss': 0.5117020606994629, 'vd_loss': 0.31223243474960327, 'confidence_penalty': 0.0, 'total_loss': 1.0841940641403198}
2025-07-31 17:44:31 - pulsar_trainer - INFO - Epoch   7: Train Loss=0.7981, Val Loss=0.6912, Train Acc=48.57%, Val Acc=50.00%
2025-07-31 17:44:31 - pulsar_trainer - INFO - Validation F1: 33.3333, Precision: 25.0000, Recall: 50.0000
2025-07-31 17:44:32 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.7159731984138489, 'fd_loss': 0.5106011629104614, 'vd_loss': 0.4315396249294281, 'confidence_penalty': 0.0, 'total_loss': 1.1007356643676758}
2025-07-31 17:44:36 - pulsar_trainer - INFO - Epoch   8: Train Loss=0.7882, Val Loss=0.6965, Train Acc=51.19%, Val Acc=50.00%
2025-07-31 17:44:36 - pulsar_trainer - INFO - Validation F1: 33.3333, Precision: 25.0000, Recall: 50.0000
2025-07-31 17:44:36 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.6757376194000244, 'fd_loss': 0.48457929491996765, 'vd_loss': 0.023012489080429077, 'confidence_penalty': 0.0, 'total_loss': 0.9249310493469238}
2025-07-31 17:44:41 - pulsar_trainer - INFO - Epoch   9: Train Loss=0.7887, Val Loss=0.6922, Train Acc=48.81%, Val Acc=50.00%
2025-07-31 17:44:41 - pulsar_trainer - INFO - Validation F1: 33.3333, Precision: 25.0000, Recall: 50.0000
2025-07-31 17:44:41 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.690990686416626, 'fd_loss': 0.498835027217865, 'vd_loss': 0.1063600480556488, 'confidence_penalty': 0.0, 'total_loss': 0.9723162651062012}
2025-07-31 17:44:46 - pulsar_trainer - INFO - Epoch  10: Train Loss=0.8077, Val Loss=0.8790, Train Acc=47.85%, Val Acc=50.00%
2025-07-31 17:44:46 - pulsar_trainer - INFO - Validation F1: 33.3333, Precision: 25.0000, Recall: 50.0000
2025-07-31 17:44:46 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.9492945075035095, 'fd_loss': 0.5478140115737915, 'vd_loss': 0.6975163221359253, 'confidence_penalty': 0.0, 'total_loss': 1.432456374168396}
2025-07-31 17:44:51 - pulsar_trainer - INFO - Epoch  11: Train Loss=0.9225, Val Loss=0.7087, Train Acc=48.81%, Val Acc=50.00%
2025-07-31 17:44:51 - pulsar_trainer - INFO - Validation F1: 33.3333, Precision: 25.0000, Recall: 50.0000
2025-07-31 17:44:51 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.7164613008499146, 'fd_loss': 0.5077466368675232, 'vd_loss': 0.24894583225250244, 'confidence_penalty': 0.0, 'total_loss': 1.0450184345245361}
2025-07-31 17:44:55 - pulsar_trainer - INFO - Epoch  12: Train Loss=0.8086, Val Loss=0.7168, Train Acc=50.96%, Val Acc=50.00%
2025-07-31 17:44:55 - pulsar_trainer - INFO - Validation F1: 33.3333, Precision: 25.0000, Recall: 50.0000
2025-07-31 17:44:55 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.7270868420600891, 'fd_loss': 0.5101699233055115, 'vd_loss': 0.28771865367889404, 'confidence_penalty': 0.0, 'total_loss': 1.0684874057769775}
2025-07-31 17:45:00 - pulsar_trainer - INFO - Epoch  13: Train Loss=0.9308, Val Loss=0.7813, Train Acc=48.21%, Val Acc=50.00%
2025-07-31 17:45:00 - pulsar_trainer - INFO - Validation F1: 33.3333, Precision: 25.0000, Recall: 50.0000
2025-07-31 17:45:00 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.827018141746521, 'fd_loss': 0.5328141450881958, 'vd_loss': 0.5375179052352905, 'confidence_penalty': 0.0, 'total_loss': 1.2546806335449219}
2025-07-31 17:45:05 - pulsar_trainer - INFO - Epoch  14: Train Loss=0.7917, Val Loss=0.6927, Train Acc=51.67%, Val Acc=50.00%
2025-07-31 17:45:05 - pulsar_trainer - INFO - Validation F1: 33.3333, Precision: 25.0000, Recall: 50.0000
2025-07-31 17:45:05 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.6707163453102112, 'fd_loss': 0.48543232679367065, 'vd_loss': 0.1334584653377533, 'confidence_penalty': 0.0, 'total_loss': 0.95346999168396}
2025-07-31 17:45:10 - pulsar_trainer - INFO - Epoch  15: Train Loss=0.7880, Val Loss=0.6943, Train Acc=49.64%, Val Acc=50.00%
2025-07-31 17:45:10 - pulsar_trainer - INFO - Validation F1: 33.3333, Precision: 25.0000, Recall: 50.0000
2025-07-31 17:45:10 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.6912246346473694, 'fd_loss': 0.49897104501724243, 'vd_loss': 0.10853680968284607, 'confidence_penalty': 0.0, 'total_loss': 0.9732712507247925}
2025-07-31 17:45:14 - pulsar_trainer - INFO - Epoch  16: Train Loss=0.7824, Val Loss=0.6929, Train Acc=49.88%, Val Acc=50.00%
2025-07-31 17:45:14 - pulsar_trainer - INFO - Validation F1: 33.3333, Precision: 25.0000, Recall: 50.0000
2025-07-31 17:45:14 - pulsar_trainer - INFO - 早停触发，在第 16 轮停止训练
2025-07-31 17:45:14 - pulsar_trainer - INFO - ============================================================
2025-07-31 17:45:14 - pulsar_trainer - INFO - ✅ 训练完成
2025-07-31 17:45:14 - pulsar_trainer - INFO - 🏆 最佳验证准确率: 50.0000
2025-07-31 17:45:14 - pulsar_trainer - INFO - ⏱️ 总训练时间: 122.47秒
2025-07-31 17:45:14 - pulsar_trainer - INFO - ============================================================
2025-07-31 17:45:14 - pulsar_trainer - INFO - 🔍 开始最终评估...
2025-07-31 17:45:15 - pulsar_trainer - INFO - 模型已加载: D:\pulsarSuanfa\outputs_FPP\models\best_model.pth
2025-07-31 17:45:38 - pulsar_trainer.utils.comprehensive_misclassification_analysis - INFO - 开始全面误分类分析...
2025-07-31 17:45:38 - pulsar_trainer.utils.comprehensive_misclassification_analysis - INFO - 综合误分类分析报告已生成: D:\pulsarSuanfa\outputs_FPP\results\comprehensive_misclassification_analysis\comprehensive_misclassification_report.txt
2025-07-31 17:45:38 - pulsar_trainer.utils.comprehensive_misclassification_analysis - INFO - 全面误分类分析完成。报告保存至: D:\pulsarSuanfa\outputs_FPP\results\comprehensive_misclassification_analysis
2025-07-31 17:45:38 - pulsar_trainer - INFO - 📊 评估结果:
2025-07-31 17:45:38 - pulsar_trainer - INFO -   - accuracy: 0.5000
2025-07-31 17:45:38 - pulsar_trainer - INFO -   - precision: 0.5000
2025-07-31 17:45:38 - pulsar_trainer - INFO -   - recall: 1.0000
2025-07-31 17:45:38 - pulsar_trainer - INFO -   - specificity: 0.0000
2025-07-31 17:45:38 - pulsar_trainer - INFO -   - f1_score: 0.6667
2025-07-31 17:45:38 - pulsar_trainer - INFO -   - false_positive_rate: 1.0000
2025-07-31 17:45:38 - pulsar_trainer - INFO - 📊 增强模块统计信息:
2025-07-31 17:45:38 - pulsar_trainer - INFO -   - 总前向传播次数: 231
2025-07-31 17:45:38 - pulsar_trainer - INFO -   - FPP激活次数: 0
2025-07-31 17:45:38 - pulsar_trainer - INFO -   - TPP激活次数: 77
2025-07-31 17:45:38 - pulsar_trainer - INFO -   - 损失计算次数: 53
2025-07-31 17:45:38 - pulsar_trainer - INFO -   - FPP激活率: 0.00%
2025-07-31 17:45:38 - pulsar_trainer - INFO -   - TPP激活率: 33.33%
2025-07-31 17:45:40 - pulsar_trainer - WARNING - ⚠️ Misclassification analysis failed: 'low_pulsar_prob_count'
2025-07-31 17:45:40 - pulsar_trainer - INFO - 所有结果已保存到: D:\pulsarSuanfa\outputs_FPP\results
2025-07-31 17:45:40 - pulsar_trainer - INFO - 可视化图表已保存到: D:\pulsarSuanfa\outputs_FPP\plots
