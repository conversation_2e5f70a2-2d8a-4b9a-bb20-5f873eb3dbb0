{"project": {"name": "pulsar-coatnet", "version": "3.0.0", "description": "Standardized CoAtNet-based pulsar classification system"}, "data": {"modality": "FPP", "data_root": "D:/pulsarSuanfa/datasets/HTRU", "normalize": true, "num_workers": 4, "pin_memory": true}, "model": {"name": "coatnet", "coatnet": {"name": "coatnet", "num_blocks": [2, 2, 6, 8, 2], "channels": [96, 128, 256, 512, 1024], "block_types": ["C", "C", "T", "T"], "image_size": [64, 64], "in_channels": 1, "num_classes": 2, "dropout": 0.2}}, "augmentation": {"enabled": true, "phase_shift": {"enabled": true, "probability": 0.7, "max_shift": 1.0}, "frequency_shift": {"enabled": true, "probability": 0.3, "max_shift": 0.1}, "time_shift": {"enabled": true, "probability": 0.3, "max_shift": 0.1}, "brightness_scaling": {"enabled": true, "probability": 0.5, "scale_range": [0.8, 1.2]}, "noise": {"enabled": true, "probability": 0.4, "noise_level": 0.01}}, "training": {"batch_size": 32, "epochs": 100, "learning_rate": 0.001, "weight_decay": 0.01, "optimizer": "adamw", "scheduler": {"type": "cosine", "warmup_epochs": 10, "min_lr": 1e-05, "total_epochs": 100}, "early_stopping": {"patience": 15, "min_delta": 0.001}, "loss": {"type": "cross_entropy", "smoothing": 0.1}}, "device": {"use_cuda": true, "device_id": 0, "compile_model": false, "benchmark": true, "deterministic": false}, "output": {"base_dir": "outputs", "save_best_model": true, "save_last_model": true, "save_optimizer": true, "log_interval": 10, "eval_interval": 1}, "logging": {"level": "INFO", "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s", "save_to_file": true, "console_output": true}, "evaluation": {"metrics": ["accuracy", "precision", "recall", "f1_score", "auc_roc", "confusion_matrix"], "save_predictions": true, "save_probabilities": true, "generate_plots": true, "misclassification_analysis": true}, "enhancement_modules": {"enabled": true, "modality_aware": true, "fpp": {"enabled": true, "freq_fusion_config": {"lowpass_kernel": 5, "highpass_kernel": 3, "compressed_channels": 24, "use_high_pass": true, "use_low_pass": true, "hamming_window": true}, "channels": 256, "fft_dim": 256}, "tpp": {"enabled": true, "cbam_config": {"ratio": 16, "kernel_size": 7}, "loss_config": {"beta": 1.0, "alpha": 0.5, "gamma": 0.3}, "channels": 512}}, "performance_targets": {"accuracy": 1.0, "precision": 1.0, "recall": 1.0, "f1_score": 1.0, "fpp_specific": {"maintain_recall": 1.0, "improve_precision": 0.99}, "tpp_specific": {"improve_all_metrics": 0.99}}, "module_control": {"fpp_enhancement": {"fft_processing": true, "freq_fusion": true, "stage_s3_only": true}, "tpp_enhancement": {"cbam_attention": true, "enhanced_loss": true, "stage_s4_only": true}, "fallback_mode": false, "performance_monitoring": true, "auto_channel_config": true}, "training_strategy": {"enhanced_loss_weight": 0.3, "standard_loss_weight": 0.7, "loss_combination": "weighted", "enhancement_warmup_epochs": 5}, "validation": {"enhanced_metrics": true, "loss_component_tracking": true, "modality_specific_eval": true}, "debug": {"enhancement_stats": true, "channel_mismatch_check": true, "forward_pass_timing": false, "memory_usage_tracking": false}}