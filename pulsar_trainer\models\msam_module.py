"""
多尺度注意力模块 (Multi-Scale Attention Module, MSAM)
基于CBAM成功经验和PyTorch Image Models最佳实践
专门针对FPP问题的细粒度特征判别设计
"""

import torch
import torch.nn as nn
import torch.nn.functional as F


class ChannelAttention(nn.Module):
    """
    通道注意力模块
    基于PyTorch Image Models的SimpleChannelAttention实现
    """
    def __init__(self, channels, reduction_ratio=16):
        super().__init__()
        self.avg_pool = nn.AdaptiveAvgPool2d(1)
        self.max_pool = nn.AdaptiveMaxPool2d(1)
        
        # 共享的MLP
        self.shared_mlp = nn.Sequential(
            nn.Conv2d(channels, channels // reduction_ratio, 1, bias=False),
            nn.ReLU(inplace=True),
            nn.Conv2d(channels // reduction_ratio, channels, 1, bias=False)
        )
        
        self.sigmoid = nn.Sigmoid()
        
    def forward(self, x):
        # 平均池化和最大池化
        avg_out = self.shared_mlp(self.avg_pool(x))
        max_out = self.shared_mlp(self.max_pool(x))
        
        # 融合并应用sigmoid
        attention = self.sigmoid(avg_out + max_out)
        return x * attention


class SpatialAttention(nn.Module):
    """
    空间注意力模块
    基于CBAM的空间注意力设计
    """
    def __init__(self, kernel_size=7):
        super().__init__()
        self.conv = nn.Conv2d(2, 1, kernel_size, padding=kernel_size//2, bias=False)
        self.sigmoid = nn.Sigmoid()
        
    def forward(self, x):
        # 通道维度的平均和最大
        avg_out = torch.mean(x, dim=1, keepdim=True)
        max_out, _ = torch.max(x, dim=1, keepdim=True)
        
        # 拼接并卷积
        concat = torch.cat([avg_out, max_out], dim=1)
        attention = self.sigmoid(self.conv(concat))
        return x * attention


class MultiScaleFusion(nn.Module):
    """
    多尺度特征融合模块
    使用不同膨胀率的卷积捕捉多尺度特征
    """
    def __init__(self, channels, scales=[1, 2, 4]):
        super().__init__()
        self.scales = scales
        
        # 多尺度卷积分支
        self.multi_scale_convs = nn.ModuleList([
            nn.Sequential(
                nn.Conv2d(channels, channels, 3, padding=scale, dilation=scale, bias=False),
                nn.BatchNorm2d(channels),
                nn.ReLU(inplace=True)
            )
            for scale in scales
        ])
        
        # 特征融合
        self.fusion_conv = nn.Sequential(
            nn.Conv2d(channels * len(scales), channels, 1, bias=False),
            nn.BatchNorm2d(channels),
            nn.ReLU(inplace=True)
        )
        
    def forward(self, x):
        # 多尺度特征提取
        multi_scale_features = []
        for conv in self.multi_scale_convs:
            multi_scale_features.append(conv(x))
        
        # 特征拼接和融合
        fused = torch.cat(multi_scale_features, dim=1)
        output = self.fusion_conv(fused)
        
        return output


class MultiScaleAttentionModule(nn.Module):
    """
    多尺度注意力模块 (MSAM)
    结合通道注意力、空间注意力和多尺度融合
    专门针对FPP问题的精细特征判别
    """
    def __init__(self, channels, reduction_ratio=16, spatial_kernel=7, scales=[1, 2, 4]):
        super().__init__()
        
        # 通道注意力
        self.channel_attention = ChannelAttention(channels, reduction_ratio)
        
        # 空间注意力
        self.spatial_attention = SpatialAttention(spatial_kernel)
        
        # 多尺度融合
        self.multi_scale_fusion = MultiScaleFusion(channels, scales)
        
        # 最终的特征增强
        self.feature_enhancement = nn.Sequential(
            nn.Conv2d(channels, channels, 1, bias=False),
            nn.BatchNorm2d(channels),
            nn.ReLU(inplace=True)
        )
        
        # 残差连接的权重
        self.residual_weight = nn.Parameter(torch.ones(1))
        
    def forward(self, x):
        # 保存输入用于残差连接
        identity = x
        
        # 通道注意力增强
        x = self.channel_attention(x)
        
        # 空间注意力增强
        x = self.spatial_attention(x)
        
        # 多尺度特征融合
        x = self.multi_scale_fusion(x)
        
        # 最终特征增强
        x = self.feature_enhancement(x)
        
        # 残差连接
        output = x + self.residual_weight * identity
        
        return output


def test_msam():
    """测试MSAM模块"""
    print("测试多尺度注意力模块 (MSAM)...")
    
    # 创建测试输入 (s3阶段：256通道)
    batch_size, channels, height, width = 2, 256, 8, 8
    x = torch.randn(batch_size, channels, height, width)
    
    # 创建MSAM模块
    msam = MultiScaleAttentionModule(channels)
    
    # 前向传播
    with torch.no_grad():
        output = msam(x)
    
    # 验证输出形状
    assert output.shape == x.shape, f"输出形状不匹配: {output.shape} vs {x.shape}"
    
    # 计算参数数量
    total_params = sum(p.numel() for p in msam.parameters())
    trainable_params = sum(p.numel() for p in msam.parameters() if p.requires_grad)
    
    print(f"✅ MSAM模块测试通过")
    print(f"   输入形状: {x.shape}")
    print(f"   输出形状: {output.shape}")
    print(f"   总参数数: {total_params:,}")
    print(f"   可训练参数: {trainable_params:,}")
    
    return True


if __name__ == '__main__':
    test_msam()
