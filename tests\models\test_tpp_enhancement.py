"""
TPP Enhancement Module Unit Tests
测试CBAM和CF_Loss_2D的功能
"""

import torch
import torch.nn as nn
import unittest
import sys
import os

# 添加路径以便导入模块
sys.path.append('pulsar_trainer/models')

from tpp_enhancement_module import (
    CBAM,
    ChannelAttention,
    SpatialAttention,
    CF_Loss_2D,
    TPPEnhancementModule,
    create_tpp_enhancement,
    validate_tpp_config
)


class TestChannelAttention(unittest.TestCase):
    """通道注意力模块测试"""
    
    def setUp(self):
        """测试初始化"""
        self.batch_size = 2
        self.channels = 192
        self.height = 8
        self.width = 8
    
    def test_attention_weights_shape(self):
        """测试注意力权重形状"""
        model = ChannelAttention(in_planes=self.channels, ratio=16)
        x = torch.randn(self.batch_size, self.channels, self.height, self.width)
        
        with torch.no_grad():
            attention_weights = model(x)
        
        expected_shape = (self.batch_size, self.channels, 1, 1)
        self.assertEqual(attention_weights.shape, expected_shape)
        print(f"✓ ChannelAttention weights shape test passed: {attention_weights.shape}")
    
    def test_attention_weights_range(self):
        """测试注意力权重范围"""
        model = ChannelAttention(in_planes=self.channels, ratio=16)
        x = torch.randn(self.batch_size, self.channels, self.height, self.width)
        
        with torch.no_grad():
            attention_weights = model(x)
        
        # 权重应该在[0, 1]范围内
        self.assertTrue(torch.all(attention_weights >= 0), "Attention weights should be >= 0")
        self.assertTrue(torch.all(attention_weights <= 1), "Attention weights should be <= 1")
        print("✓ ChannelAttention weights range test passed")


class TestSpatialAttention(unittest.TestCase):
    """空间注意力模块测试"""
    
    def setUp(self):
        """测试初始化"""
        self.batch_size = 2
        self.channels = 192
        self.height = 8
        self.width = 8
    
    def test_attention_weights_shape(self):
        """测试注意力权重形状"""
        model = SpatialAttention(kernel_size=7)
        x = torch.randn(self.batch_size, self.channels, self.height, self.width)
        
        with torch.no_grad():
            attention_weights = model(x)
        
        expected_shape = (self.batch_size, 1, self.height, self.width)
        self.assertEqual(attention_weights.shape, expected_shape)
        print(f"✓ SpatialAttention weights shape test passed: {attention_weights.shape}")
    
    def test_attention_weights_range(self):
        """测试注意力权重范围"""
        model = SpatialAttention(kernel_size=7)
        x = torch.randn(self.batch_size, self.channels, self.height, self.width)
        
        with torch.no_grad():
            attention_weights = model(x)
        
        # 权重应该在[0, 1]范围内
        self.assertTrue(torch.all(attention_weights >= 0), "Attention weights should be >= 0")
        self.assertTrue(torch.all(attention_weights <= 1), "Attention weights should be <= 1")
        print("✓ SpatialAttention weights range test passed")


class TestCBAM(unittest.TestCase):
    """CBAM模块测试"""
    
    def setUp(self):
        """测试初始化"""
        self.batch_size = 2
        self.channels = 192
        self.height = 8
        self.width = 8
    
    def test_input_output_shape(self):
        """测试输入输出形状"""
        model = CBAM(in_planes=self.channels, ratio=16, kernel_size=7)
        x = torch.randn(self.batch_size, self.channels, self.height, self.width)
        
        with torch.no_grad():
            output = model(x)
        
        self.assertEqual(output.shape, x.shape, "CBAM should preserve input shape")
        print(f"✓ CBAM shape preservation test passed: {x.shape} -> {output.shape}")
    
    def test_attention_enhancement(self):
        """测试注意力增强效果"""
        model = CBAM(in_planes=self.channels, ratio=16, kernel_size=7)
        x = torch.randn(self.batch_size, self.channels, self.height, self.width)
        
        with torch.no_grad():
            output = model(x)
        
        # 输出应该与输入不同（经过注意力增强）
        self.assertFalse(torch.allclose(x, output, atol=1e-6), "CBAM should enhance features")
        print("✓ CBAM attention enhancement test passed")
    
    def test_parameter_count(self):
        """测试参数数量（轻量级设计）"""
        model = CBAM(in_planes=self.channels, ratio=16, kernel_size=7)
        param_count = sum(p.numel() for p in model.parameters() if p.requires_grad)
        
        # CBAM应该是轻量级的
        expected_max_params = self.channels * 100  # 经验阈值
        self.assertLess(param_count, expected_max_params, "CBAM should be lightweight")
        print(f"✓ CBAM parameter count test passed: {param_count} parameters")


class TestCF_Loss_2D(unittest.TestCase):
    """CF_Loss_2D模块测试"""
    
    def setUp(self):
        """测试初始化"""
        self.batch_size = 32
        self.num_classes = 2
    
    def test_loss_computation(self):
        """测试损失计算"""
        loss_fn = CF_Loss_2D(beta=1.0, alpha=0.5, gamma=0.3)
        predictions = torch.randn(self.batch_size, self.num_classes)
        targets = torch.randint(0, self.num_classes, (self.batch_size,))
        
        loss = loss_fn(predictions, targets)
        
        self.assertIsInstance(loss, torch.Tensor, "Loss should be a tensor")
        self.assertEqual(loss.dim(), 0, "Loss should be a scalar")
        self.assertGreater(loss.item(), 0, "Loss should be positive")
        print(f"✓ CF_Loss_2D computation test passed: loss = {loss.item():.4f}")
    
    def test_gradient_computation(self):
        """测试梯度计算"""
        loss_fn = CF_Loss_2D(beta=1.0, alpha=0.5, gamma=0.3)
        predictions = torch.randn(self.batch_size, self.num_classes, requires_grad=True)
        targets = torch.randint(0, self.num_classes, (self.batch_size,))
        
        loss = loss_fn(predictions, targets)
        loss.backward()
        
        self.assertIsNotNone(predictions.grad, "Gradients should be computed")
        self.assertFalse(torch.isnan(predictions.grad).any(), "Gradients should not contain NaN")
        self.assertFalse(torch.isinf(predictions.grad).any(), "Gradients should not contain Inf")
        print("✓ CF_Loss_2D gradient computation test passed")
    
    def test_loss_components(self):
        """测试损失组件"""
        loss_fn = CF_Loss_2D(beta=1.0, alpha=0.5, gamma=0.3)
        predictions = torch.randn(self.batch_size, self.num_classes)
        targets = torch.randint(0, self.num_classes, (self.batch_size,))
        
        components = loss_fn.get_loss_components(predictions, targets)
        
        required_components = ['ce_loss', 'fd_loss', 'vd_loss', 'confidence_penalty', 'total_loss']
        for component in required_components:
            self.assertIn(component, components, f"Missing component: {component}")
            self.assertIsInstance(components[component], float, f"Component {component} should be float")
        
        print(f"✓ CF_Loss_2D components test passed: {list(components.keys())}")
    
    def test_class_imbalance_handling(self):
        """测试类别不平衡处理"""
        loss_fn = CF_Loss_2D(beta=1.0, alpha=0.5, gamma=0.3)
        
        # 创建不平衡数据集
        imbalanced_targets = torch.cat([
            torch.zeros(self.batch_size // 4, dtype=torch.long),  # 25% class 0
            torch.ones(3 * self.batch_size // 4, dtype=torch.long)  # 75% class 1
        ])
        
        predictions = torch.randn(self.batch_size, self.num_classes)
        
        loss = loss_fn(predictions, imbalanced_targets)
        components = loss_fn.get_loss_components(predictions, imbalanced_targets)
        
        # VD损失应该处理类别不平衡
        self.assertGreater(components['vd_loss'], 0, "VD loss should handle class imbalance")
        print("✓ CF_Loss_2D class imbalance handling test passed")


class TestTPPEnhancementModule(unittest.TestCase):
    """TPPEnhancementModule集成测试"""
    
    def setUp(self):
        """测试初始化"""
        self.batch_size = 2
        self.channels = 192
        self.height = 8
        self.width = 8
    
    def test_module_creation(self):
        """测试模块创建"""
        config = {
            'channels': self.channels,
            'cbam_config': {
                'ratio': 16,
                'kernel_size': 7
            },
            'loss_config': {
                'beta': 1.0,
                'alpha': 0.5,
                'gamma': 0.3
            },
            'enabled': True
        }
        
        module = create_tpp_enhancement(config)
        self.assertIsInstance(module, TPPEnhancementModule)
        print("✓ TPPEnhancementModule creation test passed")
    
    def test_modality_aware_activation(self):
        """测试模态感知激活"""
        config = {
            'channels': self.channels,
            'enabled': True
        }
        
        module = create_tpp_enhancement(config)
        x = torch.randn(self.batch_size, self.channels, self.height, self.width)
        
        # TPP模态应该激活
        with torch.no_grad():
            output_tpp = module(x, modality='TPP')
        self.assertFalse(torch.equal(x, output_tpp), "TPP modality should process input")
        
        # FPP模态应该直接返回
        with torch.no_grad():
            output_fpp = module(x, modality='FPP')
        self.assertTrue(torch.equal(x, output_fpp), "FPP modality should return input unchanged")
        
        print("✓ TPPEnhancementModule modality awareness test passed")
    
    def test_loss_computation(self):
        """测试损失计算"""
        config = {
            'channels': self.channels,
            'enabled': True
        }
        
        module = create_tpp_enhancement(config)
        predictions = torch.randn(self.batch_size, 2)
        targets = torch.randint(0, 2, (self.batch_size,))
        
        loss = module.compute_loss(predictions, targets)
        
        self.assertIsInstance(loss, torch.Tensor, "Loss should be a tensor")
        self.assertGreater(loss.item(), 0, "Loss should be positive")
        print(f"✓ TPPEnhancementModule loss computation test passed: {loss.item():.4f}")
    
    def test_statistics_tracking(self):
        """测试统计信息跟踪"""
        config = {
            'channels': self.channels,
            'enabled': True
        }
        
        module = create_tpp_enhancement(config)
        x = torch.randn(self.batch_size, self.channels, self.height, self.width)
        predictions = torch.randn(self.batch_size, 2)
        targets = torch.randint(0, 2, (self.batch_size,))
        
        # 初始统计
        initial_stats = module.get_stats()
        self.assertEqual(initial_stats['total_forwards'], 0)
        self.assertEqual(initial_stats['loss_computations'], 0)
        
        # 执行操作
        with torch.no_grad():
            module(x, modality='TPP')
            module.compute_loss(predictions, targets)
        
        # 检查统计
        final_stats = module.get_stats()
        self.assertEqual(final_stats['total_forwards'], 1)
        self.assertEqual(final_stats['loss_computations'], 1)
        
        print("✓ TPPEnhancementModule statistics tracking test passed")


class TestConfigValidation(unittest.TestCase):
    """配置验证测试"""
    
    def test_valid_config(self):
        """测试有效配置"""
        valid_config = {
            'channels': 192,
            'enabled': True
        }
        
        self.assertTrue(validate_tpp_config(valid_config))
        print("✓ Valid TPP config validation test passed")
    
    def test_invalid_config(self):
        """测试无效配置"""
        invalid_configs = [
            {},  # 缺少必需字段
            {'channels': 0},  # 无效通道数
            {'channels': -1},  # 负通道数
        ]
        
        for config in invalid_configs:
            self.assertFalse(validate_tpp_config(config))
        
        print("✓ Invalid TPP config validation test passed")


def run_tpp_tests():
    """运行所有TPP增强模块测试"""
    print("="*60)
    print("TPP Enhancement Module Unit Tests")
    print("="*60)
    
    # 创建测试套件
    test_classes = [
        TestChannelAttention,
        TestSpatialAttention,
        TestCBAM,
        TestCF_Loss_2D,
        TestTPPEnhancementModule,
        TestConfigValidation
    ]
    
    total_tests = 0
    passed_tests = 0
    
    for test_class in test_classes:
        print(f"\n--- Testing {test_class.__name__} ---")
        
        # 获取测试方法
        test_methods = [method for method in dir(test_class) if method.startswith('test_')]
        
        for method_name in test_methods:
            total_tests += 1
            try:
                # 创建测试实例并运行测试
                test_instance = test_class()
                test_instance.setUp()
                test_method = getattr(test_instance, method_name)
                test_method()
                passed_tests += 1
            except Exception as e:
                print(f"✗ {method_name} failed: {e}")
    
    print("\n" + "="*60)
    print(f"TPP Tests Results: {passed_tests}/{total_tests} tests passed")
    print("="*60)
    
    return passed_tests == total_tests


if __name__ == '__main__':
    success = run_tpp_tests()
    
    if success:
        print("\n🎉 All TPP Enhancement Module tests passed!")
    else:
        print("\n❌ Some TPP Enhancement Module tests failed!")
        exit(1)
