"""
阶段3训练脚本：使用高级损失函数优化
专门针对召回率保护和精确率提升的训练流程

版本: 3.0.0 (阶段3：损失函数优化)
作者: Pulsar Classification Enhancement Team
日期: 2025-01-31
"""

import os
import sys
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import numpy as np
import argparse
import yaml
import warnings
from pathlib import Path
from typing import Dict, Any, Tuple, Optional

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from models.coatnet import CoAtNet
from models.advanced_loss_functions import AdaptiveWeightedLoss
from data.data_loader import create_data_loaders
from utils.metrics import calculate_metrics, MetricsCalculator
from utils.early_stopping import EarlyStopping
from utils.model_utils import save_model, load_model
from utils.config_utils import load_config, save_config


class AdvancedLossTrainer:
    """
    高级损失函数训练器
    集成召回率保护和精确率提升的损失函数
    """

    def __init__(self, config: Dict[str, Any], device: torch.device):
        self.config = config
        self.device = device

        # 创建模型
        self.model = self._create_model()

        # 创建高级损失函数
        self.loss_function = AdaptiveWeightedLoss(
            recall_weight=config.get('recall_weight', 3.0),
            precision_weight=config.get('precision_weight', 2.0),
            focal_weight=config.get('focal_weight', 1.0),
            contrastive_weight=config.get('contrastive_weight', 0.5)
        ).to(device)

        # 创建优化器
        self.optimizer = self._create_optimizer()

        # 创建数据加载器
        self.train_loader, self.val_loader, self.test_loader = self._create_data_loaders()

        # 早停机制
        self.early_stopping = EarlyStopping(
            patience=config.get('early_stopping_patience', 15),
            min_delta=config.get('early_stopping_min_delta', 0.001),
            restore_best_weights=True
        )

        # 性能监控
        self.metrics_calculator = MetricsCalculator()
        self.best_recall = 0.0
        self.best_precision = 0.0

    def _create_model(self) -> nn.Module:
        """创建CoAtNet模型"""
        model_config = self.config['model']
        model = CoAtNet(
            image_size=model_config['image_size'],
            in_channels=model_config['in_channels'],
            num_blocks=model_config['num_blocks'],
            channels=model_config['channels'],
            num_classes=model_config['num_classes'],
            block_types=model_config['block_types'],
            enhancement_modules=self.config.get('enhancement_modules', {})
        )
        return model.to(self.device)

    def _create_optimizer(self) -> optim.Optimizer:
        """创建优化器"""
        optimizer_config = self.config['optimizer']

        # 为损失函数权重使用更高的学习率
        param_groups = [
            {
                'params': [p for n, p in self.model.named_parameters()],
                'lr': optimizer_config['lr']
            },
            {
                'params': [p for p in self.loss_function.parameters()],
                'lr': optimizer_config['lr'] * 10  # 损失权重使用更高学习率
            }
        ]

        return optim.AdamW(param_groups, weight_decay=optimizer_config.get('weight_decay', 1e-4))

    def _create_data_loaders(self) -> Tuple[DataLoader, DataLoader, DataLoader]:
        """创建数据加载器"""
        return create_data_loaders(
            data_dir=self.config['data']['data_dir'],
            batch_size=self.config['training']['batch_size'],
            num_workers=self.config['data'].get('num_workers', 4),
            pin_memory=self.config['data'].get('pin_memory', True)
        )

    def train_epoch(self, epoch: int) -> Dict[str, float]:
        """训练一个epoch"""
        self.model.train()
        self.loss_function.train()

        total_loss = 0.0
        loss_components = {
            'total': 0.0, 'recall': 0.0, 'precision': 0.0,
            'focal': 0.0, 'contrastive': 0.0
        }

        all_predictions = []
        all_targets = []

        for batch_idx, (data, targets) in enumerate(self.train_loader):
            data, targets = data.to(self.device), targets.to(self.device)

            self.optimizer.zero_grad()

            # 前向传播，获取对比特征
            logits, contrast_features = self._forward_with_contrast(data)

            # 计算高级损失
            loss_dict = self.loss_function(logits, targets, contrast_features)
            loss = loss_dict['total_loss']

            # 反向传播
            loss.backward()

            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
            torch.nn.utils.clip_grad_norm_(self.loss_function.parameters(), max_norm=0.1)

            self.optimizer.step()

            # 统计
            total_loss += loss.item()
            for key in loss_components:
                if key in loss_dict:
                    loss_components[key] += loss_dict[key].item()

            # 收集预测结果
            with torch.no_grad():
                predictions = torch.softmax(logits, dim=1)
                all_predictions.append(predictions.cpu())
                all_targets.append(targets.cpu())

        # 计算epoch平均损失
        num_batches = len(self.train_loader)
        avg_loss = total_loss / num_batches
        for key in loss_components:
            loss_components[key] /= num_batches

        # 计算训练指标
        all_predictions = torch.cat(all_predictions, dim=0)
        all_targets = torch.cat(all_targets, dim=0)

        train_metrics = self.metrics_calculator.calculate_metrics(
            all_predictions.numpy(), all_targets.numpy()
        )

        # 更新损失函数权重
        self.loss_function.update_weights(
            train_metrics['recall'], train_metrics['precision']
        )

        return {
            'loss': avg_loss,
            'loss_components': loss_components,
            **train_metrics
        }

    def _forward_with_contrast(self, data: torch.Tensor) -> Tuple[torch.Tensor, Optional[torch.Tensor]]:
        """
        前向传播并获取对比特征
        """
        # 使用修改后的CoAtNet前向传播获取对比特征
        if hasattr(self.model, 'enhancement_manager') and self.model.enhancement_manager is not None:
            logits, contrast_features = self.model(data, return_contrast_features=True)
        else:
            logits = self.model(data)
            contrast_features = None

        return logits, contrast_features

    def validate(self) -> Dict[str, float]:
        """验证模型"""
        self.model.eval()
        self.loss_function.eval()

        total_loss = 0.0
        all_predictions = []
        all_targets = []

        with torch.no_grad():
            for data, targets in self.val_loader:
                data, targets = data.to(self.device), targets.to(self.device)

                logits, contrast_features = self._forward_with_contrast(data)

                # 计算损失
                loss_dict = self.loss_function(logits, targets, contrast_features)
                total_loss += loss_dict['total_loss'].item()

                # 收集预测结果
                predictions = torch.softmax(logits, dim=1)
                all_predictions.append(predictions.cpu())
                all_targets.append(targets.cpu())

        # 计算验证指标
        all_predictions = torch.cat(all_predictions, dim=0)
        all_targets = torch.cat(all_targets, dim=0)

        val_metrics = self.metrics_calculator.calculate_metrics(
            all_predictions.numpy(), all_targets.numpy()
        )

        val_metrics['loss'] = total_loss / len(self.val_loader)
        return val_metrics

    def train(self) -> Dict[str, Any]:
        """完整训练流程"""
        print("🚀 开始阶段3训练：高级损失函数优化")
        print(f"   目标：召回率≥100%, 精确率≥99%, 准确率≥99.5%")

        best_metrics = None
        training_history = []

        for epoch in range(self.config['training']['epochs']):
            print(f"\n📊 Epoch {epoch+1}/{self.config['training']['epochs']}")

            # 训练
            train_metrics = self.train_epoch(epoch)

            # 验证
            val_metrics = self.validate()

            # 记录历史
            epoch_history = {
                'epoch': epoch + 1,
                'train': train_metrics,
                'val': val_metrics
            }
            training_history.append(epoch_history)

            # 打印进度
            self._print_epoch_progress(train_metrics, val_metrics)

            # 早停检查
            if self.early_stopping(val_metrics['loss'], self.model):
                print(f"🛑 Early stopping at epoch {epoch+1}")
                break

            # 更新最佳指标
            if val_metrics['recall'] >= self.best_recall:
                self.best_recall = val_metrics['recall']
                self.best_precision = val_metrics['precision']
                best_metrics = val_metrics.copy()

        # 恢复最佳权重
        self.early_stopping.restore_best_weights(self.model)

        # 最终测试
        test_metrics = self.test()

        return {
            'best_val_metrics': best_metrics,
            'final_test_metrics': test_metrics,
            'training_history': training_history
        }

    def _print_epoch_progress(self, train_metrics: Dict[str, float], val_metrics: Dict[str, float]):
        """打印epoch进度"""
        print(f"   训练 - Loss: {train_metrics['loss']:.4f}, "
              f"Acc: {train_metrics['accuracy']:.4f}, "
              f"Rec: {train_metrics['recall']:.4f}, "
              f"Pre: {train_metrics['precision']:.4f}")

        print(f"   验证 - Loss: {val_metrics['loss']:.4f}, "
              f"Acc: {val_metrics['accuracy']:.4f}, "
              f"Rec: {val_metrics['recall']:.4f}, "
              f"Pre: {val_metrics['precision']:.4f}")

        # 打印损失组件
        if 'loss_components' in train_metrics:
            components = train_metrics['loss_components']
            print(f"   损失组件 - Recall: {components['recall']:.4f}, "
                  f"Precision: {components['precision']:.4f}, "
                  f"Focal: {components['focal']:.4f}, "
                  f"Contrastive: {components['contrastive']:.4f}")

    def test(self) -> Dict[str, float]:
        """测试模型"""
        self.model.eval()

        all_predictions = []
        all_targets = []

        with torch.no_grad():
            for data, targets in self.test_loader:
                data, targets = data.to(self.device), targets.to(self.device)

                logits, _ = self._forward_with_contrast(data)
                predictions = torch.softmax(logits, dim=1)

                all_predictions.append(predictions.cpu())
                all_targets.append(targets.cpu())

        all_predictions = torch.cat(all_predictions, dim=0)
        all_targets = torch.cat(all_targets, dim=0)

        return self.metrics_calculator.calculate_metrics(
            all_predictions.numpy(), all_targets.numpy()
        )


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='阶段3训练：高级损失函数优化')
    parser.add_argument('--config', type=str, required=True, help='配置文件路径')
    parser.add_argument('--output_dir', type=str, default='outputs_stage3', help='输出目录')

    args = parser.parse_args()

    # 加载配置
    config = load_config(args.config)

    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"🔧 使用设备: {device}")

    # 创建输出目录
    output_dir = Path(args.output_dir)
    output_dir.mkdir(exist_ok=True)

    # 创建训练器
    trainer = AdvancedLossTrainer(config, device)

    # 开始训练
    results = trainer.train()

    # 保存结果
    print(f"\n🎯 阶段3训练完成！")
    print(f"   最佳验证指标: {results['best_val_metrics']}")
    print(f"   最终测试指标: {results['final_test_metrics']}")

    # 检查是否达到目标
    test_metrics = results['final_test_metrics']
    success = (
        test_metrics['recall'] >= 1.0 and
        test_metrics['precision'] >= 0.99 and
        test_metrics['accuracy'] >= 0.995
    )

    if success:
        print("✅ 阶段3目标达成！所有指标均达到要求")
    else:
        print("❌ 阶段3目标未达成，需要进一步优化")
        print(f"   召回率: {test_metrics['recall']:.4f} (目标: ≥1.000)")
        print(f"   精确率: {test_metrics['precision']:.4f} (目标: ≥0.990)")
        print(f"   准确率: {test_metrics['accuracy']:.4f} (目标: ≥0.995)")


if __name__ == '__main__':
    main()
