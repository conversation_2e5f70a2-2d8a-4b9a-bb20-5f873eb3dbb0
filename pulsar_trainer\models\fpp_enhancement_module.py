"""
FPP Enhancement Module V2.0 - FPP误分类解决方案
集成MSAM和FDE，专门解决脉冲星FPP数据的假阳性问题

模块组成:
- MultiScaleAttentionModule (MSAM): 多尺度注意力机制，基于CBAM成功经验
- FeatureDiscriminatorEnhancer (FDE): 特征判别增强器，基于对比学习思想

目标: 在保持100%召回率基础上，进一步提高精确率，减少假阳性误分类

版本: 2.0.0 (阶段2：核心模块替换)
作者: Pulsar Classification Enhancement Team
日期: 2025-01-31
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import warnings
from typing import Optional, Dict, Any, Tuple

# 导入新的核心模块
try:
    from .msam_module import MultiScaleAttentionModule
    from .fde_module import FeatureDiscriminatorEnhancer
except ImportError:
    # 用于直接运行测试
    from msam_module import MultiScaleAttentionModule
    from fde_module import FeatureDiscriminatorEnhancer


# ============================================================================
# 工具函数
# ============================================================================

def normal_init(module, mean=0, std=1, bias=0):
    """标准正态分布初始化"""
    if hasattr(module, 'weight') and module.weight is not None:
        nn.init.normal_(module.weight, mean, std)
    if hasattr(module, 'bias') and module.bias is not None:
        nn.init.constant_(module.bias, bias)


def xavier_init(module, distribution='uniform'):
    """Xavier初始化"""
    if hasattr(module, 'weight') and module.weight is not None:
        if distribution == 'uniform':
            nn.init.xavier_uniform_(module.weight)
        else:
            nn.init.xavier_normal_(module.weight)


def hamming2D(M: int, N: int) -> np.ndarray:
    """生成二维Hamming窗"""
    hamming_x = np.hamming(M)
    hamming_y = np.hamming(N)
    hamming_2d = np.outer(hamming_x, hamming_y)
    return hamming_2d


# ============================================================================
# FFTNetBlock2D - 频域特征处理模块
# ============================================================================

class ModReLU(nn.Module):
    """复数域的修正线性单元激活函数"""
    def __init__(self, features):
        super().__init__()
        self.b = nn.Parameter(torch.Tensor(features))
        self.b.data.uniform_(-0.1, 0.1)

    def forward(self, x):
        return torch.abs(x) * F.relu(torch.cos(torch.angle(x) + self.b))


class FFTNetBlock2D(nn.Module):
    """2D图像频域特征处理模块"""

    def __init__(self, dim):
        super().__init__()
        self.dim = dim
        self.filter = nn.Linear(dim, dim)
        self.modrelu = ModReLU(dim)

    def forward(self, x):
        """2D图像的频域特征处理"""
        # 输入形状验证
        assert len(x.shape) == 4, f"Input must be 4D tensor [B, C, H, W], got shape {x.shape}"

        batch_size, channels, height, width = x.shape
        assert channels == self.dim, f"Input channels {channels} must match dim {self.dim}"

        # 将图像重塑为 [batch_size, height*width, channels] 以便进行线性变换
        x_reshaped = x.permute(0, 2, 3, 1).reshape(batch_size, height * width, channels)

        # 2D FFT处理 - 在空间维度上进行FFT
        x_spatial = x_reshaped.reshape(batch_size, height, width, channels).permute(0, 3, 1, 2)
        x_fft = torch.fft.fft2(x_spatial)  # 2D FFT处理

        # 频域滤波 - 分别处理实部和虚部
        x_fft_reshaped = x_fft.permute(0, 2, 3, 1).reshape(batch_size, height * width, channels)
        x_filtered = self.filter(x_fft_reshaped.real) + 1j * self.filter(x_fft_reshaped.imag)

        # 复数域激活
        x_filtered = self.modrelu(x_filtered)

        # 逆FFT变换回空间域
        x_filtered_spatial = x_filtered.reshape(batch_size, height, width, channels).permute(0, 3, 1, 2)
        x_out = torch.fft.ifft2(x_filtered_spatial).real

        return x_out


# ============================================================================
# SimplifiedFreqFusion - 频域感知特征融合模块
# ============================================================================

class SimplifiedFreqFusion(nn.Module):
    """简化版频域感知特征融合模块"""

    def __init__(self,
                 hr_channels,
                 lr_channels,
                 lowpass_kernel=5,
                 highpass_kernel=3,
                 compressed_channels=64,
                 use_high_pass=True,
                 use_low_pass=True,
                 hamming_window=True):
        super().__init__()

        self.lowpass_kernel = lowpass_kernel
        self.highpass_kernel = highpass_kernel
        self.compressed_channels = compressed_channels
        self.use_high_pass = use_high_pass
        self.use_low_pass = use_low_pass
        self.hamming_window = hamming_window

        # 通道压缩器
        self.hr_channel_compressor = nn.Conv2d(hr_channels, compressed_channels, 1)
        self.lr_channel_compressor = nn.Conv2d(lr_channels, compressed_channels, 1)

        # 低通滤波器生成器
        if self.use_low_pass:
            self.lowpass_encoder = nn.Conv2d(
                compressed_channels,
                compressed_channels,
                kernel_size=3,
                padding=1,
                groups=1
            )

        # 高通滤波器生成器
        if self.use_high_pass:
            self.highpass_encoder = nn.Conv2d(
                compressed_channels,
                compressed_channels,
                kernel_size=3,
                padding=1,
                groups=1
            )

        # 特征融合器
        self.feature_fusion = nn.Conv2d(
            compressed_channels * 2 if use_high_pass and use_low_pass else compressed_channels,
            hr_channels,
            kernel_size=1
        )

        # Hamming窗
        if self.hamming_window:
            self.register_buffer('hamming_lowpass',
                               torch.FloatTensor(hamming2D(lowpass_kernel, lowpass_kernel))[None, None,])
            self.register_buffer('hamming_highpass',
                               torch.FloatTensor(hamming2D(highpass_kernel, highpass_kernel))[None, None,])
        else:
            self.register_buffer('hamming_lowpass', torch.FloatTensor([1.0]))
            self.register_buffer('hamming_highpass', torch.FloatTensor([1.0]))

        self.init_weights()

    def init_weights(self):
        """权重初始化"""
        for m in self.modules():
            if isinstance(m, nn.Conv2d):
                xavier_init(m, distribution='uniform')

        if self.use_low_pass:
            normal_init(self.lowpass_encoder, std=0.001)
        if self.use_high_pass:
            normal_init(self.highpass_encoder, std=0.001)

    def apply_frequency_filter(self, feat, filter_type='low'):
        """应用频域滤波"""
        if filter_type == 'low' and self.use_low_pass:
            # 低通滤波：保留低频信息
            filtered = self.lowpass_encoder(feat)
            # 使用平均池化模拟低通效果
            filtered = F.avg_pool2d(filtered, kernel_size=3, stride=1, padding=1)
        elif filter_type == 'high' and self.use_high_pass:
            # 高通滤波：提取高频信息
            filtered = self.highpass_encoder(feat)
            # 高通滤波 = 原始 - 低通
            low_freq = F.avg_pool2d(feat, kernel_size=3, stride=1, padding=1)
            filtered = feat - low_freq + filtered
        else:
            filtered = feat

        return filtered

    def forward(self, hr_feat, lr_feat=None):
        """前向传播"""
        # 如果没有提供lr_feat，使用hr_feat
        if lr_feat is None:
            lr_feat = hr_feat

        # 输入验证
        assert hr_feat.shape == lr_feat.shape, f"hr_feat and lr_feat must have same shape"

        # 通道压缩
        compressed_hr = self.hr_channel_compressor(hr_feat)
        compressed_lr = self.lr_channel_compressor(lr_feat)

        # 特征融合列表
        fusion_features = []

        # 低频特征处理
        if self.use_low_pass:
            low_freq_hr = self.apply_frequency_filter(compressed_hr, 'low')
            low_freq_lr = self.apply_frequency_filter(compressed_lr, 'low')
            low_freq_fused = (low_freq_hr + low_freq_lr) / 2
            fusion_features.append(low_freq_fused)

        # 高频特征处理
        if self.use_high_pass:
            high_freq_hr = self.apply_frequency_filter(compressed_hr, 'high')
            high_freq_lr = self.apply_frequency_filter(compressed_lr, 'high')
            high_freq_fused = (high_freq_hr + high_freq_lr) / 2
            fusion_features.append(high_freq_fused)

        # 如果没有启用任何滤波器，直接融合
        if not fusion_features:
            fusion_features.append((compressed_hr + compressed_lr) / 2)

        # 特征拼接和融合
        if len(fusion_features) > 1:
            fused_compressed = torch.cat(fusion_features, dim=1)
        else:
            fused_compressed = fusion_features[0]

        # 恢复到原始通道数
        fused_feat = self.feature_fusion(fused_compressed)

        # 残差连接
        fused_feat = fused_feat + hr_feat

        return fused_feat


# ============================================================================
# FPPEnhancementModule - 统一的FPP增强模块
# ============================================================================

class FPPEnhancementModule(nn.Module):
    """
    FPP增强模块 V2.0 - 专门解决FPP数据的假阳性问题

    集成MSAM和FDE，通过多尺度注意力机制和特征判别增强
    来提高对脉冲星信号的判别能力，在保持100%召回率的基础上提升精确率。

    Args:
        channels: 输入特征通道数
        msam_config: 多尺度注意力模块配置
        fde_config: 特征判别增强器配置
        enabled: 是否启用模块
    """

    def __init__(self,
                 channels: int,
                 msam_config: Optional[Dict[str, Any]] = None,
                 fde_config: Optional[Dict[str, Any]] = None,
                 enabled: bool = True):
        super().__init__()

        self.channels = channels
        self.enabled = enabled

        # 多尺度注意力模块配置
        default_msam_config = {
            'reduction_ratio': 16,
            'spatial_kernel': 7,
            'scales': [1, 2, 4]
        }
        if msam_config:
            default_msam_config.update(msam_config)

        # 特征判别增强器配置
        default_fde_config = {
            'feature_dim': min(128, channels // 2),
            'contrast_temperature': 0.1
        }
        if fde_config:
            default_fde_config.update(fde_config)

        # 核心模块：轻量级MSAM + FDE
        self.msam = MultiScaleAttentionModule(channels, **default_msam_config)
        self.fde = FeatureDiscriminatorEnhancer(channels, **default_fde_config)

        # 性能监控
        self.register_buffer('forward_count', torch.tensor(0, dtype=torch.long))
        self.register_buffer('active_count', torch.tensor(0, dtype=torch.long))

    def forward(self, x: torch.Tensor, modality: str = 'FPP') -> torch.Tensor:
        """
        前向传播 V2.0

        Args:
            x: 输入特征张量 [B, C, H, W]
            modality: 数据模态，只有'FPP'时才激活

        Returns:
            enhanced_x: 增强后的特征张量
        """
        self.forward_count += 1

        # 只在FPP模态下激活
        if not self.enabled or modality.upper() != 'FPP':
            return x

        self.active_count += 1

        # 应用多尺度注意力机制
        x_msam = self.msam(x)

        # 应用特征判别增强
        x_enhanced, _ = self.fde(x_msam)

        return x_enhanced

    def enable(self):
        """启用模块"""
        self.enabled = True

    def disable(self):
        """禁用模块"""
        self.enabled = False

    def get_stats(self) -> Dict[str, Any]:
        """获取模块统计信息 V2.0"""
        total_forwards = self.forward_count.item()
        active_forwards = self.active_count.item()

        return {
            'enabled': self.enabled,
            'channels': self.channels,
            'version': '2.0',
            'total_forwards': total_forwards,
            'active_forwards': active_forwards,
            'activation_rate': active_forwards / max(total_forwards, 1),
            'parameter_count': sum(p.numel() for p in self.parameters() if p.requires_grad)
        }

    def reset_stats(self):
        """重置统计信息"""
        self.forward_count.zero_()
        self.active_count.zero_()


# ============================================================================
# 配置验证和工厂函数
# ============================================================================

def validate_fpp_config(config: Dict[str, Any]) -> bool:
    """验证FPP增强模块配置"""
    required_keys = ['channels']

    for key in required_keys:
        if key not in config:
            return False

    # 验证数值范围
    if config['channels'] <= 0:
        return False

    return True


def create_fpp_enhancement(config: Dict[str, Any]) -> FPPEnhancementModule:
    """创建FPP增强模块 V2.0 - 支持新旧配置格式"""
    if not validate_fpp_config(config):
        raise ValueError("Invalid FPP enhancement configuration")

    # 配置格式转换：旧格式 -> 新格式
    new_config = {}

    # 基本参数
    new_config['channels'] = config['channels']
    new_config['enabled'] = config.get('enabled', True)

    # 处理旧的freq_fusion_config -> 新的msam_config和fde_config
    if 'freq_fusion_config' in config:
        # 从旧的freq_fusion_config提取参数
        freq_config = config['freq_fusion_config']

        # 映射到MSAM配置
        new_config['msam_config'] = {
            'reduction_ratio': 32,  # 优化后的值
            'spatial_kernel': 7,
            'scales': [1, 2]  # 优化后的值
        }

        # 映射到FDE配置
        compressed_channels = freq_config.get('compressed_channels', 64)
        new_config['fde_config'] = {
            'feature_dim': min(compressed_channels, config['channels'] // 4),
            'contrast_temperature': 0.1
        }
    else:
        # 使用新格式的配置
        new_config['msam_config'] = config.get('msam_config', {})
        new_config['fde_config'] = config.get('fde_config', {})

    # 移除旧的参数（如果存在）
    for old_param in ['fft_dim', 'freq_fusion_config']:
        new_config.pop(old_param, None)

    return FPPEnhancementModule(**new_config)


# 测试代码
if __name__ == '__main__':
    print("Testing FPP Enhancement Module...")

    # 测试配置 V2.0
    config = {
        'channels': 256,
        'msam_config': {
            'reduction_ratio': 32,
            'spatial_kernel': 7,
            'scales': [1, 2]
        },
        'fde_config': {
            'feature_dim': 64,
            'contrast_temperature': 0.1
        },
        'enabled': True
    }

    # 创建模块
    fpp_module = create_fpp_enhancement(config)
    print(f"FPP Enhancement Module created with {fpp_module.get_stats()['parameter_count']} parameters")

    # 测试前向传播
    batch_size = 2
    channels = 256
    height, width = 16, 16

    x = torch.randn(batch_size, channels, height, width)

    # FPP模态测试
    output_fpp = fpp_module(x, modality='FPP')
    print(f"FPP mode - Input: {x.shape}, Output: {output_fpp.shape}")

    # TPP模态测试（应该直接返回输入）
    output_tpp = fpp_module(x, modality='TPP')
    print(f"TPP mode - Input: {x.shape}, Output: {output_tpp.shape}")
    print(f"TPP mode passthrough: {torch.equal(x, output_tpp)}")

    # 统计信息
    stats = fpp_module.get_stats()
    print(f"Module stats: {stats}")

    print("✅ FPP Enhancement Module test completed successfully!")
