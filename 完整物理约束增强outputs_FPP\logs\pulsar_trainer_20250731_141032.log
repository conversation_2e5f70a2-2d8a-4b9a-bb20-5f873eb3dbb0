2025-07-31 14:10:32 - pulsar_trainer - INFO - <PERSON><PERSON> initialized. Log file: D:\pulsarSuanfa\outputs_FPP\logs\pulsar_trainer_20250731_141032.log
2025-07-31 14:10:32 - pulsar_trainer - INFO - 随机种子设置为: 42
2025-07-31 14:10:32 - pulsar_trainer - INFO - 使用配置文件参数创建模型
2025-07-31 14:10:32 - pulsar_trainer - INFO - 模型配置 - num_blocks: [2, 2, 6, 8, 2]
2025-07-31 14:10:32 - pulsar_trainer - INFO - 模型配置 - channels: [96, 128, 256, 512, 1024]
2025-07-31 14:10:32 - pulsar_trainer - INFO - 模型配置 - in_channels: 1
2025-07-31 14:10:32 - pulsar_trainer - INFO - 模型架构详情:
2025-07-31 14:10:32 - pulsar_trainer - INFO -   总参数数量: 38,726,128
2025-07-31 14:10:32 - pulsar_trainer - INFO -   可训练参数: 38,726,128
2025-07-31 14:10:32 - pulsar_trainer - INFO -   模型大小: 147.73 MB
2025-07-31 14:10:32 - pulsar_trainer - INFO -   块类型配置: ['C', 'C', 'T', 'T']
2025-07-31 14:10:32 - pulsar_trainer - INFO -   各阶段块数: [2, 2, 6, 8, 2]
2025-07-31 14:10:32 - pulsar_trainer - INFO -   各阶段通道数: [96, 128, 256, 512, 1024]
2025-07-31 14:10:32 - pulsar_trainer - INFO - Using standard cross entropy loss
2025-07-31 14:10:33 - pulsar_trainer - INFO - 🖥️ 使用GPU: NVIDIA GeForce RTX 4050 Laptop GPU
2025-07-31 14:10:33 - pulsar_trainer - INFO - 💾 GPU内存: 6.4GB
2025-07-31 14:10:33 - pulsar_trainer - INFO - 📋 模型参数总数: 38,726,128
2025-07-31 14:10:33 - pulsar_trainer - INFO - 🏗️ 模型架构: CoAtNet
2025-07-31 14:10:33 - pulsar_trainer - INFO - 📚 数据集大小:
2025-07-31 14:10:33 - pulsar_trainer - INFO -   - 训练集: 1674
2025-07-31 14:10:33 - pulsar_trainer - INFO -   - 验证集: 360
2025-07-31 14:10:33 - pulsar_trainer - INFO -   - 测试集: 358
2025-07-31 14:10:33 - pulsar_trainer - INFO -   - 总计: 2392
2025-07-31 14:10:33 - pulsar_trainer - INFO - ============================================================
2025-07-31 14:10:33 - pulsar_trainer - INFO - 🚀 开始脉冲星分类训练
2025-07-31 14:10:33 - pulsar_trainer - INFO - 📊 模态: FPP
2025-07-31 14:10:33 - pulsar_trainer - INFO - 🧠 模型: coatnet
2025-07-31 14:10:33 - pulsar_trainer - INFO - 📦 批次大小: 32
2025-07-31 14:10:33 - pulsar_trainer - INFO - 🎯 学习率: 0.001
2025-07-31 14:10:33 - pulsar_trainer - INFO - 🔄 训练轮数: 100
2025-07-31 14:10:33 - pulsar_trainer - INFO - ============================================================
2025-07-31 14:11:23 - pulsar_trainer - INFO - Epoch   1: Train Loss=3.3096, Val Loss=0.5655, Train Acc=79.69%, Val Acc=80.00%
2025-07-31 14:11:23 - pulsar_trainer - INFO - Validation F1: 79.3044, Precision: 84.6598, Recall: 80.0000
2025-07-31 14:11:23 - pulsar_trainer - INFO - 模型已保存: D:\pulsarSuanfa\outputs_FPP\models\best_model.pth
2025-07-31 14:11:28 - pulsar_trainer - INFO - Epoch   2: Train Loss=0.2380, Val Loss=0.3257, Train Acc=92.71%, Val Acc=92.50%
2025-07-31 14:11:28 - pulsar_trainer - INFO - Validation F1: 92.4693, Precision: 93.2054, Recall: 92.5000
2025-07-31 14:11:28 - pulsar_trainer - INFO - 模型已保存: D:\pulsarSuanfa\outputs_FPP\models\best_model.pth
2025-07-31 14:11:33 - pulsar_trainer - INFO - Epoch   3: Train Loss=0.2623, Val Loss=0.1749, Train Acc=93.97%, Val Acc=95.56%
2025-07-31 14:11:33 - pulsar_trainer - INFO - Validation F1: 95.5521, Precision: 95.6966, Recall: 95.5556
2025-07-31 14:11:33 - pulsar_trainer - INFO - 模型已保存: D:\pulsarSuanfa\outputs_FPP\models\best_model.pth
2025-07-31 14:11:37 - pulsar_trainer - INFO - Epoch   4: Train Loss=0.1767, Val Loss=0.1137, Train Acc=94.44%, Val Acc=96.39%
2025-07-31 14:11:37 - pulsar_trainer - INFO - Validation F1: 96.3866, Precision: 96.5052, Recall: 96.3889
2025-07-31 14:11:38 - pulsar_trainer - INFO - 模型已保存: D:\pulsarSuanfa\outputs_FPP\models\best_model.pth
2025-07-31 14:11:42 - pulsar_trainer - INFO - Epoch   5: Train Loss=0.1025, Val Loss=0.1006, Train Acc=97.07%, Val Acc=97.22%
2025-07-31 14:11:42 - pulsar_trainer - INFO - Validation F1: 97.2219, Precision: 97.2456, Recall: 97.2222
2025-07-31 14:11:43 - pulsar_trainer - INFO - 模型已保存: D:\pulsarSuanfa\outputs_FPP\models\best_model.pth
2025-07-31 14:11:47 - pulsar_trainer - INFO - Epoch   6: Train Loss=0.1228, Val Loss=0.0798, Train Acc=95.82%, Val Acc=97.78%
2025-07-31 14:11:47 - pulsar_trainer - INFO - Validation F1: 97.7778, Precision: 97.7778, Recall: 97.7778
2025-07-31 14:11:48 - pulsar_trainer - INFO - 模型已保存: D:\pulsarSuanfa\outputs_FPP\models\best_model.pth
2025-07-31 14:11:52 - pulsar_trainer - INFO - Epoch   7: Train Loss=0.1128, Val Loss=0.0891, Train Acc=96.71%, Val Acc=97.78%
2025-07-31 14:11:52 - pulsar_trainer - INFO - Validation F1: 97.7775, Precision: 97.8014, Recall: 97.7778
2025-07-31 14:11:57 - pulsar_trainer - INFO - Epoch   8: Train Loss=0.0832, Val Loss=0.0841, Train Acc=97.19%, Val Acc=97.78%
2025-07-31 14:11:57 - pulsar_trainer - INFO - Validation F1: 97.7777, Precision: 97.7837, Recall: 97.7778
2025-07-31 14:12:01 - pulsar_trainer - INFO - Epoch   9: Train Loss=0.0651, Val Loss=0.0643, Train Acc=98.09%, Val Acc=98.06%
2025-07-31 14:12:01 - pulsar_trainer - INFO - Validation F1: 98.0555, Precision: 98.0570, Recall: 98.0556
2025-07-31 14:12:02 - pulsar_trainer - INFO - 模型已保存: D:\pulsarSuanfa\outputs_FPP\models\best_model.pth
2025-07-31 14:12:06 - pulsar_trainer - INFO - Epoch  10: Train Loss=0.0557, Val Loss=0.0883, Train Acc=98.09%, Val Acc=97.78%
2025-07-31 14:12:06 - pulsar_trainer - INFO - Validation F1: 97.7778, Precision: 97.7778, Recall: 97.7778
2025-07-31 14:12:11 - pulsar_trainer - INFO - Epoch  11: Train Loss=0.0569, Val Loss=0.0748, Train Acc=98.15%, Val Acc=98.06%
2025-07-31 14:12:11 - pulsar_trainer - INFO - Validation F1: 98.0555, Precision: 98.0570, Recall: 98.0556
2025-07-31 14:12:15 - pulsar_trainer - INFO - Epoch  12: Train Loss=0.0856, Val Loss=0.0569, Train Acc=98.03%, Val Acc=98.06%
2025-07-31 14:12:15 - pulsar_trainer - INFO - Validation F1: 98.0554, Precision: 98.0689, Recall: 98.0556
2025-07-31 14:12:19 - pulsar_trainer - INFO - Epoch  13: Train Loss=0.0687, Val Loss=0.1002, Train Acc=97.73%, Val Acc=96.11%
2025-07-31 14:12:19 - pulsar_trainer - INFO - Validation F1: 96.1081, Precision: 96.2539, Recall: 96.1111
2025-07-31 14:12:24 - pulsar_trainer - INFO - Epoch  14: Train Loss=0.0496, Val Loss=0.0571, Train Acc=98.51%, Val Acc=98.61%
2025-07-31 14:12:24 - pulsar_trainer - INFO - Validation F1: 98.6111, Precision: 98.6126, Recall: 98.6111
2025-07-31 14:12:24 - pulsar_trainer - INFO - 模型已保存: D:\pulsarSuanfa\outputs_FPP\models\best_model.pth
2025-07-31 14:12:29 - pulsar_trainer - INFO - Epoch  15: Train Loss=0.0687, Val Loss=0.0850, Train Acc=97.61%, Val Acc=97.78%
2025-07-31 14:12:29 - pulsar_trainer - INFO - Validation F1: 97.7778, Precision: 97.7778, Recall: 97.7778
2025-07-31 14:12:33 - pulsar_trainer - INFO - Epoch  16: Train Loss=0.0471, Val Loss=0.0847, Train Acc=98.51%, Val Acc=98.06%
2025-07-31 14:12:33 - pulsar_trainer - INFO - Validation F1: 98.0555, Precision: 98.0570, Recall: 98.0556
2025-07-31 14:12:38 - pulsar_trainer - INFO - Epoch  17: Train Loss=0.0465, Val Loss=0.0803, Train Acc=98.69%, Val Acc=97.78%
2025-07-31 14:12:38 - pulsar_trainer - INFO - Validation F1: 97.7775, Precision: 97.8014, Recall: 97.7778
2025-07-31 14:12:42 - pulsar_trainer - INFO - Epoch  18: Train Loss=0.0441, Val Loss=0.0452, Train Acc=98.63%, Val Acc=97.78%
2025-07-31 14:12:42 - pulsar_trainer - INFO - Validation F1: 97.7777, Precision: 97.7837, Recall: 97.7778
2025-07-31 14:12:46 - pulsar_trainer - INFO - Epoch  19: Train Loss=0.0437, Val Loss=0.0698, Train Acc=98.27%, Val Acc=98.06%
2025-07-31 14:12:46 - pulsar_trainer - INFO - Validation F1: 98.0554, Precision: 98.0689, Recall: 98.0556
2025-07-31 14:12:51 - pulsar_trainer - INFO - Epoch  20: Train Loss=0.0392, Val Loss=0.0791, Train Acc=98.57%, Val Acc=98.33%
2025-07-31 14:12:51 - pulsar_trainer - INFO - Validation F1: 98.3333, Precision: 98.3393, Recall: 98.3333
2025-07-31 14:12:55 - pulsar_trainer - INFO - Epoch  21: Train Loss=0.0379, Val Loss=0.0773, Train Acc=98.98%, Val Acc=98.06%
2025-07-31 14:12:55 - pulsar_trainer - INFO - Validation F1: 98.0554, Precision: 98.0689, Recall: 98.0556
2025-07-31 14:13:00 - pulsar_trainer - INFO - Epoch  22: Train Loss=0.0437, Val Loss=0.0537, Train Acc=98.69%, Val Acc=98.61%
2025-07-31 14:13:00 - pulsar_trainer - INFO - Validation F1: 98.6111, Precision: 98.6126, Recall: 98.6111
2025-07-31 14:13:04 - pulsar_trainer - INFO - Epoch  23: Train Loss=0.0291, Val Loss=0.0778, Train Acc=99.16%, Val Acc=98.33%
2025-07-31 14:13:04 - pulsar_trainer - INFO - Validation F1: 98.3331, Precision: 98.3572, Recall: 98.3333
2025-07-31 14:13:09 - pulsar_trainer - INFO - Epoch  24: Train Loss=0.0381, Val Loss=0.0580, Train Acc=99.10%, Val Acc=98.61%
2025-07-31 14:13:09 - pulsar_trainer - INFO - Validation F1: 98.6110, Precision: 98.6246, Recall: 98.6111
2025-07-31 14:13:13 - pulsar_trainer - INFO - Epoch  25: Train Loss=0.0387, Val Loss=0.0585, Train Acc=98.51%, Val Acc=98.61%
2025-07-31 14:13:13 - pulsar_trainer - INFO - Validation F1: 98.6110, Precision: 98.6246, Recall: 98.6111
2025-07-31 14:13:17 - pulsar_trainer - INFO - Epoch  26: Train Loss=0.0450, Val Loss=0.0589, Train Acc=98.57%, Val Acc=98.06%
2025-07-31 14:13:17 - pulsar_trainer - INFO - Validation F1: 98.0555, Precision: 98.0570, Recall: 98.0556
2025-07-31 14:13:22 - pulsar_trainer - INFO - Epoch  27: Train Loss=0.0283, Val Loss=0.0373, Train Acc=99.34%, Val Acc=98.33%
2025-07-31 14:13:22 - pulsar_trainer - INFO - Validation F1: 98.3331, Precision: 98.3572, Recall: 98.3333
2025-07-31 14:13:26 - pulsar_trainer - INFO - Epoch  28: Train Loss=0.0426, Val Loss=0.0529, Train Acc=98.63%, Val Acc=97.50%
2025-07-31 14:13:26 - pulsar_trainer - INFO - Validation F1: 97.4995, Precision: 97.5367, Recall: 97.5000
2025-07-31 14:13:31 - pulsar_trainer - INFO - Epoch  29: Train Loss=0.0371, Val Loss=0.0668, Train Acc=98.98%, Val Acc=98.33%
2025-07-31 14:13:31 - pulsar_trainer - INFO - Validation F1: 98.3333, Precision: 98.3393, Recall: 98.3333
2025-07-31 14:13:31 - pulsar_trainer - INFO - 早停触发，在第 29 轮停止训练
2025-07-31 14:13:31 - pulsar_trainer - INFO - ============================================================
2025-07-31 14:13:31 - pulsar_trainer - INFO - ✅ 训练完成
2025-07-31 14:13:31 - pulsar_trainer - INFO - 🏆 最佳验证准确率: 98.6111
2025-07-31 14:13:31 - pulsar_trainer - INFO - ⏱️ 总训练时间: 178.25秒
2025-07-31 14:13:31 - pulsar_trainer - INFO - ============================================================
2025-07-31 14:13:31 - pulsar_trainer - INFO - 🔍 开始最终评估...
2025-07-31 14:13:31 - pulsar_trainer - INFO - 模型已加载: D:\pulsarSuanfa\outputs_FPP\models\best_model.pth
2025-07-31 14:13:54 - pulsar_trainer.utils.comprehensive_misclassification_analysis - INFO - 开始全面误分类分析...
2025-07-31 14:13:54 - pulsar_trainer.utils.comprehensive_misclassification_analysis - INFO - 综合误分类分析报告已生成: D:\pulsarSuanfa\outputs_FPP\results\comprehensive_misclassification_analysis\comprehensive_misclassification_report.txt
2025-07-31 14:13:54 - pulsar_trainer.utils.comprehensive_misclassification_analysis - INFO - 全面误分类分析完成。报告保存至: D:\pulsarSuanfa\outputs_FPP\results\comprehensive_misclassification_analysis
2025-07-31 14:13:54 - pulsar_trainer - INFO - 📊 评估结果:
2025-07-31 14:13:54 - pulsar_trainer - INFO -   - accuracy: 0.9888
2025-07-31 14:13:54 - pulsar_trainer - INFO -   - precision: 0.9781
2025-07-31 14:13:54 - pulsar_trainer - INFO -   - recall: 1.0000
2025-07-31 14:13:54 - pulsar_trainer - INFO -   - specificity: 0.9777
2025-07-31 14:13:54 - pulsar_trainer - INFO -   - f1_score: 0.9890
2025-07-31 14:13:54 - pulsar_trainer - INFO -   - false_positive_rate: 0.0223
2025-07-31 14:13:56 - pulsar_trainer - WARNING - ⚠️ Misclassification analysis failed: 'low_pulsar_prob_count'
2025-07-31 14:13:56 - pulsar_trainer - INFO - 所有结果已保存到: D:\pulsarSuanfa\outputs_FPP\results
2025-07-31 14:13:56 - pulsar_trainer - INFO - 可视化图表已保存到: D:\pulsarSuanfa\outputs_FPP\plots
