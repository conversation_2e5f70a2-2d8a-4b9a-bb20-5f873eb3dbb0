2025-07-31 16:16:50 - pulsar_trainer - INFO - <PERSON><PERSON> initialized. Log file: D:\pulsarSuanfa\outputs_TPP\logs\pulsar_trainer_20250731_161650.log
2025-07-31 16:16:50 - pulsar_trainer - INFO - 随机种子设置为: 42
2025-07-31 16:16:50 - pulsar_trainer - INFO - 使用配置文件参数创建模型
2025-07-31 16:16:50 - pulsar_trainer - INFO - 增强模块配置已加载: FPP=True, TPP=True
2025-07-31 16:16:50 - pulsar_trainer - INFO - 模型配置 - num_blocks: [2, 2, 6, 8, 2]
2025-07-31 16:16:50 - pulsar_trainer - INFO - 模型配置 - channels: [96, 128, 256, 512, 1024]
2025-07-31 16:16:50 - pulsar_trainer - INFO - 模型配置 - in_channels: 1
2025-07-31 16:16:50 - pulsar_trainer - INFO - ✓ 增强模块已启用
2025-07-31 16:16:50 - pulsar_trainer - INFO -   - FPP模块可用: True
2025-07-31 16:16:50 - pulsar_trainer - INFO -   - TPP模块可用: True
2025-07-31 16:16:50 - pulsar_trainer - INFO -   - 当前模态: AUTO
2025-07-31 16:16:50 - pulsar_trainer - INFO - 模型架构详情:
2025-07-31 16:16:50 - pulsar_trainer - INFO -   总参数数量: 38,773,938
2025-07-31 16:16:50 - pulsar_trainer - INFO -   可训练参数: 38,773,938
2025-07-31 16:16:50 - pulsar_trainer - INFO -   模型大小: 147.91 MB
2025-07-31 16:16:50 - pulsar_trainer - INFO -   块类型配置: ['C', 'C', 'T', 'T']
2025-07-31 16:16:50 - pulsar_trainer - INFO -   各阶段块数: [2, 2, 6, 8, 2]
2025-07-31 16:16:50 - pulsar_trainer - INFO -   各阶段通道数: [96, 128, 256, 512, 1024]
2025-07-31 16:16:50 - pulsar_trainer - INFO - Using standard cross entropy loss
2025-07-31 16:16:50 - pulsar_trainer - INFO - 🖥️ 使用GPU: NVIDIA GeForce RTX 4050 Laptop GPU
2025-07-31 16:16:50 - pulsar_trainer - INFO - 💾 GPU内存: 6.4GB
2025-07-31 16:16:50 - pulsar_trainer - INFO - 📋 模型参数总数: 38,773,938
2025-07-31 16:16:50 - pulsar_trainer - INFO - 🏗️ 模型架构: CoAtNet
2025-07-31 16:16:50 - pulsar_trainer - INFO - 📚 数据集大小:
2025-07-31 16:16:50 - pulsar_trainer - INFO -   - 训练集: 1674
2025-07-31 16:16:50 - pulsar_trainer - INFO -   - 验证集: 360
2025-07-31 16:16:50 - pulsar_trainer - INFO -   - 测试集: 358
2025-07-31 16:16:50 - pulsar_trainer - INFO -   - 总计: 2392
2025-07-31 16:16:50 - pulsar_trainer - INFO - ============================================================
2025-07-31 16:16:50 - pulsar_trainer - INFO - 🚀 开始脉冲星分类训练
2025-07-31 16:16:50 - pulsar_trainer - INFO - 📊 模态: TPP
2025-07-31 16:16:50 - pulsar_trainer - INFO - 🧠 模型: coatnet
2025-07-31 16:16:50 - pulsar_trainer - INFO - 📦 批次大小: 32
2025-07-31 16:16:50 - pulsar_trainer - INFO - 🎯 学习率: 0.001
2025-07-31 16:16:50 - pulsar_trainer - INFO - 🔄 训练轮数: 100
2025-07-31 16:16:50 - pulsar_trainer - INFO - ============================================================
2025-07-31 16:17:13 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.6305369138717651, 'fd_loss': 0.4615345597267151, 'vd_loss': 0.2774299383163452, 'confidence_penalty': 0.0, 'total_loss': 0.9445331692695618}
2025-07-31 16:17:41 - pulsar_trainer - INFO - Epoch   1: Train Loss=7.6326, Val Loss=0.2823, Train Acc=68.88%, Val Acc=91.67%
2025-07-31 16:17:41 - pulsar_trainer - INFO - Validation F1: 91.6574, Precision: 91.8527, Recall: 91.6667
2025-07-31 16:17:42 - pulsar_trainer - INFO - 模型已保存: D:\pulsarSuanfa\outputs_TPP\models\best_model.pth
2025-07-31 16:17:42 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.5288880467414856, 'fd_loss': 0.23689812421798706, 'vd_loss': 0.2543178200721741, 'confidence_penalty': 0.004524052143096924, 'total_loss': 0.7281565070152283}
2025-07-31 16:17:47 - pulsar_trainer - INFO - Epoch   2: Train Loss=0.3602, Val Loss=0.4766, Train Acc=90.62%, Val Acc=90.56%
2025-07-31 16:17:47 - pulsar_trainer - INFO - Validation F1: 90.4706, Precision: 92.0561, Recall: 90.5556
2025-07-31 16:17:47 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.5553025603294373, 'fd_loss': 0.09031319618225098, 'vd_loss': 0.17984989285469055, 'confidence_penalty': 0.004969037603586912, 'total_loss': 0.6593831777572632}
2025-07-31 16:17:52 - pulsar_trainer - INFO - Epoch   3: Train Loss=0.3891, Val Loss=0.5532, Train Acc=92.00%, Val Acc=90.28%
2025-07-31 16:17:52 - pulsar_trainer - INFO - Validation F1: 90.1850, Precision: 91.8605, Recall: 90.2778
2025-07-31 16:17:52 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.29526206851005554, 'fd_loss': 0.0313321053981781, 'vd_loss': 0.06232595443725586, 'confidence_penalty': 0.004991296678781509, 'total_loss': 0.3346171975135803}
2025-07-31 16:17:57 - pulsar_trainer - INFO - Epoch   4: Train Loss=0.5476, Val Loss=0.6710, Train Acc=85.96%, Val Acc=58.06%
2025-07-31 16:17:57 - pulsar_trainer - INFO - Validation F1: 49.1006, Precision: 77.1903, Recall: 58.0556
2025-07-31 16:17:58 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.1081673726439476, 'fd_loss': 0.06663132458925247, 'vd_loss': 0.10160505771636963, 'confidence_penalty': 0.0032933929469436407, 'total_loss': 0.17525795102119446}
2025-07-31 16:18:03 - pulsar_trainer - INFO - Epoch   5: Train Loss=0.1845, Val Loss=0.0343, Train Acc=94.80%, Val Acc=99.17%
2025-07-31 16:18:03 - pulsar_trainer - INFO - Validation F1: 99.1667, Precision: 99.1682, Recall: 99.1667
2025-07-31 16:18:03 - pulsar_trainer - INFO - 模型已保存: D:\pulsarSuanfa\outputs_TPP\models\best_model.pth
2025-07-31 16:18:03 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.014001566916704178, 'fd_loss': 0.013488213531672955, 'vd_loss': 0.013497471809387207, 'confidence_penalty': 0.004499976988881826, 'total_loss': 0.029294893145561218}
2025-07-31 16:18:08 - pulsar_trainer - INFO - Epoch   6: Train Loss=0.1106, Val Loss=0.0590, Train Acc=97.01%, Val Acc=99.17%
2025-07-31 16:18:08 - pulsar_trainer - INFO - Validation F1: 99.1666, Precision: 99.1803, Recall: 99.1667
2025-07-31 16:18:08 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.46859800815582275, 'fd_loss': 0.10573127865791321, 'vd_loss': 0.036937981843948364, 'confidence_penalty': 0.004679714795202017, 'total_loss': 0.5372247695922852}
2025-07-31 16:18:13 - pulsar_trainer - INFO - Epoch   7: Train Loss=0.0991, Val Loss=0.0253, Train Acc=97.31%, Val Acc=99.17%
2025-07-31 16:18:13 - pulsar_trainer - INFO - Validation F1: 99.1667, Precision: 99.1682, Recall: 99.1667
2025-07-31 16:18:14 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.031956519931554794, 'fd_loss': 0.02225150540471077, 'vd_loss': 0.03885936737060547, 'confidence_penalty': 0.0046249497681856155, 'total_loss': 0.059365034103393555}
2025-07-31 16:18:19 - pulsar_trainer - INFO - Epoch   8: Train Loss=0.0961, Val Loss=0.0315, Train Acc=97.55%, Val Acc=99.17%
2025-07-31 16:18:19 - pulsar_trainer - INFO - Validation F1: 99.1667, Precision: 99.1682, Recall: 99.1667
2025-07-31 16:18:19 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.06395988166332245, 'fd_loss': 0.0339164137840271, 'vd_loss': 0.06631964445114136, 'confidence_penalty': 0.004813291132450104, 'total_loss': 0.10562727600336075}
2025-07-31 16:18:24 - pulsar_trainer - INFO - Epoch   9: Train Loss=0.0792, Val Loss=0.0206, Train Acc=97.79%, Val Acc=99.17%
2025-07-31 16:18:24 - pulsar_trainer - INFO - Validation F1: 99.1667, Precision: 99.1682, Recall: 99.1667
2025-07-31 16:18:24 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.0013080614153295755, 'fd_loss': 0.0013048240216448903, 'vd_loss': 0.00027060508728027344, 'confidence_penalty': 0.004869518801569939, 'total_loss': 0.006911173462867737}
2025-07-31 16:18:29 - pulsar_trainer - INFO - Epoch  10: Train Loss=0.0541, Val Loss=0.0207, Train Acc=98.39%, Val Acc=99.72%
2025-07-31 16:18:29 - pulsar_trainer - INFO - Validation F1: 99.7222, Precision: 99.7238, Recall: 99.7222
2025-07-31 16:18:29 - pulsar_trainer - INFO - 模型已保存: D:\pulsarSuanfa\outputs_TPP\models\best_model.pth
2025-07-31 16:18:29 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.11885672062635422, 'fd_loss': 0.06897495687007904, 'vd_loss': 0.12414127588272095, 'confidence_penalty': 0.00469346484169364, 'total_loss': 0.1952800452709198}
2025-07-31 16:18:34 - pulsar_trainer - INFO - Epoch  11: Train Loss=0.0906, Val Loss=0.0144, Train Acc=97.55%, Val Acc=99.72%
2025-07-31 16:18:34 - pulsar_trainer - INFO - Validation F1: 99.7222, Precision: 99.7238, Recall: 99.7222
2025-07-31 16:18:35 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.04369816556572914, 'fd_loss': 0.024585846811532974, 'vd_loss': 0.048593759536743164, 'confidence_penalty': 0.004854205530136824, 'total_loss': 0.07542341947555542}
2025-07-31 16:18:40 - pulsar_trainer - INFO - Epoch  12: Train Loss=0.0634, Val Loss=0.0099, Train Acc=98.27%, Val Acc=99.72%
2025-07-31 16:18:40 - pulsar_trainer - INFO - Validation F1: 99.7222, Precision: 99.7238, Recall: 99.7222
2025-07-31 16:18:40 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.111991748213768, 'fd_loss': 0.04076530784368515, 'vd_loss': 0.08099067211151123, 'confidence_penalty': 0.004118120297789574, 'total_loss': 0.16078972816467285}
2025-07-31 16:18:45 - pulsar_trainer - INFO - Epoch  13: Train Loss=0.0549, Val Loss=0.0185, Train Acc=98.57%, Val Acc=99.44%
2025-07-31 16:18:45 - pulsar_trainer - INFO - Validation F1: 99.4444, Precision: 99.4444, Recall: 99.4444
2025-07-31 16:18:45 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.008067679591476917, 'fd_loss': 0.0076546454802155495, 'vd_loss': 0.010914534330368042, 'confidence_penalty': 0.004689088556915522, 'total_loss': 0.019858451560139656}
2025-07-31 16:18:50 - pulsar_trainer - INFO - Epoch  14: Train Loss=0.0421, Val Loss=0.0328, Train Acc=98.92%, Val Acc=98.61%
2025-07-31 16:18:50 - pulsar_trainer - INFO - Validation F1: 98.6110, Precision: 98.6246, Recall: 98.6111
2025-07-31 16:18:50 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.031689975410699844, 'fd_loss': 0.02025451511144638, 'vd_loss': 0.03917646408081055, 'confidence_penalty': 0.004944589454680681, 'total_loss': 0.05851476266980171}
2025-07-31 16:18:55 - pulsar_trainer - INFO - Epoch  15: Train Loss=0.0422, Val Loss=0.0278, Train Acc=98.92%, Val Acc=98.89%
2025-07-31 16:18:55 - pulsar_trainer - INFO - Validation F1: 98.8889, Precision: 98.8889, Recall: 98.8889
2025-07-31 16:18:55 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.08446183055639267, 'fd_loss': 0.03388754278421402, 'vd_loss': 0.0610278844833374, 'confidence_penalty': 0.004472442902624607, 'total_loss': 0.12418641149997711}
2025-07-31 16:19:00 - pulsar_trainer - INFO - Epoch  16: Train Loss=0.0505, Val Loss=0.0172, Train Acc=98.39%, Val Acc=99.72%
2025-07-31 16:19:00 - pulsar_trainer - INFO - Validation F1: 99.7222, Precision: 99.7238, Recall: 99.7222
2025-07-31 16:19:00 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.11425741761922836, 'fd_loss': 0.031778983771800995, 'vd_loss': 0.058121711015701294, 'confidence_penalty': 0.004778389818966389, 'total_loss': 0.15236181020736694}
2025-07-31 16:19:05 - pulsar_trainer - INFO - Epoch  17: Train Loss=0.0500, Val Loss=0.0230, Train Acc=98.45%, Val Acc=99.72%
2025-07-31 16:19:05 - pulsar_trainer - INFO - Validation F1: 99.7222, Precision: 99.7238, Recall: 99.7222
2025-07-31 16:19:05 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.01615263894200325, 'fd_loss': 0.014187747612595558, 'vd_loss': 0.02630138397216797, 'confidence_penalty': 0.0048209321685135365, 'total_loss': 0.035957857966423035}
2025-07-31 16:19:10 - pulsar_trainer - INFO - Epoch  18: Train Loss=0.0502, Val Loss=0.0137, Train Acc=98.63%, Val Acc=99.72%
2025-07-31 16:19:10 - pulsar_trainer - INFO - Validation F1: 99.7222, Precision: 99.7238, Recall: 99.7222
2025-07-31 16:19:10 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.006003854796290398, 'fd_loss': 0.005733049474656582, 'vd_loss': 0.011310577392578125, 'confidence_penalty': 0.004795117303729057, 'total_loss': 0.01705867052078247}
2025-07-31 16:19:15 - pulsar_trainer - INFO - Epoch  19: Train Loss=0.0432, Val Loss=0.0159, Train Acc=98.86%, Val Acc=99.72%
2025-07-31 16:19:15 - pulsar_trainer - INFO - Validation F1: 99.7222, Precision: 99.7238, Recall: 99.7222
2025-07-31 16:19:15 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.00947672501206398, 'fd_loss': 0.009231668896973133, 'vd_loss': 0.007060885429382324, 'confidence_penalty': 0.004366369917988777, 'total_loss': 0.020577194169163704}
2025-07-31 16:19:20 - pulsar_trainer - INFO - Epoch  20: Train Loss=0.0433, Val Loss=0.0206, Train Acc=99.10%, Val Acc=99.17%
2025-07-31 16:19:20 - pulsar_trainer - INFO - Validation F1: 99.1666, Precision: 99.1803, Recall: 99.1667
2025-07-31 16:19:20 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.00707710487768054, 'fd_loss': 0.006670078262686729, 'vd_loss': 0.0057141780853271484, 'confidence_penalty': 0.00479375384747982, 'total_loss': 0.016920151188969612}
2025-07-31 16:19:25 - pulsar_trainer - INFO - Epoch  21: Train Loss=0.0404, Val Loss=0.0035, Train Acc=98.81%, Val Acc=100.00%
2025-07-31 16:19:25 - pulsar_trainer - INFO - Validation F1: 100.0000, Precision: 100.0000, Recall: 100.0000
2025-07-31 16:19:26 - pulsar_trainer - INFO - 模型已保存: D:\pulsarSuanfa\outputs_TPP\models\best_model.pth
2025-07-31 16:19:26 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.0020894459448754787, 'fd_loss': 0.0020671398378908634, 'vd_loss': 0.003719836473464966, 'confidence_penalty': 0.004793287254869938, 'total_loss': 0.009032254107296467}
2025-07-31 16:19:31 - pulsar_trainer - INFO - Epoch  22: Train Loss=0.0385, Val Loss=0.0358, Train Acc=98.92%, Val Acc=97.78%
2025-07-31 16:19:31 - pulsar_trainer - INFO - Validation F1: 97.7767, Precision: 97.8723, Recall: 97.7778
2025-07-31 16:19:31 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.006975997239351273, 'fd_loss': 0.006801387760788202, 'vd_loss': 0.010889887809753418, 'confidence_penalty': 0.004588468000292778, 'total_loss': 0.018232125788927078}
2025-07-31 16:19:36 - pulsar_trainer - INFO - Epoch  23: Train Loss=0.0600, Val Loss=0.0316, Train Acc=98.45%, Val Acc=99.17%
2025-07-31 16:19:36 - pulsar_trainer - INFO - Validation F1: 99.1666, Precision: 99.1803, Recall: 99.1667
2025-07-31 16:19:36 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.008380535989999771, 'fd_loss': 0.007653728127479553, 'vd_loss': 0.0122927725315094, 'confidence_penalty': 0.004853963386267424, 'total_loss': 0.020749196410179138}
2025-07-31 16:19:41 - pulsar_trainer - INFO - Epoch  24: Train Loss=0.0374, Val Loss=0.0162, Train Acc=99.04%, Val Acc=99.44%
2025-07-31 16:19:41 - pulsar_trainer - INFO - Validation F1: 99.4444, Precision: 99.4505, Recall: 99.4444
2025-07-31 16:19:41 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.0037541971541941166, 'fd_loss': 0.003708136733621359, 'vd_loss': 0.007165879011154175, 'confidence_penalty': 0.004629187285900116, 'total_loss': 0.012387216091156006}
2025-07-31 16:19:46 - pulsar_trainer - INFO - Epoch  25: Train Loss=0.0278, Val Loss=0.0153, Train Acc=99.40%, Val Acc=99.72%
2025-07-31 16:19:46 - pulsar_trainer - INFO - Validation F1: 99.7222, Precision: 99.7238, Recall: 99.7222
2025-07-31 16:19:47 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.0011194475227966905, 'fd_loss': 0.0011165249161422253, 'vd_loss': 0.0021440982818603516, 'confidence_penalty': 0.004888348281383514, 'total_loss': 0.007209287956357002}
2025-07-31 16:19:52 - pulsar_trainer - INFO - Epoch  26: Train Loss=0.0252, Val Loss=0.0278, Train Acc=99.28%, Val Acc=99.17%
2025-07-31 16:19:52 - pulsar_trainer - INFO - Validation F1: 99.1666, Precision: 99.1803, Recall: 99.1667
2025-07-31 16:19:52 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.029628250747919083, 'fd_loss': 0.01961441896855831, 'vd_loss': 0.03779461979866028, 'confidence_penalty': 0.0049195680767297745, 'total_loss': 0.05569341778755188}
2025-07-31 16:19:57 - pulsar_trainer - INFO - Epoch  27: Train Loss=0.0510, Val Loss=0.0362, Train Acc=98.86%, Val Acc=98.89%
2025-07-31 16:19:57 - pulsar_trainer - INFO - Validation F1: 98.8889, Precision: 98.8949, Recall: 98.8889
2025-07-31 16:19:57 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.13245129585266113, 'fd_loss': 0.031024571508169174, 'vd_loss': 0.061800748109817505, 'confidence_penalty': 0.004931692034006119, 'total_loss': 0.17143550515174866}
2025-07-31 16:20:02 - pulsar_trainer - INFO - Epoch  28: Train Loss=0.0481, Val Loss=0.0300, Train Acc=98.75%, Val Acc=99.17%
2025-07-31 16:20:02 - pulsar_trainer - INFO - Validation F1: 99.1666, Precision: 99.1803, Recall: 99.1667
2025-07-31 16:20:02 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.05842924118041992, 'fd_loss': 0.04617014527320862, 'vd_loss': 0.022473126649856567, 'confidence_penalty': 0.004230228718370199, 'total_loss': 0.09248648583889008}
2025-07-31 16:20:07 - pulsar_trainer - INFO - Epoch  29: Train Loss=0.0438, Val Loss=0.0129, Train Acc=98.57%, Val Acc=99.72%
2025-07-31 16:20:07 - pulsar_trainer - INFO - Validation F1: 99.7222, Precision: 99.7238, Recall: 99.7222
2025-07-31 16:20:07 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.0006174652953632176, 'fd_loss': 0.0006152107380330563, 'vd_loss': 0.000444263219833374, 'confidence_penalty': 0.004938479978591204, 'total_loss': 0.005996829830110073}
2025-07-31 16:20:12 - pulsar_trainer - INFO - Epoch  30: Train Loss=0.0442, Val Loss=0.0135, Train Acc=98.81%, Val Acc=99.72%
2025-07-31 16:20:12 - pulsar_trainer - INFO - Validation F1: 99.7222, Precision: 99.7238, Recall: 99.7222
2025-07-31 16:20:12 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.010584209114313126, 'fd_loss': 0.010305595584213734, 'vd_loss': 0.019506901502609253, 'confidence_penalty': 0.004441247787326574, 'total_loss': 0.026030326262116432}
2025-07-31 16:20:17 - pulsar_trainer - INFO - Epoch  31: Train Loss=0.0478, Val Loss=0.0116, Train Acc=98.86%, Val Acc=99.72%
2025-07-31 16:20:17 - pulsar_trainer - INFO - Validation F1: 99.7222, Precision: 99.7238, Recall: 99.7222
2025-07-31 16:20:17 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.00377151882275939, 'fd_loss': 0.003723315428942442, 'vd_loss': 0.006765127182006836, 'confidence_penalty': 0.00462766969576478, 'total_loss': 0.012290384620428085}
2025-07-31 16:20:22 - pulsar_trainer - INFO - Epoch  32: Train Loss=0.0477, Val Loss=0.0172, Train Acc=98.69%, Val Acc=99.44%
2025-07-31 16:20:22 - pulsar_trainer - INFO - Validation F1: 99.4444, Precision: 99.4444, Recall: 99.4444
2025-07-31 16:20:22 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.018720995634794235, 'fd_loss': 0.015856970101594925, 'vd_loss': 0.029451072216033936, 'confidence_penalty': 0.0045458064414560795, 'total_loss': 0.04003060981631279}
2025-07-31 16:20:27 - pulsar_trainer - INFO - Epoch  33: Train Loss=0.0354, Val Loss=0.0159, Train Acc=98.92%, Val Acc=99.72%
2025-07-31 16:20:27 - pulsar_trainer - INFO - Validation F1: 99.7222, Precision: 99.7238, Recall: 99.7222
2025-07-31 16:20:27 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.00014868014841340482, 'fd_loss': 0.00014861891395412385, 'vd_loss': 0.0002091526985168457, 'confidence_penalty': 0.0049851397052407265, 'total_loss': 0.005270875059068203}
2025-07-31 16:20:32 - pulsar_trainer - INFO - Epoch  34: Train Loss=0.0347, Val Loss=0.0253, Train Acc=99.16%, Val Acc=99.17%
2025-07-31 16:20:32 - pulsar_trainer - INFO - Validation F1: 99.1667, Precision: 99.1682, Recall: 99.1667
2025-07-31 16:20:33 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.0042496174573898315, 'fd_loss': 0.004117181524634361, 'vd_loss': 0.00781857967376709, 'confidence_penalty': 0.004835130181163549, 'total_loss': 0.01348891295492649}
2025-07-31 16:20:38 - pulsar_trainer - INFO - Epoch  35: Train Loss=0.0362, Val Loss=0.0173, Train Acc=99.04%, Val Acc=99.17%
2025-07-31 16:20:38 - pulsar_trainer - INFO - Validation F1: 99.1667, Precision: 99.1682, Recall: 99.1667
2025-07-31 16:20:38 - pulsar_trainer - INFO - Batch 0 - Enhanced loss components: {'ce_loss': 0.05604526400566101, 'fd_loss': 0.03963128477334976, 'vd_loss': 0.0632142722606659, 'confidence_penalty': 0.004483789671212435, 'total_loss': 0.09930897504091263}
2025-07-31 16:20:43 - pulsar_trainer - INFO - Epoch  36: Train Loss=0.0282, Val Loss=0.0303, Train Acc=99.16%, Val Acc=98.61%
2025-07-31 16:20:43 - pulsar_trainer - INFO - Validation F1: 98.6108, Precision: 98.6486, Recall: 98.6111
2025-07-31 16:20:43 - pulsar_trainer - INFO - 早停触发，在第 36 轮停止训练
2025-07-31 16:20:43 - pulsar_trainer - INFO - ============================================================
2025-07-31 16:20:43 - pulsar_trainer - INFO - ✅ 训练完成
2025-07-31 16:20:43 - pulsar_trainer - INFO - 🏆 最佳验证准确率: 100.0000
2025-07-31 16:20:43 - pulsar_trainer - INFO - ⏱️ 总训练时间: 232.74秒
2025-07-31 16:20:43 - pulsar_trainer - INFO - ============================================================
2025-07-31 16:20:43 - pulsar_trainer - INFO - 🔍 开始最终评估...
2025-07-31 16:20:43 - pulsar_trainer - INFO - 模型已加载: D:\pulsarSuanfa\outputs_TPP\models\best_model.pth
2025-07-31 16:21:07 - pulsar_trainer.utils.comprehensive_misclassification_analysis - INFO - 开始全面误分类分析...
2025-07-31 16:21:07 - pulsar_trainer.utils.comprehensive_misclassification_analysis - INFO - 综合误分类分析报告已生成: D:\pulsarSuanfa\outputs_TPP\results\comprehensive_misclassification_analysis\comprehensive_misclassification_report.txt
2025-07-31 16:21:07 - pulsar_trainer.utils.comprehensive_misclassification_analysis - INFO - 全面误分类分析完成。报告保存至: D:\pulsarSuanfa\outputs_TPP\results\comprehensive_misclassification_analysis
2025-07-31 16:21:07 - pulsar_trainer - INFO - 📊 评估结果:
2025-07-31 16:21:07 - pulsar_trainer - INFO -   - accuracy: 0.9972
2025-07-31 16:21:07 - pulsar_trainer - INFO -   - precision: 1.0000
2025-07-31 16:21:07 - pulsar_trainer - INFO -   - recall: 0.9944
2025-07-31 16:21:07 - pulsar_trainer - INFO -   - specificity: 1.0000
2025-07-31 16:21:07 - pulsar_trainer - INFO -   - f1_score: 0.9972
2025-07-31 16:21:07 - pulsar_trainer - INFO -   - false_positive_rate: 0.0000
2025-07-31 16:21:07 - pulsar_trainer - INFO - 📊 增强模块统计信息:
2025-07-31 16:21:07 - pulsar_trainer - INFO -   - 总前向传播次数: 4131
2025-07-31 16:21:07 - pulsar_trainer - INFO -   - FPP激活次数: 1377
2025-07-31 16:21:07 - pulsar_trainer - INFO -   - TPP激活次数: 1377
2025-07-31 16:21:07 - pulsar_trainer - INFO -   - 损失计算次数: 1113
2025-07-31 16:21:07 - pulsar_trainer - INFO -   - FPP激活率: 33.33%
2025-07-31 16:21:07 - pulsar_trainer - INFO -   - TPP激活率: 33.33%
2025-07-31 16:21:09 - pulsar_trainer - INFO - 所有结果已保存到: D:\pulsarSuanfa\outputs_TPP\results
2025-07-31 16:21:09 - pulsar_trainer - INFO - 可视化图表已保存到: D:\pulsarSuanfa\outputs_TPP\plots
