"""
common_utils.py - 增强模块的共享工具函数和依赖处理
为所有增强模块提供通用的工具函数和依赖管理

用途: 脉冲星分类系统增强模块的公共工具库
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import warnings
from typing import Optional, Tuple, Union, Dict, Any


# ============================================================================
# 权重初始化工具函数
# ============================================================================

def normal_init(module: nn.Module, mean: float = 0, std: float = 1, bias: float = 0):
    """
    标准正态分布初始化
    
    Args:
        module: 要初始化的模块
        mean: 均值
        std: 标准差
        bias: 偏置初始值
    """
    if hasattr(module, 'weight') and module.weight is not None:
        nn.init.normal_(module.weight, mean, std)
    if hasattr(module, 'bias') and module.bias is not None:
        nn.init.constant_(module.bias, bias)


def xavier_init(module: nn.Module, distribution: str = 'uniform'):
    """
    Xavier初始化
    
    Args:
        module: 要初始化的模块
        distribution: 分布类型 ('uniform' 或 'normal')
    """
    if hasattr(module, 'weight') and module.weight is not None:
        if distribution == 'uniform':
            nn.init.xavier_uniform_(module.weight)
        else:
            nn.init.xavier_normal_(module.weight)


def kaiming_init(module: nn.Module, mode: str = 'fan_out', nonlinearity: str = 'relu'):
    """
    Kaiming初始化
    
    Args:
        module: 要初始化的模块
        mode: 模式 ('fan_in' 或 'fan_out')
        nonlinearity: 非线性函数类型
    """
    if hasattr(module, 'weight') and module.weight is not None:
        nn.init.kaiming_normal_(module.weight, mode=mode, nonlinearity=nonlinearity)


# ============================================================================
# 频域处理工具函数
# ============================================================================

def hamming2D(M: int, N: int) -> np.ndarray:
    """
    生成二维Hamming窗
    
    Args:
        M: 窗口的行数
        N: 窗口的列数
        
    Returns:
        np.ndarray: 二维Hamming窗
    """
    hamming_x = np.hamming(M)
    hamming_y = np.hamming(N)
    hamming_2d = np.outer(hamming_x, hamming_y)
    return hamming_2d


def create_gaussian_kernel(size: int, sigma: float = 1.0) -> torch.Tensor:
    """
    创建高斯核
    
    Args:
        size: 核大小
        sigma: 标准差
        
    Returns:
        torch.Tensor: 高斯核
    """
    coords = torch.arange(size, dtype=torch.float32)
    coords -= size // 2
    
    g = torch.exp(-(coords ** 2) / (2 * sigma ** 2))
    g /= g.sum()
    
    # 创建2D高斯核
    g2d = g[:, None] * g[None, :]
    return g2d


def apply_frequency_filter(x: torch.Tensor, filter_type: str = 'low', kernel_size: int = 3) -> torch.Tensor:
    """
    应用频域滤波
    
    Args:
        x: 输入张量 [B, C, H, W]
        filter_type: 滤波类型 ('low', 'high', 'band')
        kernel_size: 滤波核大小
        
    Returns:
        torch.Tensor: 滤波后的张量
    """
    if filter_type == 'low':
        # 低通滤波：平均池化
        filtered = F.avg_pool2d(x, kernel_size=kernel_size, stride=1, padding=kernel_size//2)
    elif filter_type == 'high':
        # 高通滤波：原始 - 低通
        low_freq = F.avg_pool2d(x, kernel_size=kernel_size, stride=1, padding=kernel_size//2)
        filtered = x - low_freq
    else:
        # 带通滤波：高通 - 更高频
        low_freq = F.avg_pool2d(x, kernel_size=kernel_size, stride=1, padding=kernel_size//2)
        high_freq = x - low_freq
        very_high_freq = F.avg_pool2d(high_freq, kernel_size=kernel_size//2+1, stride=1, padding=kernel_size//4)
        filtered = high_freq - very_high_freq
    
    return filtered


# ============================================================================
# 张量操作工具函数
# ============================================================================

def safe_divide(numerator: torch.Tensor, denominator: torch.Tensor, eps: float = 1e-8) -> torch.Tensor:
    """
    安全除法，避免除零错误
    
    Args:
        numerator: 分子
        denominator: 分母
        eps: 小的常数，避免除零
        
    Returns:
        torch.Tensor: 除法结果
    """
    return numerator / (denominator + eps)


def normalize_tensor(x: torch.Tensor, dim: int = 1, eps: float = 1e-8) -> torch.Tensor:
    """
    张量归一化
    
    Args:
        x: 输入张量
        dim: 归一化维度
        eps: 小的常数
        
    Returns:
        torch.Tensor: 归一化后的张量
    """
    norm = torch.norm(x, dim=dim, keepdim=True)
    return x / (norm + eps)


def adaptive_pool2d(x: torch.Tensor, output_size: Union[int, Tuple[int, int]]) -> torch.Tensor:
    """
    自适应池化，支持不同的输出尺寸
    
    Args:
        x: 输入张量 [B, C, H, W]
        output_size: 输出尺寸
        
    Returns:
        torch.Tensor: 池化后的张量
    """
    if isinstance(output_size, int):
        output_size = (output_size, output_size)
    
    return F.adaptive_avg_pool2d(x, output_size)


# ============================================================================
# 设备和数据类型工具函数
# ============================================================================

def get_device(tensor_or_module: Union[torch.Tensor, nn.Module]) -> torch.device:
    """
    获取张量或模块的设备
    
    Args:
        tensor_or_module: 张量或模块
        
    Returns:
        torch.device: 设备
    """
    if isinstance(tensor_or_module, torch.Tensor):
        return tensor_or_module.device
    elif isinstance(tensor_or_module, nn.Module):
        return next(tensor_or_module.parameters()).device
    else:
        return torch.device('cpu')


def ensure_tensor(x: Union[torch.Tensor, np.ndarray, float, int], 
                 device: Optional[torch.device] = None,
                 dtype: Optional[torch.dtype] = None) -> torch.Tensor:
    """
    确保输入是张量
    
    Args:
        x: 输入数据
        device: 目标设备
        dtype: 目标数据类型
        
    Returns:
        torch.Tensor: 张量
    """
    if not isinstance(x, torch.Tensor):
        x = torch.tensor(x)
    
    if device is not None:
        x = x.to(device)
    
    if dtype is not None:
        x = x.to(dtype)
    
    return x


# ============================================================================
# 配置验证工具函数
# ============================================================================

def validate_tensor_shape(tensor: torch.Tensor, expected_dims: int, name: str = "tensor"):
    """
    验证张量形状
    
    Args:
        tensor: 输入张量
        expected_dims: 期望的维度数
        name: 张量名称（用于错误信息）
    """
    if len(tensor.shape) != expected_dims:
        raise ValueError(f"{name} must have {expected_dims} dimensions, got {len(tensor.shape)}")


def validate_positive_int(value: int, name: str = "value"):
    """
    验证正整数
    
    Args:
        value: 要验证的值
        name: 参数名称
    """
    if not isinstance(value, int) or value <= 0:
        raise ValueError(f"{name} must be a positive integer, got {value}")


def validate_range(value: float, min_val: float, max_val: float, name: str = "value"):
    """
    验证数值范围
    
    Args:
        value: 要验证的值
        min_val: 最小值
        max_val: 最大值
        name: 参数名称
    """
    if not (min_val <= value <= max_val):
        raise ValueError(f"{name} must be in range [{min_val}, {max_val}], got {value}")


# ============================================================================
# 性能监控工具函数
# ============================================================================

class PerformanceMonitor:
    """
    性能监控器
    
    用于监控模块的计算时间和内存使用
    """
    
    def __init__(self):
        self.timings = {}
        self.memory_usage = {}
    
    def start_timer(self, name: str):
        """开始计时"""
        if torch.cuda.is_available():
            torch.cuda.synchronize()
        self.timings[name] = torch.cuda.Event(enable_timing=True) if torch.cuda.is_available() else None
        if self.timings[name]:
            self.timings[name].record()
    
    def end_timer(self, name: str) -> float:
        """结束计时并返回时间（毫秒）"""
        if torch.cuda.is_available():
            torch.cuda.synchronize()
            if name in self.timings and self.timings[name]:
                end_event = torch.cuda.Event(enable_timing=True)
                end_event.record()
                torch.cuda.synchronize()
                elapsed_time = self.timings[name].elapsed_time(end_event)
                return elapsed_time
        return 0.0
    
    def get_memory_usage(self) -> Dict[str, float]:
        """获取内存使用情况"""
        if torch.cuda.is_available():
            return {
                'allocated': torch.cuda.memory_allocated() / 1024**2,  # MB
                'cached': torch.cuda.memory_reserved() / 1024**2,      # MB
            }
        return {'allocated': 0.0, 'cached': 0.0}


# ============================================================================
# 错误处理和警告
# ============================================================================

def handle_optional_dependency(module_name: str, error_msg: str = None):
    """
    处理可选依赖的导入错误
    
    Args:
        module_name: 模块名称
        error_msg: 自定义错误信息
    """
    if error_msg is None:
        error_msg = f"Optional dependency '{module_name}' not found. Some features may be disabled."
    
    warnings.warn(error_msg, UserWarning)


def deprecated_warning(old_name: str, new_name: str):
    """
    发出弃用警告
    
    Args:
        old_name: 旧的名称
        new_name: 新的名称
    """
    warnings.warn(f"'{old_name}' is deprecated, use '{new_name}' instead.", DeprecationWarning)


# 测试代码
if __name__ == '__main__':
    print("Testing common utilities...")
    
    # 测试张量操作
    x = torch.randn(2, 64, 32, 32)
    print(f"Input shape: {x.shape}")
    
    # 测试频域滤波
    low_freq = apply_frequency_filter(x, 'low', 3)
    high_freq = apply_frequency_filter(x, 'high', 3)
    print(f"Low freq shape: {low_freq.shape}")
    print(f"High freq shape: {high_freq.shape}")
    
    # 测试设备检测
    device = get_device(x)
    print(f"Device: {device}")
    
    # 测试性能监控
    monitor = PerformanceMonitor()
    memory_info = monitor.get_memory_usage()
    print(f"Memory usage: {memory_info}")
    
    print("Common utilities test completed successfully!")
