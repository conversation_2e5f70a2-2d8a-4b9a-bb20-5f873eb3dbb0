#!/usr/bin/env python3
"""
Resplit HTRU dataset with stratified sampling to ensure fair distribution.
"""

import numpy as np
import shutil
from pathlib import Path
from sklearn.model_selection import train_test_split
from collections import defaultdict
import json
import random

class DatasetResplitter:
    def __init__(self, data_root: str, target_splits: dict, random_seed: int = 42):
        """
        Initialize dataset resplitter.
        
        Args:
            data_root: Root directory of dataset
            target_splits: Target split sizes {'train': 1674, 'validation': 360, 'test': 358}
            random_seed: Random seed for reproducibility
        """
        self.data_root = Path(data_root)
        self.target_splits = target_splits
        self.random_seed = random_seed
        
        # Set random seeds
        random.seed(random_seed)
        np.random.seed(random_seed)
    
    def collect_all_files(self, modality: str):
        """Collect all files from all splits for a modality."""
        modality_path = self.data_root / modality
        
        all_files = {
            'positive': [],
            'negative': []
        }
        
        for split in ['train', 'validation', 'test']:
            split_path = modality_path / split
            if split_path.exists():
                # Collect positive files
                positive_files = list(split_path.glob('*positive*.npy'))
                all_files['positive'].extend(positive_files)
                
                # Collect negative files
                negative_files = list(split_path.glob('*negative*.npy'))
                all_files['negative'].extend(negative_files)
        
        return all_files
    
    def stratified_split(self, files_dict: dict):
        """Perform stratified split maintaining 1:1 ratio."""
        positive_files = files_dict['positive']
        negative_files = files_dict['negative']
        
        print(f"总正样本: {len(positive_files)}")
        print(f"总负样本: {len(negative_files)}")
        
        # Ensure we have enough samples
        min_samples = min(len(positive_files), len(negative_files))
        total_target = sum(self.target_splits.values())
        
        if min_samples * 2 < total_target:
            raise ValueError(f"Not enough samples. Need {total_target}, have {min_samples * 2}")
        
        # Randomly sample to ensure balance
        positive_files = random.sample(positive_files, min_samples)
        negative_files = random.sample(negative_files, min_samples)
        
        # Calculate split sizes (half positive, half negative for each split)
        split_sizes = {}
        for split_name, total_size in self.target_splits.items():
            if total_size % 2 != 0:
                raise ValueError(f"Split size must be even for 1:1 ratio. Got {total_size} for {split_name}")
            split_sizes[split_name] = total_size // 2
        
        # Perform stratified split
        splits = {}
        
        # First split: train vs (validation + test)
        train_size = split_sizes['train']
        remaining_size = split_sizes['validation'] + split_sizes['test']
        
        pos_train, pos_temp = train_test_split(
            positive_files, 
            train_size=train_size, 
            test_size=remaining_size,
            random_state=self.random_seed
        )
        
        neg_train, neg_temp = train_test_split(
            negative_files,
            train_size=train_size,
            test_size=remaining_size,
            random_state=self.random_seed
        )
        
        # Second split: validation vs test
        val_size = split_sizes['validation']
        test_size = split_sizes['test']
        
        pos_val, pos_test = train_test_split(
            pos_temp,
            train_size=val_size,
            test_size=test_size,
            random_state=self.random_seed
        )
        
        neg_val, neg_test = train_test_split(
            neg_temp,
            train_size=val_size,
            test_size=test_size,
            random_state=self.random_seed
        )
        
        # Combine positive and negative for each split
        splits['train'] = {
            'positive': pos_train,
            'negative': neg_train
        }
        splits['validation'] = {
            'positive': pos_val,
            'negative': neg_val
        }
        splits['test'] = {
            'positive': pos_test,
            'negative': neg_test
        }
        
        return splits
    
    def copy_files_to_splits(self, modality: str, splits: dict):
        """Copy files to new split directories."""
        modality_path = self.data_root / modality
        
        # Create temporary directory for new splits
        temp_path = self.data_root / f"{modality}_new"
        if temp_path.exists():
            shutil.rmtree(temp_path)
        
        for split_name, files_dict in splits.items():
            split_path = temp_path / split_name
            split_path.mkdir(parents=True, exist_ok=True)
            
            # Copy positive files
            for i, file_path in enumerate(files_dict['positive']):
                new_name = f"{split_name}_positive_{i:04d}.npy"
                shutil.copy2(file_path, split_path / new_name)
            
            # Copy negative files
            for i, file_path in enumerate(files_dict['negative']):
                new_name = f"{split_name}_negative_{i:04d}.npy"
                shutil.copy2(file_path, split_path / new_name)
        
        # Replace old splits with new ones
        backup_path = self.data_root / f"{modality}_old"
        if backup_path.exists():
            shutil.rmtree(backup_path)
        
        shutil.move(modality_path, backup_path)
        shutil.move(temp_path, modality_path)
        
        print(f"✅ {modality} 数据集重新划分完成")
    
    def resplit_modality(self, modality: str):
        """Resplit a single modality."""
        print(f"\n🔄 重新划分 {modality} 数据集...")
        
        # Collect all files
        all_files = self.collect_all_files(modality)
        
        # Perform stratified split
        splits = self.stratified_split(all_files)
        
        # Verify split sizes
        for split_name, files_dict in splits.items():
            total_size = len(files_dict['positive']) + len(files_dict['negative'])
            expected_size = self.target_splits[split_name]
            
            print(f"{split_name}: {len(files_dict['positive'])} positive + {len(files_dict['negative'])} negative = {total_size} (expected: {expected_size})")
            
            if total_size != expected_size:
                raise ValueError(f"Split size mismatch for {split_name}: got {total_size}, expected {expected_size}")
        
        # Copy files to new splits
        self.copy_files_to_splits(modality, splits)
        
        return splits
    
    def resplit_all(self):
        """Resplit all modalities."""
        print("🚀 开始数据集重新划分...")
        print(f"目标划分: {self.target_splits}")
        print(f"随机种子: {self.random_seed}")
        
        results = {}
        
        for modality in ['FPP', 'TPP']:
            try:
                splits = self.resplit_modality(modality)
                results[modality] = {
                    'success': True,
                    'splits': {
                        split_name: {
                            'positive': len(files_dict['positive']),
                            'negative': len(files_dict['negative']),
                            'total': len(files_dict['positive']) + len(files_dict['negative'])
                        }
                        for split_name, files_dict in splits.items()
                    }
                }
            except Exception as e:
                results[modality] = {
                    'success': False,
                    'error': str(e)
                }
                print(f"❌ {modality} 重新划分失败: {e}")
        
        return results

if __name__ == "__main__":
    data_root = "D:/pulsarSuanfa/datasets/HTRU"
    target_splits = {
        'train': 1674,
        'validation': 360,
        'test': 358
    }
    
    resplitter = DatasetResplitter(data_root, target_splits)
    results = resplitter.resplit_all()
    
    # Save results
    with open("resplit_results.json", "w") as f:
        json.dump(results, f, indent=2)
    
    print("\n📊 重新划分结果:")
    print(json.dumps(results, indent=2))
